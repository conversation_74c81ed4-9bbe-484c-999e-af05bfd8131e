package com.pcitc.yczd.cata.service.impl;

import com.pcitc.yczd.cata.service.*;
import com.pcitc.yczd.cata.utils.ExamineUtil;
import com.pcitc.yczd.cata.utils.YczdCataUtils;
import com.pcitc.yczd.cata.vo.resp.ExamineSocraparamVo;
import com.pcitc.yczd.cata.vo.resp.TablePageConfigVO;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: yczd-springboot
 * @description: ExamineServiceIpml
 * @author: pengchong.li
 * @createTime: 2024-05-24 14:38
 **/
@Service
@Slf4j
public class ExamineServiceImpl implements IExamineService {


    @Resource
    private IExamineStandDownService examinestanddownservice;
    @Resource
    private IExamineConsumptionService examineconsumptionservice;
    @Resource
    private IExamineTotalCataService examinetotalcataservice;
    @Resource
    private IExamineCataService examinecataservice;
    @Resource
    private IExamineSmokePowerCataService examinesmokepowercataservice;
    @Resource
    private IExamineDrygasCataService examinedrygascataservice;
    @Resource
    private IExamineDrycokeconCataService examinedrycokeconcataservice;
    @Resource
    private IExamineGasronCataService examinegasroncataservice;
    @Resource
    private IExamineGasDieselCataService examinegasdieselcataservice;
    @Autowired
    private ITablePageConfigService tablepageconfigservice;

    public Map<String, List<TablePageConfigVO>> getExaminelist(String[] group, String pageencode, List<TablePageConfigVO> list, String starttime,
                                                               String endtime) {
        List dataList = new ArrayList();
        Map<String, List<TablePageConfigVO>> newmapDevice = new Hashtable();
        try {
            List<String> dwlist =
                    list.stream().map(TablePageConfigVO::getDwcode).distinct().collect(Collectors.toList());
            Map<String, Map<String, List<String>>> idc_examine_value = new Hashtable<>();


            String IdKey = "";
            String device_id, items_id;
            Map<String, List<TablePageConfigVO>> mapDevice = new Hashtable();

            Map<String, List<String>> IDC_examineItem = new Hashtable<>();
            for (int i = 0; i < dwlist.size(); i++) {
                //if(i==1) {
                final String dwcode = dwlist.get(i);
                List<TablePageConfigVO> listdwcode =
                        list.stream().filter(ivo -> ivo.getDwcode().equals(dwcode)).collect(Collectors.toList());
                IdKey = dwlist.get(i);
                for (int j = 0; j < listdwcode.size(); j++) {
                    TablePageConfigVO vo = new TablePageConfigVO();
                    vo.setMax(listdwcode.get(j).getMax());
                    vo.setMin(listdwcode.get(j).getMin());
                    vo.setEtc(listdwcode.get(j).getEtc());
                    if (listdwcode.get(j).getClasstype().indexOf("ExamineStandDown") != -1) {
                        List<String> douList = examinestanddownservice.GetCalculateValue(listdwcode.get(j).getEtc()
                                , starttime, endtime);
                        IDC_examineItem.put(listdwcode.get(j).getDesc(), douList);
                    }
                    if (listdwcode.get(j).getClasstype().indexOf("ExamineConsumption") != -1) {
                        List<String> douList =
                                examineconsumptionservice.GetCalculateValue(listdwcode.get(j).getEtc()
                                        , starttime, endtime);
                        IDC_examineItem.put(listdwcode.get(j).getDesc(), douList);
                    }
                    if (listdwcode.get(j).getClasstype().indexOf("ExamineTotal_Cata") != -1) {
                        List<String> douList = examinetotalcataservice.getCalculateValue(listdwcode.get(j).getEtc()
                                , starttime, endtime);
                        IDC_examineItem.put(listdwcode.get(j).getDesc(), douList);
                    }
                    if (listdwcode.get(j).getClasstype().indexOf("Examine_Cata") != -1) {
                        List<String> douList = examinecataservice.GetCalculateValue(listdwcode.get(j).getEtc()
                                , starttime, endtime);
                        IDC_examineItem.put(listdwcode.get(j).getDesc(), douList);
                    }
                    if (listdwcode.get(j).getClasstype().indexOf("ExamineSmokePower_Cata") != -1) {
                        List<String> douList = examinesmokepowercataservice.GetCalculateValue(listdwcode.get(j).getEtc()
                                , starttime, endtime);
                        IDC_examineItem.put(listdwcode.get(j).getDesc(), douList);
                    }
                    if (listdwcode.get(j).getClasstype().indexOf("ExamineDrygas_Cata") != -1) {
                        List<String> douList = examinedrygascataservice.GetCalculateValue(listdwcode.get(j).getEtc()
                                , starttime, endtime);
                        IDC_examineItem.put(listdwcode.get(j).getDesc(), douList);
                    }
                    if (listdwcode.get(j).getClasstype().indexOf("ExamineDrycokecon_Cata") != -1) {
                        List<String> douList = examinedrycokeconcataservice.GetCalculateValue(listdwcode.get(j).getEtc()
                                , starttime, endtime);
                        IDC_examineItem.put(listdwcode.get(j).getDesc(), douList);
                    }
                    if (listdwcode.get(j).getClasstype().indexOf("ExamineGasron_Cata") != -1) {
                        List<String> douList = examinegasroncataservice.GetCalculateValue(listdwcode.get(j).getEtc()
                                , starttime, endtime);
                        IDC_examineItem.put(listdwcode.get(j).getDesc(), douList);
                    }
                    if (listdwcode.get(j).getClasstype().indexOf("ExamineGasDiesel_Cata") != -1) {
                        List<String> douList = examinegasdieselcataservice.GetCalculateValue(listdwcode.get(j).getEtc()
                                , starttime, endtime);
                        IDC_examineItem.put(listdwcode.get(j).getDesc(), douList);
                    }

                }
                idc_examine_value.put(dwlist.get(i), IDC_examineItem);
                //}

            }
            //region===注释各种list
            /*//非计划停工次数
            final String fjhtg = group[0];
            List<TablePageConfigVO> fjhtglist =
                    list.stream().filter(ivo -> ivo.getDesc().equals(fjhtg)).collect(Collectors.toList());
            //能耗/10+损失
            final String nhss = group[1];
            List<TablePageConfigVO> nhsslist = list.stream().filter(ivo -> ivo.getDesc().equals(nhss)).collect(Collectors.toList());
            //长周期运行天数
            final String czqyxts = group[2];
            List<TablePageConfigVO> czqyxtslist = list.stream().filter(ivo -> ivo.getDesc().equals(czqyxts)).collect(Collectors.toList());
            //[汽油收率×1.2+柴油收率+液化气收率]×(原料密度/1000)×{100/(100-原料残炭×0.8)}+掺渣比例/20
            final String qyslcyls = group[3];
            List<TablePageConfigVO> qyslcylslist = list.stream().filter(ivo -> ivo.getDesc().equals(qyslcyls)).collect(Collectors.toList());
            //烟机累计回收功率/主风机累计耗功
            final String yjljhsl = group[4];
            List<TablePageConfigVO> yjljhsllist = list.stream().filter(ivo -> ivo.getDesc().equals(yjljhsl)).collect(Collectors.toList());
            //干气C3+含量
            final String gqhl = group[5];
            List<TablePageConfigVO> gqhllist = list.stream().filter(ivo -> ivo.getDesc().equals(gqhl)).collect(Collectors.toList());
            //（干气+生焦）/转化率
            final String gqsj = group[6];
            List<TablePageConfigVO> gqsjlist = list.stream().filter(ivo -> ivo.getDesc().equals(gqsj)).collect(Collectors.toList());
            //汽油收率×RON
            final String qysl = group[7];
            List<TablePageConfigVO> qysllist = list.stream().filter(ivo -> ivo.getDesc().equals(qysl)).collect(Collectors.toList());
            //汽油和柴油重叠度
            final String qcycdu = group[8];
            List<TablePageConfigVO> qcycdulist = list.stream().filter(ivo -> ivo.getDesc().equals(qcycdu)).collect(Collectors.toList());
            Map<String, List<Double>> IDC_examineItem = new Hashtable<>();
            //非计划停工次数
            for (int i = 0; i < fjhtglist.size(); i++) {
                TablePageConfigVO vo = new TablePageConfigVO();
                vo.setMax(fjhtglist.get(i).getMax());
                vo.setEtc(fjhtglist.get(i).getEtc());
                List<Double> douList = examinestanddownservice.GetCalculateValue(fjhtglist.get(i).getEtc(), starttime, endtime);
                IDC_examineItem.put(fjhtglist.get(i).getDesc(), douList);
            }
            idc_examine_value.put(fjhtglist.get(0).getDwcode(), IDC_examineItem);
            //能耗/10+损失
            for (int i = 0; i < nhsslist.size(); i++) {
                TablePageConfigVO vo = new TablePageConfigVO();
                vo.setMax(nhsslist.get(i).getMax());
                vo.setEtc(nhsslist.get(i).getEtc());
                List<Double> douList = examineconsumptionservice.GetCalculateValue(nhsslist.get(i).getEtc(), starttime, endtime);
                IDC_examineItem.put(nhsslist.get(i).getDesc(), douList);
            }
            idc_examine_value.put(nhsslist.get(0).getDwcode(), IDC_examineItem);
            //长周期运行天数
            for (int i = 0; i < czqyxtslist.size(); i++) {
                TablePageConfigVO vo = new TablePageConfigVO();
                vo.setMax(czqyxtslist.get(i).getMax());
                vo.setEtc(czqyxtslist.get(i).getEtc());
                List<Double> douList = examinetotalcataservice.GetCalculateValue(czqyxtslist.get(i).getEtc(), starttime
                        , endtime);
                IDC_examineItem.put(czqyxtslist.get(i).getDesc(), douList);
            }
            idc_examine_value.put(czqyxtslist.get(0).getDwcode(), IDC_examineItem);
            //[汽油收率×1.2+柴油收率+液化气收率]×(原料密度/1000)×{100/(100-原料残炭×0.8)}+掺渣比例/20
            for (int i = 0; i < qyslcylslist.size(); i++) {
                TablePageConfigVO vo = new TablePageConfigVO();
                vo.setMax(qyslcylslist.get(i).getMax());
                vo.setEtc(qyslcylslist.get(i).getEtc());
                List<Double> douList = examinecataservice.GetCalculateValue(qyslcylslist.get(i).getEtc(), starttime, endtime);
                IDC_examineItem.put(qyslcylslist.get(i).getDesc(), douList);
            }
            idc_examine_value.put(qyslcylslist.get(0).getDwcode(), IDC_examineItem);
            //烟机累计回收功率/主风机累计耗功
            for (int i = 0; i < yjljhsllist.size(); i++) {
                TablePageConfigVO vo = new TablePageConfigVO();
                vo.setMax(yjljhsllist.get(i).getMax());
                vo.setEtc(yjljhsllist.get(i).getEtc());
                List<Double> douList = examinesmokepowercataservice.GetCalculateValue(yjljhsllist.get(i).getEtc(), starttime, endtime);
                IDC_examineItem.put(yjljhsllist.get(i).getDesc(), douList);
            }
            idc_examine_value.put(yjljhsllist.get(0).getDwcode(), IDC_examineItem);
            //干气C3+含量
            for (int i = 0; i < gqhllist.size(); i++) {
                TablePageConfigVO vo = new TablePageConfigVO();
                vo.setMax(gqhllist.get(i).getMax());
                vo.setEtc(gqhllist.get(i).getEtc());
                List<Double> douList = examinedrygascataservice.GetCalculateValue(gqhllist.get(i).getEtc(), starttime, endtime);
                IDC_examineItem.put(gqhllist.get(i).getDesc(), douList);
            }
            idc_examine_value.put(gqhllist.get(0).getDwcode(), IDC_examineItem);
            //（干气+生焦）/转化率
            for (int i = 0; i < gqsjlist.size(); i++) {
                TablePageConfigVO vo = new TablePageConfigVO();
                vo.setMax(gqsjlist.get(i).getMax());
                vo.setEtc(gqsjlist.get(i).getEtc());
                List<Double> douList = examinedrycokeconcataservice.GetCalculateValue(gqsjlist.get(i).getEtc(), starttime, endtime);
                IDC_examineItem.put(gqsjlist.get(i).getDesc(), douList);
            }
            idc_examine_value.put(gqsjlist.get(0).getDwcode(), IDC_examineItem);
            //汽油收率×RON
            for (int i = 0; i < qysllist.size(); i++) {
                TablePageConfigVO vo = new TablePageConfigVO();
                vo.setMax(qysllist.get(i).getMax());
                vo.setEtc(qysllist.get(i).getEtc());
                List<Double> douList = examinegasroncataservice.GetCalculateValue(qysllist.get(i).getEtc(), starttime, endtime);
                IDC_examineItem.put(qysllist.get(i).getDesc(), douList);
            }
            idc_examine_value.put(qysllist.get(0).getDwcode(), IDC_examineItem);
            //汽油和柴油重叠度
            for (int i = 0; i < qcycdulist.size(); i++) {
                TablePageConfigVO vo = new TablePageConfigVO();
                vo.setMax(qcycdulist.get(i).getMax());
                vo.setEtc(qcycdulist.get(i).getEtc());
                List<Double> douList = examinegasdieselcataservice.GetCalculateValue(qcycdulist.get(i).getEtc(), starttime, endtime);
                IDC_examineItem.put(qcycdulist.get(i).getDesc(), douList);
            }
            idc_examine_value.put(qcycdulist.get(0).getDwcode(), IDC_examineItem);*/
//endregion
            // 从已存在的数据中找出各个装置的停工时常，判断出哪些企业不参与考核

            List<String> stopname = new ArrayList();//存放不显示的要过滤掉的企业
            List<TablePageConfigVO> fjhtglist =
                    list.stream().filter(ivo -> ivo.getDesc().equals("非计划停工次数")).collect(Collectors.toList());
            for (int i = 0; i < idc_examine_value.size(); i++) {
                for (int j = 0; j < fjhtglist.size(); j++) {
                    if (idc_examine_value.get(fjhtglist.get(j).getDwcode()) != null) {
                        List<String> stopL = idc_examine_value.get(fjhtglist.get(j).getDwcode()).get(group[0]);
                        if (Double.parseDouble(stopL.get(2).split("\\,")[2]) > 90)
                            stopname.add(idc_examine_value.get(fjhtglist.get(j).getDwcode()).get(group[0]).toString());
                    }
                }
            }
            Map<String, Double> IDC1 = new HashMap<>();
            Map<String, Double> IDC2 = new HashMap<>();
            Map<String, Double> IDC3 = new HashMap<>();
            Map<String, Double> IDC4 = new HashMap<>();
            Map<String, Double> IDC5 = new HashMap<>();
            Map<String, Double> IDC6 = new HashMap<>();
            Map<String, Double> IDC7 = new HashMap<>();
            for (int i = 0; i < idc_examine_value.size(); i++) {
                for (int m = 0; m < dwlist.size(); m++) {

                    if (idc_examine_value.get(dwlist.get(m)) != null) {
                        for (Map.Entry<String, Map<String, List<String>>> item : idc_examine_value.entrySet()
                        ) {
                            double LI1 = 0;
                            double LI2 = 0;
                            double LI3 = 0;
                            double LI4 = 0;
                            double LI5 = 0;
                            double LI6 = 0;
                            double LI7 = 0;
                            for (Map.Entry<String, List<String>> ite : idc_examine_value.get(dwlist.get(m)).entrySet()) {
                                String keys = ite.getKey();
                                switch (keys) {
                                    case "能耗/10+损失":
                                        LI1 = Double.parseDouble(ite.getValue().get(0).split("\\,")[2]);
                                        break;
                                    case "[汽油收率×1.2+柴油收率+液化气收率]×(原料密度/1000)×{100/(100-原料残炭×0.8)}+掺渣比例/20":
                                        LI2 = Double.parseDouble(ite.getValue().get(0).split("\\,")[2]);
                                        break;
                                    case "烟机累计回收功率/主风机累计耗功":
                                        LI3 = Double.parseDouble(ite.getValue().get(0).split("\\,")[2]);
                                        break;
                                    case "干气C3+含量":
                                        LI4 = Double.parseDouble(ite.getValue().get(0).split("\\,")[2]);
                                        break;
                                    case "（干气+生焦）/转化率":
                                        LI5 = Double.parseDouble(ite.getValue().get(0).split("\\,")[2]);
                                        break;
                                    case "汽油收率×RON":
                                        LI6 = Double.parseDouble(ite.getValue().get(0).split("\\,")[2]);
                                        break;
                                    case "汽油和柴油重叠度":
                                        LI7 = Double.parseDouble(ite.getValue().get(0).split("\\,")[2]);
                                        break;
                                    default:
                                        break;
                                }
                            }
                            IDC1.put(item.getKey(), getXsValue(LI1, 2));
                            IDC2.put(item.getKey(), getXsValue(LI2, 2));
                            IDC3.put(item.getKey(), getXsValue(LI3, 2));
                            IDC4.put(item.getKey(), getXsValue(LI4, 2));
                            IDC5.put(item.getKey(), getXsValue(LI5, 2));
                            IDC6.put(item.getKey(), getXsValue(LI6, 2));
                            IDC7.put(item.getKey(), getXsValue(LI7, 2));
                        }
                    }
                }
                examineconsumptionservice.GetScore(IDC1);
                examinecataservice.GetScore(IDC2);
                examinesmokepowercataservice.GetScore(IDC3);
                examinedrygascataservice.GetScore(IDC4);
                examinedrycokeconcataservice.GetScore(IDC5);
                examinegasroncataservice.GetScore(IDC6);
                examinegasdieselcataservice.GetScore(IDC7);
                List<TablePageConfigVO> listtablepage =
                        tablepageconfigservice.GetTablePageConfigList("CMMain");
                List<String> dwlistdesc =
                        list.stream().map(TablePageConfigVO::getDesc).distinct().collect(Collectors.toList());
                for (int n = 0; n < dwlistdesc.size(); n++) {
                    List<TablePageConfigVO> tablelistdate = new ArrayList<>();
                    final String dwcode = dwlistdesc.get(n);
                    List<TablePageConfigVO> listdwcode =
                            list.stream().filter(ivo -> ivo.getDesc().equals(dwcode)).collect(Collectors.toList());
                    IdKey = dwlist.get(n);
                    if (!stopname.contains(IdKey)) {
                        for (int m = 0; m < listdwcode.size(); m++) {
                            TablePageConfigVO vo = new TablePageConfigVO();
                            vo.setDevicename(DeviceNameTrans(listtablepage, listdwcode.get(m).getDwcode()));
                            vo.setMax(listdwcode.get(m).getMax());
                            vo.setMin(listdwcode.get(m).getMin());
                            vo.setEtc(listdwcode.get(m).getEtc());
                        /*if(listdwcode.get(m).getDesc().equals(group[0])){
                            List<Double> douList =examinestanddownservice.GetCalculateValue(listdwcode.get(m).getEtc()
                                    , starttime, endtime);
                            IDC_examineItem.put(listdwcode.get(m).getDesc(), douList);
                        }*/
                            if (listdwcode.get(m).getDesc().equals(group[0])) {
                                vo.setScore(examineconsumptionservice.GetScore(idc_examine_value, IdKey,
                                        listdwcode.get(m).getDesc(), stopname,
                                        Double.parseDouble(listdwcode.get(m).getMax())));
                            }
                            if (listdwcode.get(m).getDesc().equals(group[1])) {
                                vo.setScore(examineconsumptionservice.GetSocre(IDC1, idc_examine_value, IdKey,
                                        listdwcode.get(m).getDesc()));
                            }
                            if (listdwcode.get(m).getDesc().equals(group[2])) {
                                vo.setScore(examinetotalcataservice.getSocre(IDC1, idc_examine_value, IdKey,
                                        listdwcode.get(m).getDesc()));
                            }
                            if (listdwcode.get(m).getDesc().equals(group[3])) {
                                vo.setScore(examinecataservice.GetSocre(IDC1, idc_examine_value, IdKey,
                                        listdwcode.get(m).getDesc()));
                            }
                            if (listdwcode.get(m).getDesc().equals(group[4])) {
                                vo.setScore(examinesmokepowercataservice.GetSocre(IDC1, idc_examine_value, IdKey,
                                        listdwcode.get(m).getDesc()));
                            }
                            if (listdwcode.get(m).getDesc().equals(group[5])) {
                                vo.setScore(examinedrygascataservice.GetSocre(IDC1, idc_examine_value, IdKey,
                                        listdwcode.get(m).getDesc()));
                            }
                            if (listdwcode.get(m).getDesc().equals(group[6])) {
                                vo.setScore(examinedrycokeconcataservice.GetSocre(IDC1, idc_examine_value, IdKey,
                                        listdwcode.get(m).getDesc()));
                            }
                            if (listdwcode.get(m).getDesc().equals(group[7])) {
                                vo.setScore(examinegasroncataservice.GetSocre(IDC1, idc_examine_value, IdKey,
                                        listdwcode.get(m).getDesc()));
                            }
                            if (listdwcode.get(m).getDesc().equals(group[8])) {
                                vo.setScore(examinegasdieselcataservice.GetSocre(IDC1, idc_examine_value, IdKey,
                                        listdwcode.get(m).getDesc()));
                            }
                            /*ExamineUtil.RegisterExam(listdwcode.get(m).getDesc(),vo);*/
                            tablelistdate.add(vo);

                        }

                        mapDevice.put(dwlistdesc.get(n), tablelistdate);
                    } else {
                        log.debug("企业" + IdKey + "由于停工时常超过90天，不计入考核。");
                    }

                }
                //为提高第一张图的显示速度，将数据处理工作放在了buffer刷新中实现
                //其余的数据处理工作放在叶面中实现
                List value = new ArrayList();
                List key = new ArrayList();
                for (Map.Entry<String, List<TablePageConfigVO>> item : mapDevice.entrySet()
                ) {

                    key.add(item.getKey());
                    value.add(item.getValue());
                }

            /*dataList.clear();
            dataList.add(key);
            dataList.add(value);
            dataList.add(mapDevice);*/
                newmapDevice = mapDevice;
                log.debug("===========================装置考核相关内容成功缓存=======================");
                //第三列存放原始的未加工的类
                return newmapDevice;
            }
        } catch (Exception e) {
            log.error("=====================刷新装置考核得分时报错==============原因：");
            log.error(e.getMessage());
            // //return false;
            /* throw new Exception(e.getMessage() + "堆栈信息：" + e.getStackTrace());*/
        }
        return newmapDevice;
    }


    public List getExaminelistnew() {
        List<TablePageConfigVO> list =
                tablepageconfigservice.GetTablePageConfigList("ExamineConfig").stream().filter(ivo -> !ivo.getMax().equals("")).collect(Collectors.toList());
        List dataList = new ArrayList();
        String starttime = "";
        String endtime = "";
        Map<String, List<TablePageConfigVO>> newmapDevice = new Hashtable();
        try {
            List<String> dwlist =
                    list.stream().map(TablePageConfigVO::getDwcode).distinct().collect(Collectors.toList());
            Map<String, Map<String, List<String>>> idc_examine_value = new Hashtable<>();


            String IdKey = "";
            String device_id, items_id;
            Map<String, List<TablePageConfigVO>> mapDevice = new Hashtable();

            Map<String, List<String>> IDC_examineItem = new Hashtable<>();
            for (int i = 0; i < dwlist.size(); i++) {
                //if(i==41||i==42||i==43) {
                final String dwcode = dwlist.get(i);
                List<TablePageConfigVO> listdwcode =
                        list.stream().filter(ivo -> ivo.getDwcode().equals(dwcode)).collect(Collectors.toList());
                IdKey = dwlist.get(i);
                for (int j = 0; j < listdwcode.size(); j++) {
                    TablePageConfigVO vo = new TablePageConfigVO();
                    vo.setMax(listdwcode.get(j).getMax());
                    vo.setMin(listdwcode.get(j).getMin());
                    vo.setEtc(listdwcode.get(j).getEtc());
                    if (listdwcode.get(j).getClasstype().indexOf("ExamineStandDown") != -1) {
                        List<String> douList = examinestanddownservice.GetCalculateValue(listdwcode.get(j).getEtc()
                                , starttime, endtime);
                        IDC_examineItem.put(listdwcode.get(j).getDesc(), douList);
                    }
                    if (listdwcode.get(j).getClasstype().indexOf("ExamineConsumption") != -1) {
                        List<String> douList =
                                examineconsumptionservice.GetCalculateValue(listdwcode.get(j).getEtc()
                                        , starttime, endtime);
                        IDC_examineItem.put(listdwcode.get(j).getDesc(), douList);
                    }
                    if (listdwcode.get(j).getClasstype().indexOf("ExamineTotal_Cata") != -1) {
                        List<String> douList = examinetotalcataservice.getCalculateValue(listdwcode.get(j).getEtc()
                                , starttime, endtime);
                        IDC_examineItem.put(listdwcode.get(j).getDesc(), douList);
                    }
                    if (listdwcode.get(j).getClasstype().indexOf("Examine_Cata") != -1) {
                        List<String> douList = examinecataservice.GetCalculateValue(listdwcode.get(j).getEtc()
                                , starttime, endtime);
                        IDC_examineItem.put(listdwcode.get(j).getDesc(), douList);
                    }
                    if (listdwcode.get(j).getClasstype().indexOf("ExamineSmokePower_Cata") != -1) {
                        List<String> douList = examinesmokepowercataservice.GetCalculateValue(listdwcode.get(j).getEtc()
                                , starttime, endtime);
                        IDC_examineItem.put(listdwcode.get(j).getDesc(), douList);
                    }
                    if (listdwcode.get(j).getClasstype().indexOf("ExamineDrygas_Cata") != -1) {
                        List<String> douList = examinedrygascataservice.GetCalculateValue(listdwcode.get(j).getEtc()
                                , starttime, endtime);
                        IDC_examineItem.put(listdwcode.get(j).getDesc(), douList);
                    }
                    if (listdwcode.get(j).getClasstype().indexOf("ExamineDrycokecon_Cata") != -1) {
                        List<String> douList = examinedrycokeconcataservice.GetCalculateValue(listdwcode.get(j).getEtc()
                                , starttime, endtime);
                        IDC_examineItem.put(listdwcode.get(j).getDesc(), douList);
                    }
                    if (listdwcode.get(j).getClasstype().indexOf("ExamineGasron_Cata") != -1) {
                        List<String> douList = examinegasroncataservice.GetCalculateValue(listdwcode.get(j).getEtc()
                                , starttime, endtime);
                        IDC_examineItem.put(listdwcode.get(j).getDesc(), douList);
                    }
                    if (listdwcode.get(j).getClasstype().indexOf("ExamineGasDiesel_Cata") != -1) {
                        List<String> douList = examinegasdieselcataservice.GetCalculateValue(listdwcode.get(j).getEtc()
                                , starttime, endtime);
                        IDC_examineItem.put(listdwcode.get(j).getDesc(), douList);
                    }

                }
                idc_examine_value.put(dwlist.get(i), IDC_examineItem);
                //}

            }
            String[] group = new String[]{"非计划停工次数", "能耗/10+损失", "长周期运行天数", "[汽油收率×1.2+柴油收率+液化气收率]×(原料密度/1000)×{100/(100-原料残炭×0.8)}+掺渣比例/20", "烟机累计回收功率/主风机累计耗功", "干气C3+含量", "（干气+生焦）/转化率", "汽油收率×RON", "汽油和柴油重叠度"};
            // 从已存在的数据中找出各个装置的停工时常，判断出哪些企业不参与考核

            List<String> stopname = new ArrayList();//存放不显示的要过滤掉的企业
            List<TablePageConfigVO> fjhtglist =
                    list.stream().filter(ivo -> ivo.getDesc().equals("非计划停工次数")).collect(Collectors.toList());
            for (int i = 0; i < idc_examine_value.size(); i++) {
                for (int j = 0; j < fjhtglist.size(); j++) {
                    if (idc_examine_value.get(fjhtglist.get(j).getDwcode()) != null) {
                        List<String> stopL = idc_examine_value.get(fjhtglist.get(j).getDwcode()).get(group[0]);
                        if (Double.parseDouble(stopL.get(2).split("\\,")[2]) > 90)
                            stopname.add(idc_examine_value.get(fjhtglist.get(j).getDwcode()).get(group[0]).toString());
                    }
                }
            }
            Map<String, Double> IDC1 = new HashMap<>();
            Map<String, Double> IDC2 = new HashMap<>();
            Map<String, Double> IDC3 = new HashMap<>();
            Map<String, Double> IDC4 = new HashMap<>();
            Map<String, Double> IDC5 = new HashMap<>();
            Map<String, Double> IDC6 = new HashMap<>();
            Map<String, Double> IDC7 = new HashMap<>();
            for (int i = 0; i < idc_examine_value.size(); i++) {
                for (int m = 0; m < dwlist.size(); m++) {

                    if (idc_examine_value.get(dwlist.get(m)) != null) {
                        for (Map.Entry<String, Map<String, List<String>>> item : idc_examine_value.entrySet()
                        ) {
                            double LI1 = 0;
                            double LI2 = 0;
                            double LI3 = 0;
                            double LI4 = 0;
                            double LI5 = 0;
                            double LI6 = 0;
                            double LI7 = 0;
                            for (Map.Entry<String, List<String>> ite : idc_examine_value.get(dwlist.get(m)).entrySet()) {
                                String keys = ite.getKey();
                                switch (keys) {

                                    case "能耗/10+损失":
                                        LI1 = Double.parseDouble(ite.getValue().get(0).split("\\,")[2]);
                                        break;
                                    case "[汽油收率×1.2+柴油收率+液化气收率]×(原料密度/1000)×{100/(100-原料残炭×0.8)}+掺渣比例/20":
                                        LI2 = Double.parseDouble(ite.getValue().get(0).split("\\,")[2]);
                                        break;
                                    case "烟机累计回收功率/主风机累计耗功":
                                        LI3 = Double.parseDouble(ite.getValue().get(0).split("\\,")[2]);
                                        break;
                                    case "干气C3+含量":
                                        LI4 = Double.parseDouble(ite.getValue().get(0).split("\\,")[2]);
                                        break;
                                    case "（干气+生焦）/转化率":
                                        LI5 = Double.parseDouble(ite.getValue().get(0).split("\\,")[2]);
                                        break;
                                    case "汽油收率×RON":
                                        LI6 = Double.parseDouble(ite.getValue().get(0).split("\\,")[2]);
                                        break;
                                    case "汽油和柴油重叠度":
                                        LI7 = Double.parseDouble(ite.getValue().get(0).split("\\,")[2]);
                                        break;
                                    default:
                                        break;
                                }
                            }
                            IDC1.put(item.getKey(), getXsValue(LI1, 2));
                            IDC2.put(item.getKey(), getXsValue(LI2, 2));
                            IDC3.put(item.getKey(), getXsValue(LI3, 2));
                            IDC4.put(item.getKey(), getXsValue(LI4, 2));
                            IDC5.put(item.getKey(), getXsValue(LI5, 2));
                            IDC6.put(item.getKey(), getXsValue(LI6, 2));
                            IDC7.put(item.getKey(), getXsValue(LI7, 2));
                        }
                    }
                }
                examineconsumptionservice.GetScore(IDC1);
                examinecataservice.GetScore(IDC2);
                examinesmokepowercataservice.GetScore(IDC3);
                examinedrygascataservice.GetScore(IDC4);
                examinedrycokeconcataservice.GetScore(IDC5);
                examinegasroncataservice.GetScore(IDC6);
                examinegasdieselcataservice.GetScore(IDC7);
                List<TablePageConfigVO> listtablepage =
                        tablepageconfigservice.GetTablePageConfigList("CMMain");
                List<String> dwlistdesc =
                        list.stream().map(TablePageConfigVO::getDesc).distinct().collect(Collectors.toList());
                for (int n = 0; n < dwlistdesc.size(); n++) {
                    List<TablePageConfigVO> tablelistdate = new ArrayList<>();
                    final String dwcode = dwlistdesc.get(n);
                    List<TablePageConfigVO> listdwcode =
                            list.stream().filter(ivo -> ivo.getDesc().equals(dwcode)).collect(Collectors.toList());
                    IdKey = dwlist.get(n);
                    if (!stopname.contains(IdKey)) {

                        for (int m = 0; m < listdwcode.size(); m++) {
                            TablePageConfigVO vo = new TablePageConfigVO();
                            vo.setDwcode(listdwcode.get(m).getDwcode());
                            vo.setPointidkey(IdKey);
                            vo.setDevicename(DeviceNameTrans(listtablepage, listdwcode.get(m).getDwcode()));
                            vo.setMax(listdwcode.get(m).getMax());
                            vo.setMin(listdwcode.get(m).getMin());
                            vo.setEtc(listdwcode.get(m).getEtc());
                        /*if(listdwcode.get(m).getDesc().equals(group[0])){
                            List<Double> douList =examinestanddownservice.GetCalculateValue(listdwcode.get(m).getEtc()
                                    , starttime, endtime);
                            IDC_examineItem.put(listdwcode.get(m).getDesc(), douList);
                        }*/
                            if (listdwcode.get(m).getDesc().equals(group[0])) {
                                vo.setScore(examineconsumptionservice.GetScore(idc_examine_value, IdKey,
                                        listdwcode.get(m).getDesc(), stopname,
                                        Double.parseDouble(listdwcode.get(m).getMax())));
                            }
                            if (listdwcode.get(m).getDesc().equals(group[1])) {
                                vo.setScore(examineconsumptionservice.GetSocre(IDC1, idc_examine_value, IdKey,
                                        listdwcode.get(m).getDesc()));
                            }
                            if (listdwcode.get(m).getDesc().equals(group[2])) {
                                vo.setScore(examinetotalcataservice.getSocre(IDC1, idc_examine_value, IdKey,
                                        listdwcode.get(m).getDesc()));
                            }
                            if (listdwcode.get(m).getDesc().equals(group[3])) {
                                vo.setScore(examinecataservice.GetSocre(IDC1, idc_examine_value, IdKey,
                                        listdwcode.get(m).getDesc()));
                            }
                            if (listdwcode.get(m).getDesc().equals(group[4])) {
                                vo.setScore(examinesmokepowercataservice.GetSocre(IDC1, idc_examine_value, IdKey,
                                        listdwcode.get(m).getDesc()));
                            }
                            if (listdwcode.get(m).getDesc().equals(group[5])) {
                                vo.setScore(examinedrygascataservice.GetSocre(IDC1, idc_examine_value, IdKey,
                                        listdwcode.get(m).getDesc()));
                            }
                            if (listdwcode.get(m).getDesc().equals(group[6])) {
                                vo.setScore(examinedrycokeconcataservice.GetSocre(IDC1, idc_examine_value, IdKey,
                                        listdwcode.get(m).getDesc()));
                            }
                            if (listdwcode.get(m).getDesc().equals(group[7])) {
                                vo.setScore(examinegasroncataservice.GetSocre(IDC1, idc_examine_value, IdKey,
                                        listdwcode.get(m).getDesc()));
                            }
                            if (listdwcode.get(m).getDesc().equals(group[8])) {
                                vo.setScore(examinegasdieselcataservice.GetSocre(IDC1, idc_examine_value, IdKey,
                                        listdwcode.get(m).getDesc()));
                            }
                            /*ExamineUtil.RegisterExam(listdwcode.get(m).getDesc(),vo);*/
                            tablelistdate.add(vo);

                        }

                        mapDevice.put(dwlistdesc.get(n), tablelistdate);
                    } else {
                        log.debug("企业" + IdKey + "由于停工时常超过90天，不计入考核。");
                    }

                }
                //为提高第一张图的显示速度，将数据处理工作放在了buffer刷新中实现
                //其余的数据处理工作放在叶面中实现
                List value = new ArrayList();
                List key = new ArrayList();
                for (Map.Entry<String, List<TablePageConfigVO>> item : mapDevice.entrySet()
                ) {

                    key.add(item.getKey());
                    value.add(item.getValue());
                }

                dataList.clear();
                dataList.add(key);
                dataList.add(value);
                dataList.add(mapDevice);
                /*newmapDevice=mapDevice;*/
                log.debug("===========================装置考核相关内容成功缓存=======================");
                //第三列存放原始的未加工的类
                return dataList;
            }
        } catch (Exception e) {
            log.error("=====================刷新装置考核得分时报错==============原因：");
            log.error(e.getMessage());
            // //return false;
            /* throw new Exception(e.getMessage() + "堆栈信息：" + e.getStackTrace());*/
        }
        return dataList;
    }

    /// <summary>
    /// 转换中英文名称
    /// </summary>
    /// <param name="Ename"></param>
    /// <returns></returns>
    public String DeviceNameTrans(List<TablePageConfigVO> list, String Ename) {

        String labername = "";
        for (int i = 0; i < list.size(); i++) {
            if (list.get(i).getDwcode().equals(Ename))
                labername = list.get(i).getDevicename();
        }
        return labername;
    }

    public Double getXsValue(Double value, Integer number) {
        BigDecimal bg = new BigDecimal(value);
        Double f1 = bg.setScale(number, BigDecimal.ROUND_HALF_UP).doubleValue();
        return f1;
    }

    public List<ExamineSocraparamVo> getExamineExamineConfiglist() {
        List<ExamineSocraparamVo> list2 = new ArrayList<>();
        List list = getExaminelistnew();
        Map<String, List<TablePageConfigVO>> mapDevice  = (Map<String, List<TablePageConfigVO>>)list.get(2);
        List<TablePageConfigVO> dt=tablepageconfigservice.GetTablePageConfigList("DAMain");
        List<String> bufIL0=(List<String>)list.get(0);
        List<TablePageConfigVO> bufIL1=(List<TablePageConfigVO>)list.get(1);
        //bufIL[0]为装置名称，bufIL[1]为得分，先排序，再按排序的找对应的项的细项内容
        list2= CreatTable(dt, bufIL0,bufIL1, mapDevice);
        return list2;
    }
    private List<ExamineSocraparamVo> CreatTable(List<TablePageConfigVO> dt, List<String> bufIL0, List<TablePageConfigVO> bufIL1,
                            Map<String, List<TablePageConfigVO>> mapDevice)
    {
        String[] group = new String[]{"非计划停工次数", "能耗/10+损失", "长周期运行天数", "[汽油收率×1.2+柴油收率+液化气收率]×(原料密度/1000)×{100/" +
                "(100-原料残炭×0.8)}+掺渣比例/20", "烟机累计回收功率/主风机累计耗功", "干气C3+含量", "（干气+生焦）/转化率", "汽油收率×RON", "汽油和柴油重叠度"};
        List<ExamineSocraparamVo> list=new ArrayList<>();
        /*AddTitle();
        List sortL = SortDoubleList(bufIL0, bufIL1);*/
        /*for (int i = 0; i < ((List)sortL.get(0)).size(); i++)
        {*/
        List<String> listdou= ExamineUtil.GetRealValues();
        List<String> devicelist=
                mapDevice.get("非计划停工次数").stream().map(TablePageConfigVO::getDevicename).distinct().collect(Collectors.toList());
        for (int i = 0; i < devicelist.size(); i++) {
            ExamineSocraparamVo vo = new ExamineSocraparamVo();
            final String devicname=devicelist.get(i);
            for (int j = 0; j < group.length; j++) {
                List<TablePageConfigVO> fjht=mapDevice.get(group[j]);
                List<TablePageConfigVO> tablepointvalue=
                        fjht.stream().filter(ivo -> ivo.getDevicename().equals(devicname)).collect(Collectors.toList());
                String idkeynew="";
                if("非计划停工次数".equals(group[j])) {
                    String etc =
                            fjht.stream().filter(ivo -> ivo.getDevicename().equals(devicname)).collect(Collectors.toList()).get(0).getEtc();
                    idkeynew = etc.split("\\|")[0];
                }else {
                    idkeynew=
                            fjht.stream().filter(ivo -> ivo.getDevicename().equals(devicname)).collect(Collectors.toList()).get(0).getPointidkey();
                }
                final String idkey=idkeynew;
                final  String groupstring=group[j];
                List<String> listdouble=
                        listdou.stream().filter(ivo -> ivo.indexOf(groupstring) != -1 && ivo.indexOf(idkey) != -1).collect(Collectors.toList());
                for (int k = 0; k < listdouble.size(); k++) {
                    String[] valuelist = listdouble.get(k).split("\\,");
                    vo.setDevicename(devicname);
                    Double pointvalue=tablepointvalue.get(0).getScore()==null?0:tablepointvalue.get(0).getScore();
                    if (valuelist[0].indexOf("非计划停工次数") != -1) {
                        if (valuelist[0].indexOf("次数") != -1) {
                            vo.setFjhtgcs(YczdCataUtils.getXsValue(Double.parseDouble(valuelist[2]),0));
                        }else {
                            vo.setFjhtgcs("0");
                        }
                        if (valuelist[0].indexOf("天数") != -1) {
                            vo.setFjhtgts(YczdCataUtils.getXsValue(Double.parseDouble(valuelist[2]),0));
                        }else {
                            vo.setFjhtgts("0");
                        }
                        if(tablepointvalue.size()>0){
                            vo.setFjhtgdf(YczdCataUtils.getXsValue(pointvalue,0));
                        }else {
                            vo.setFjhtgdf("0");
                        }
                    }
                    if (valuelist[0].indexOf("能耗/10+损失") != -1) {
                        if (valuelist[0].indexOf("能耗/10+损失") != -1) {
                            vo.setNhz(YczdCataUtils.getXsValue(Double.parseDouble(valuelist[2]),0));
                        }else {
                            vo.setNhz("0");
                        }
                        if(tablepointvalue.size()>0){
                            vo.setNhdf(YczdCataUtils.getXsValue(pointvalue,1));
                        }else {
                            vo.setNhdf("0");
                        }
                    }
                    if (valuelist[0].indexOf("长周期运行天数") != -1) {
                        if (valuelist[0].indexOf("天数") != -1) {
                            vo.setCzqyxts(YczdCataUtils.getXsValue(Double.parseDouble(valuelist[2]),0));
                        }else {
                            vo.setCzqyxts("0");
                        }
                        if (valuelist[0].indexOf("利用率") != -1) {
                            vo.setCzqyxlyl(YczdCataUtils.getXsValue(Double.parseDouble(valuelist[2]),0));
                        }else {
                            vo.setCzqyxlyl("0");
                        }
                        if(tablepointvalue.size()>0){
                            vo.setCzqyxdf(YczdCataUtils.getXsValue(pointvalue,1));
                        }else {
                            vo.setCzqyxdf("0");
                        }
                    }
                    if (valuelist[0].indexOf("[汽油收率×1.2+柴油收率+液化气收率]×(原料密度/1000)×{100/(100-原料残炭×0.8)}+掺渣比例/20") != -1) {
                        if (valuelist[0].indexOf("[汽油收率×1.2+柴油收率+液化气收率]×(原料密度/1000)×{100/(100-原料残炭×0.8)}+掺渣比例/20") != -1) {
                            vo.setQyslz(YczdCataUtils.getXsValue(Double.parseDouble(valuelist[2]),0));
                        }else {
                            vo.setQyslz("0");
                        }
                        if(tablepointvalue.size()>0){
                            vo.setQysldf(YczdCataUtils.getXsValue(pointvalue,0));
                        }else {
                            vo.setQysldf("0");
                        }
                    }
                    if (valuelist[0].indexOf("烟机累计回收功率/主风机累计耗功") != -1) {
                        if (valuelist[0].indexOf("烟机累计回收功率/主风机累计耗功") != -1) {
                            vo.setYjljhsz(YczdCataUtils.getXsValue(Double.parseDouble(valuelist[2]),2));
                        }else {
                            vo.setYjljhsz("0");
                        }
                        if(tablepointvalue.size()>0){
                            vo.setYjljhsdf(YczdCataUtils.getXsValue(pointvalue,0));
                        }else {
                            vo.setYjljhsdf("0");
                        }
                    }
                    if (valuelist[0].indexOf("干气C3+含量") != -1) {
                        if (valuelist[0].indexOf("干气C3+含量") != -1) {
                            vo.setGqz(YczdCataUtils.getXsValue(Double.parseDouble(valuelist[2]),0));
                        }else {
                            vo.setGqz("0");
                        }
                        if(tablepointvalue.size()>0){
                            vo.setGqdf(YczdCataUtils.getXsValue(pointvalue,1));
                        }else {
                            vo.setGqdf("0");
                        }
                    }
                    if (valuelist[0].indexOf("（干气+生焦）/转化率") != -1) {
                        if (valuelist[0].indexOf("（干气+生焦）/转化率") != -1) {
                            vo.setSjz(YczdCataUtils.getXsValue(Double.parseDouble(valuelist[2]),0));
                        }else {
                            vo.setSjz("0");
                        }
                        if(tablepointvalue.size()>0){
                            vo.setSjdf(YczdCataUtils.getXsValue(pointvalue,0));
                        }else {
                            vo.setSjdf("0");
                        }
                    }
                    if (valuelist[0].indexOf("汽油收率×RON") != -1) {
                        if (valuelist[0].indexOf("汽油收率×RON") != -1) {
                            vo.setQyronz(YczdCataUtils.getXsValue(Double.parseDouble(valuelist[2]),0));
                        }else {
                            vo.setQyronz("0");
                        }
                        if(tablepointvalue.size()>0){
                            vo.setQyrondf(YczdCataUtils.getXsValue(pointvalue,0));
                        }else {
                            vo.setQyrondf("0");
                        }
                    }
                    if (valuelist[0].indexOf("汽油和柴油重叠度") != -1) {
                        if (valuelist[0].indexOf("汽油和柴油重叠度") != -1) {
                            vo.setCycddz(YczdCataUtils.getXsValue(Double.parseDouble(valuelist[2]),0));
                        }else {
                            vo.setCycddz("0");
                        }
                        if(tablepointvalue.size()>0){
                            vo.setCycdddf(YczdCataUtils.getXsValue(pointvalue,0));
                        }else {
                            vo.setCycdddf("0");
                        }
                    }

                }
            }
            list.add(vo);
        }

        /*for (Map.Entry<String, List<TablePageConfigVO>> item : mapDevice.entrySet()
            ) {

            //for (int j = 0; j < group.length; j++) {
                for (int i = 0; i < item.getValue().size(); i++) {
                    ExamineSocraparamVo vo = new ExamineSocraparamVo();
                    *//*String devicename = item.getValue().get(0).getDevicename();
                    String score = item.getValue().get(0).getScore().toString();*//*
                    final String dwname=item.getValue().get(i).getDevicename();
                    final String dwcode=item.getValue().get(i).getDwcode();
                    final String groupstring=item.getKey();
                    final String pointidkey=item.getValue().get(i).getPointidkey();
                    List<String> listdouble=listdou.stream().filter(ivo -> ivo.indexOf(pointidkey) != -1 && ivo.indexOf(groupstring) != -1).collect(Collectors.toList());

                  *//* if (groupstring.equals("烟机累计回收功率/主风机累计耗功")) {
                       listdouble =

                   }else {
                       listdouble =
                               listdou.stream().filter(ivo -> ivo.indexOf(dwcode) != -1 && ivo.indexOf(groupstring) != -1).collect(Collectors.toList());
                   }*//*




                }


            //}

        }*/
        //region === 弃用
            /*//插入装置名称
            insertcell(((IList)sortL[0])[i].ToString(), tableRow, 6, i, "table_blue1");
            //插入排名序号
            insertcell((i + 1).ToString(), tableRow, 2.0, i, "table_blue3");
            //插入排名得分
            insertcell(((IList)sortL[1])[i].ToString(), tableRow, 2.0, i, "table_blue3");

            //循环考核项
            foreach (DictionaryEntry ExamItems in IDicDevice)
            {
                if (ExamItems.Key.ToString() == ((IList)sortL[0])[i].ToString())
                {
                    int k = 0;
                    string cssStyle = "table_blue3";
                    for (int j = 0; j < dt.Rows.Count; j++)
                    {
                        if (k % 2 == 0)
                            cssStyle = "table_yellow2";
                        else
                            cssStyle = "table_blue3";
                        insertcell(((Examine)ExamItems.Value).GetScore(dt.Rows[j][0].ToString()).ToString(), tableRow, 2.0, i, cssStyle);
                        foreach (double readValue in ((Examine)ExamItems.Value).GetRealValues(dt.Rows[j][0].ToString()))
                        {
                            string num = readValue > 1 ? Math.Round(readValue, 1).ToString() : Math.Round(readValue, 2).ToString();
                            num = readValue > 100 ? Math.Round(readValue, 0).ToString() : num;
                            insertcell(num, tableRow, 2.0, i, cssStyle);
                        }
                        k++;
                    }
                }
            }
            this.Table1.Rows.Add(tableRow);*/


        //}
        //endregion
        return list;
    }
    private void AddTitle()
    {
        List<TablePageConfigVO> datalise=new ArrayList<>();
        List<TablePageConfigVO> dt=tablepageconfigservice.GetTablePageConfigList("ExamineTitle");

        List<TablePageConfigVO> dt1 =
                dt.stream().filter(ivo -> ivo.getTitletype().equals("1")).collect(Collectors.toList());;
        List<TablePageConfigVO> dt2 =
                dt.stream().filter(ivo -> ivo.getTitletype().equals("2")).collect(Collectors.toList());;

        datalise.addAll(dt1);
        datalise.addAll(dt2);
    }
    /// <summary>
    /// 返回一个list中最大值的索引
    /// </summary>
    /// <param name="list"></param>
    /// <returns></returns>
    private List<String> SortDoubleList(List<String> name, List<TablePageConfigVO> value)
    {
        List<String> valuev=new ArrayList<>();
        List<String> resultL = new ArrayList();
        String tempscore = "0";
        String tempname = "";
        for (int i = 0; i < value.size() - 1; i++)
        {
            for (int j = i + 1; j < value.size(); j++)
            {
                if (Double.parseDouble(value.get(i).getScore().toString()) < Double.parseDouble(value.get(j).getScore().toString()))
                {
                    tempscore = value.get(i).getScore().toString();
                    valuev.set(i,value.get(j).getScore().toString());
                    valuev.set(j,tempscore);
                    /*tempname = name.get(i).toString();
                    name.set(i,name.get(j).toString());
                    name.set(j,tempname);*/
                    tempname = name.get(i).toString();
                    name.set(i,name.get(j).toString());
                    name.set(j,tempname);
                }
            }
        }
        resultL.addAll(name);
        resultL.addAll(valuev);
        return resultL;
    }

}
