import html2canvas from "html2canvas";
import JsPDF from 'jspdf'

export const downloadPDF = page => {
  html2canvas(page).then(function(canvas) {
    page.append(canvas);
  });
};

export const htmlToPdf = (name:String, title:String) =>{
  let ctx = document.createElement('canvas');
  let eles = document.querySelectorAll(`.${name}`); 
  let ele:any = eles&&eles[0]||null;

  // console.log(ctx,"===element==",element);
  let opts = {
    canvas: ctx,
    width: ele.clientWidth,
    height: ele.clientHeight,
    // dpi: 1,
    scale: 1, // 缩放比例，提高生成图片清晰度
    useCORS: true, // 允许加载跨域的图片
    allowTaint: false, // 允许图片跨域，和 useCORS 二者不可共同使用
    tainttest: true, // 检测每张图片已经加载完成
    logging: false, // 日志开关，发布的时候记得改成 false
    scrollY: 0,
    scrollX: 0
  }
  ctx.width = ele.clientWidth
  ctx.height = ele.clientHeight
  ctx.getContext('2d')
  // console.log('opts', opts)
  html2canvas(ele, opts).then(function(canvas) {
    let contentWidth = canvas.width
    let contentHeight = canvas.height
    let pageHeight = 841.89
    let leftHeight = contentHeight
    let position = 0
    let imgWidth = 595.28
    let imgHeight = 592.28 / contentWidth * contentHeight - 20
    let pageData = canvas.toDataURL('image/jpeg', 1.0)
    let PDF = new JsPDF("p", 'pt', 'a4')
    // console.log(leftHeight,pageHeight,'111111111');
    if (leftHeight < pageHeight) {
      // addImage(pageData, 'JPEG', 左，上，宽度，高度)设置
      PDF.addImage(pageData, 'JPEG', 0, 0, imgWidth, imgHeight)
    } else {
      while (leftHeight > 0) {
        PDF.addImage(pageData, 'JPEG', 0, position, imgWidth, imgHeight)
        leftHeight -= pageHeight
        position -= 841.89
        if (leftHeight > 0) {
          PDF.addPage()
        }
      }
    }
    PDF.save(`${title}.pdf`)
  }).catch((error) => {
    console.log('打印失败', error)
  })
}