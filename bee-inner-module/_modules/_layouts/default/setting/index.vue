<template>
  <div>
    <i class="el-icon-bee-setting icon-iconify" @click="changeDrawer(true)"></i>
    <transition name="fade">
      <setting-drawer v-if="visible" :visible.sync="visible" @close="changeDrawer(false)" />
    </transition>
  </div>
</template>
<script>
  import SettingDrawer from './SettingDrawer.vue'
  import { isIE, getIEVersion } from '_/_utils/tools.js' // 引入是否IE函数 获取IE版本函数
  export default {
    name: 'Setting',
    components: {
      SettingDrawer,
    },
    data() {
      return {
        visible: false
      }
    },
    methods: {
      changeDrawer(val) {
        let ele = document.getElementsByClassName('sider')
        if (ele.length) {
          ele[0].style.zIndex = val ? 5 : 10
        }
        // 浏览器版本判断
        if (isIE()) {
          let version = +getIEVersion() // 获取IE版本
          if (!isNaN(version) && version < 11) { // 如果IE版本低于11
            this.$notify({
              title: '提示',
              // type: 'warning',
              message: '当前IE浏览版本过低，请升级浏览器',
              offset: 100, // 偏移的距离，在同一时刻，所有的 Notification 实例应当具有一个相同的偏移量
              duration: 4000 // 显示时间, 毫秒。设为 0 则不会自动关闭
            })
            return
          }
        }
        this.visible = val
      }
    }
  }
</script>
<style lang="less" scoped>
  .icon-iconify {
    font-size: 16px;
    // margin: 0 8px;
    height: 100%;
    display: flex;
    justify-items: center;
    align-items: center;
  }
  .fade-enter-active,
  .fade-leave-active {
    transition: opacity 0.5s;
  }
  .fade-enter,
  .fade-leave-to {
    opacity: 0;
  }
</style>
