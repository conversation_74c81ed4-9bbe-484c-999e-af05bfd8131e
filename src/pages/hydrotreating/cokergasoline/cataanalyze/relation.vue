<script lang="ts" setup>
/**
 * 未完成，因为没有外网，无法完成反应温度开发，需要查看echart的温度图
 */
import { ref, reactive, computed,nextTick,onMounted,onUnmounted } from "vue";
import { message } from "ant-design-vue";
import { iTableColumn,DataItem } from "@/interface/index";
import type { TableProps, TableColumnType } from "ant-design-vue";
import * as echarts from "echarts";
import { useRoute } from "vue-router";
import useHydroStore from "@/store/hydro";
const storeHydro = useHydroStore();
const route = useRoute();
// 催化剂运行分析-催化剂分析
const activeKey = ref(["table_t1"]);
document.title = "相关参数展示-催化剂分析-加氢焦化汽油装置";
const query = route.query,
  code = query.code || "";
const relationInfo1 = ref({
  devicename:"",
  divinput1: "",
  divinput2: "",
});
const relationInfo2 = ref({
  devicename:"",
  divinput1: "",
  divinput2: "",
});
const relationInfo3 = ref({
  devicename:"",
  divinput1: "",
  divinput2: "",
});
const listTableData = ref([]);
const catalyzerInfo=computed(()=>{
  return listTableData.value[0]||{devicename:""};
})
const titleT1 = computed(() => {
  return catalyzerInfo.value.devicename + "反应温度";
});
const titleT2 = computed(() => {
  return catalyzerInfo.value.devicename + "床层压降";
});
const titleT3 = computed(() => {
  return catalyzerInfo.value.devicename + "径向温差";
});
const titleT4 = computed(() => {
  return catalyzerInfo.value.devicename + "反应温升";
});
const titleT5 = computed(() => {
  return catalyzerInfo.value.devicename + "催化剂信息";
});
let myChart1:any=null,myChart2:any=null;
type EChartsOption = echarts.EChartsOption;
const option: EChartsOption = reactive({
  series: [
    {
      type: 'gauge',
      center: ['50%', '60%'],
      startAngle: 200,
      endAngle: -20,
      min: 0,
      max: 60,
      splitNumber: 12,
      itemStyle: {
        color: '#FFAB91'
      },
      progress: {
        show: true,
        width: 30
      },
      pointer: {
        show: false
      },
      axisLine: {
        lineStyle: {
          width: 30
        }
      },
      axisTick: {
        distance: -45,
        splitNumber: 5,
        lineStyle: {
          width: 2,
          color: '#999'
        }
      },
      splitLine: {
        distance: -52,
        length: 14,
        lineStyle: {
          width: 3,
          color: '#999'
        }
      },
      axisLabel: {
        distance: -20,
        color: '#999',
        fontSize: 20
      },
      anchor: {
        show: false
      },
      title: {
        show: false
      },
      detail: {
        valueAnimation: true,
        width: '60%',
        lineHeight: 40,
        borderRadius: 8,
        offsetCenter: [0, '-15%'],
        fontSize: 40,
        fontWeight: 'bolder',
        formatter: '{value} °C',
        color: 'inherit'
      },
      data: [
        {
          value: 20
        }
      ]
    },
    {
      type: 'gauge',
      center: ['50%', '60%'],
      startAngle: 200,
      endAngle: -20,
      min: 0,
      max: 60,
      itemStyle: {
        color: '#FD7347'
      },
      progress: {
        show: true,
        width: 8
      },
      pointer: {
        show: false
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      splitLine: {
        show: false
      },
      axisLabel: {
        show: false
      },
      detail: {
        show: false
      },
      data: [
        {
          value: 20
        }
      ]
    }
  ]
})
function initChart(){
  const doc: any = document,
    chartDom1: any = doc.getElementById("chartId");
    // chartDom2: any = doc.getElementById("chartId2")

  if (!myChart1) {
    myChart1 = echarts.init(chartDom1);
    myChart1.setOption(option);
  }

  // if (!myChart2) {
  //   myChart2 = echarts.init(chartDom2);
  //   myChart2.setOption(option);
  // }

  nextTick(() => {
    setInterval(()=>{
      const random = +(Math.random()*60).toFixed(2),
        random2 = +(Math.random()*60).toFixed(2);
      myChart1.setOption({
        series:[
          {
            data:[
              {
                value:random
              }
            ]
          },
          {
            data:[
              {
                value:random
              }
            ]
          }
        ]
      });
      // myChart2.setOption({
      //   series:[
      //     {
      //       data:[
      //         {
      //           value:random2
      //         }
      //       ]
      //     },
      //     {
      //       data:[
      //         {
      //           value:random2
      //         }
      //       ]
      //     }
      //   ]
      // });
    },5e3)
    window.addEventListener('resize', function () {
      myChart1.resize();
      //myChart2.resize();
    })
  })
}
function fetchRelation1() {
  storeHydro
    .fetchRelationInfo({
      pageCode:"CMRadialTemperature",
      code: code,
      deviceType: "Cokergasoline",
    })
    .then((resp) => {
      relationInfo1.value = resp[0] || {};
    });
}
function fetchRelation2() {
  storeHydro
    .fetchRelationInfo({
      pageCode:"CMStepdownBed",
      code: code,
      deviceType: "Cokergasoline",
    })
    .then((resp) => {
      relationInfo2.value = resp[0] || {};
    });
}
function fetchRelation3() {
  storeHydro
    .fetchRelationInfo({
      pageCode:"CMResponseTempRise",
      code: code,
      deviceType: "Cokergasoline",
    })
    .then((resp) => {
      relationInfo2.value = resp[0] || {};
    });
}
const columns: TableColumnType<iTableColumn>[] = [
  {
    title: "装置名称",
    dataIndex: "devicename",
    key: "devicename",
    fixed: "left",
    // ellipsis: true,
  },
  {
    title: "反应器名称",
    dataIndex: "reactorname",
    key: "reactorname",
    // ellipsis: true,
  },
  {
    title: "床层名称",
    dataIndex: "bedname",
    key: "bedname",
    // ellipsis: true,
  },
  {
    title: "催化剂牌号",
    dataIndex: "reactornum",
    key: "reactornum",
    // ellipsis: true,
  },
  {
    title: "类型",
    dataIndex: "catalystname",
    key: "catalystname",
    // ellipsis: true,
  },
  {
    title: "装填高度",
    dataIndex: "filledheight",
    key: "filledheight",
    // ellipsis: true,
  },
  {
    title: "装填重量",
    dataIndex: "filledweight",
    key: "filledweight",
    // ellipsis: true,
  },
  {
    title: "堆比",
    dataIndex: "bulkdensity",
    key: "bulkdensity",
    // ellipsis: true,
  },
  {
    title: "换剂/再生时间",
    dataIndex: "operatetime",
    key: "operatetime",
    // ellipsis: true,
  },
  {
    title: "换剂/再生情况",
    dataIndex: "runstate",
    key: "runstate",
    // ellipsis: true,
  }
];
const isFetching = ref<boolean>(false);
const scrollTableOption = reactive({ x: "max-content"});
function fetchRelationCatalyzerInfo(){
  isFetching.value=true;
  storeHydro
    .fetchRelationCatalyzerInfo({
      code: code,
      deviceType: "Cokergasoline",
    })
    .then((resp) => {
      listTableData.value = resp || [];
    }).finally(()=>{
      isFetching.value=false;
    });
}
fetchRelation1();
fetchRelation2();
fetchRelation3();
fetchRelationCatalyzerInfo();

onMounted(()=>{
  initChart();
})
onUnmounted(() => {
  myChart1?.clear();
  myChart1?.dispose();
  myChart1 = null;
});
function doExport(){
  if (!listTableData.value || listTableData.value.length === 0) {
    message.warning("暂无可导出的数据！");
    return;
  }
  import("xlsx").then(XLSX => {
    const ws = XLSX.utils.json_to_sheet(listTableData.value);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "相关参数展示");
    XLSX.writeFile(wb, "相关参数展示-焦化汽油装置.xlsx");
  });
}
</script>
<template>
  <div class="page">
    <a-collapse v-model:activeKey="activeKey">
      <a-collapse-panel key="table_t1" :header="titleT1">
        <div class="charts">
          <div class="chart" id="chartId"></div>
          <!-- <div class="chart" id="chartId2"></div> -->
        </div>
      </a-collapse-panel>
      <a-collapse-panel key="table_t2" :header="titleT2">
        
      </a-collapse-panel>
      <a-collapse-panel key="table_t3" :header="titleT3">
        
      </a-collapse-panel>
      <a-collapse-panel key="table_t4" :header="titleT4">
        
      </a-collapse-panel>
      <a-collapse-panel key="table_t5" :header="titleT5">
        <a-table 
        class="table" 
        bordered 
        :columns="columns" 
        :data-source="listTableData" 
        :rowKey="(record) => { return 'relation-info-' + record.devicename; }" 
        :footer="null"
        :scroll="scrollTableOption"
        :pagination="false"
        :loading="isFetching"></a-table>
        <div class="export-btn-center">
          <a-button class="field" @click.stop="doExport">导出</a-button>
        </div>
      </a-collapse-panel>
    </a-collapse>
  </div>
</template>
<style lang="less" scoped>
.page {
  .charts{
    display:flex;
    >.chart{
      width:100%;height:400px;
    }
  }
  .table {
    &.t1 {
      background: url("@/assets/images/img_petro_life_estimation.jpg") no-repeat center;
      background-size: contain;
    }
  }
}
.export-btn-center {
  display: flex;
  justify-content: center;
  margin-top: 16px;
}
</style>
