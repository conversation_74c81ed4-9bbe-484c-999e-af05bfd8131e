const path = 'bee' // 应用路径，对应baseUrl,outputDir,
module.exports = {
  title: '中国石化炼油技术分析及远程诊断系统',
  projectName: path,
  description: '中国石化炼油技术分析及远程诊断系统',
  ico: `${process.env.NODE_ENV === 'development' ? '' : '/' + path}/static/images/system-ico.svg`, // 网站缩略图标 (路径同项目名)
  logo: process.env.BASE_URL + 'static/images/system-default.svg', // 项目logo  1.全图展示 图片路径 项目名称(sinopec|pcitc)-xxx(logo|ico)-xxx(default|white).png 2.单logo,配置logo路径,name有值即可
  // name: '乾坤', // 项目名 默认空,有值走个性化logo+文字展示
  routeMode: 'history', //  路由模式
  routeMenuMode: 'BACK', // 菜单模式 ROUTE_MAPPING(前端路由) BACK(后端路由)
  homePageRouteName: 'Home', // 默认首页name
  loginPageRouteName: 'Login', // 登录路由name
  loginPageTemplateName: 'default', // 登录模板(同文件夹名)
  defaultTheme: 'blue', // 模板主题 blue(默认) cyanine white sky-blue
  defaultLayout: 'sidebar', // 模板layout sidebar(左右菜单布局) mix(上-左右菜单混合布局) top-menu（顶部菜单布局）
  needLogin: true, // 是否需要登录
  log: false, // 日志上报 默认 false
  isAesEncryption: true
}
