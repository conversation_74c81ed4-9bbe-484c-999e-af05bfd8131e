<script lang="ts" setup>
import { ref,reactive, nextTick, onMounted,onUnmounted } from "vue";
import { message } from "ant-design-vue";
import * as echarts from "echarts";
import dayjs from "dayjs";
import type {Dayjs} from "dayjs";
import {isEmpty,toString} from "lodash";
import CMPTableCollapse from "@/components/hydro/residualoil/TableCollapse.vue";

import useHydroStore from "@/store/hydro.ts";
const storeHydro = useHydroStore();
// 1. 定义60种颜色
const colors = [
  '#3cb44b', '#ffe119', '#4363d8', '#f58231', '#911eb4', '#46f0f0', '#f032e6', '#bcf60c', '#fabebe',
  '#008080', '#e6beff', '#9a6324', '#fffac8', '#800000', '#aaffc3', '#808000', '#ffd8b1', '#000075', '#808080',
  '#ffffff', '#000000', '#ff7f00', '#1f78b4', '#b2df8a', '#33a02c', '#fb9a99', '#e31a1c', '#fdbf6f', '#ff1493',
  '#6a3d9a', '#b15928', '#8dd3c7', '#ffffb3', '#bebada', '#fb8072', '#80b1d3', '#fdb462', '#b3de69', '#fccde5',
  '#d9d9d9', '#bc80bd', '#ccebc5', '#ffed6f', '#a6cee3', '#1f9e77', '#d95f02', '#7570b3', '#e7298a', '#66a61e',
  '#b3b3b3', '#ffb3b3', '#b3ffb3', '#b3b3ff', '#ffffb3', '#b3ffff', '#ffb3ff', '#c2c2f0', '#f0c2c2', '#e6194b','#c2f0c2'
];
document.title="三维四象限分析-生产运行概览-渣油加氢装置";
type EChartsOption = echarts.EChartsOption;
const option: EChartsOption = {
  title:{
    subtext:"",
    subtextStyle:{
      align:"right"
    },
    right: 160,
    top: -10
  },
  graphic:{
    elements:[
      {
        type:'text',
        right:110,
        bottom:80,
        style:{
          text:"优秀",
          fill:"red"
        }
      },
      {
        type:'text',
        left:130,
        top:70,
        style:{
          text:"落后",
          fill:"red"
        }
      },
    ]
  },
  tooltip: {
    show:false,
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
      crossStyle: {
        color: '#999'
      }
    }
  },
  legend:{
    right:120,
    top: 30,
    itemGap:20
  },
  xAxis: [
    {
      type: 'value',
      axisPointer: {
        type: 'shadow'
      },
      axisTick:{
        alignWithLabel:true
      },
    }
  ],
  yAxis: [
    {
      type: 'value',
      //nameLocation:"center",
      //nameRotate:-270,
      //nameGap:58,
      min:0,
      name:"(能耗/10)+加工损失",
      axiosLine:{
        show:true
      },
    },
  ],
  series: [
    {
      type: 'scatter',
      data: [],
      symbolSize:(rawvalue,params)=>{ 
        return Math.sqrt(params.data.value2*20);
      },
      label: {
        fontFamily: 'SimSun',      // 设置为宋体
        fontSize: 10,              // 字体大小
        //fontWeight: 'bold',        // 加粗
        color: '#000'             // 黑色
      },
      emphasis: {
         itemStyle:{
          color:'blue',
        },        
        focus: 'series',
        label: {          
          show: true,
          formatter: function (param) {
            return "可裂化指数"+param.data.value2;
          },          
          fontFamily: 'Microsoft YaHei, Arial, sans-serif',
          fontSize: 12,
          fontWeight: 'bold',
          color: '#000',
          position: 'top'
        }
      },
      markLine:{
        symbol: 'none',
        lineStyle:{
          type:"soild",
          color:"red"
        },
        data:[]
      }
    }
  ]
}
const option2: EChartsOption = {
  title:{
    subtext:"",
    subtextStyle:{
      align:"right"
    },
    right: 160,
    top: -10
  },
  graphic:{
    elements:[
      {
        type:'text',
        right:110,
        bottom:80,
        style:{
          text:"落后",
          fill:"red"
        }
      },
      {
        type:'text',
        left:130,
        top:70,
        style:{
          text:"优秀",
          fill:"red"
        }
      },
    ]
  },
  tooltip: {
    show:false,
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
      crossStyle: {
        color: '#999'
      }
    }
  },
  legend:{
    right:120,
    top: 30,
    itemGap:20
  },
  xAxis: [
    {
      type: 'value',
      axisPointer: {
        type: 'shadow'
      },
      axisTick:{
        alignWithLabel:true
      },
    }
  ],
  yAxis: [
    {
      type: 'value',
      //nameLocation:"center",
      //nameRotate:-270,
      //nameGap:58,
      min:0,
      name:"原料氮含量(mg/kg)",
      axiosLine:{
        show:true
      },
    },
  ],
  series: [
    {
      type: 'scatter',
      data: [],
      symbolSize:(rawvalue,params)=>{ 
        return Math.sqrt(params.data.value2*20);
      },
      label: {
        fontFamily: 'SimSun',      // 设置为宋体
        fontSize: 10,              // 字体大小
        //fontWeight: 'bold',        // 加粗
        color: '#000'             // 黑色
      },
      emphasis: {
         itemStyle:{
          color:'blue',
        },        
        focus: 'series',
        label: {          
          show: true,
          formatter: function (param) {
            return "抗爆指数损失"+param.data.value2;
          },          
          fontFamily: 'Microsoft YaHei, Arial, sans-serif',
          fontSize: 12,
          fontWeight: 'bold',
          color: '#000',
          position: 'top'
        }
      },
      markLine:{
        symbol: 'none',
        lineStyle:{
          type:"soild",
          color:"red"
        },
        data:[]
      }
    }
  ]
}
let myChart: any = null;
let myChart2: any = null;
function initChart(){
  const doc: any = document,
    chartDom: any = doc.getElementById("chartId"),
    chartDom2: any = doc.getElementById("chartId2");

  if (!myChart) {
    myChart = echarts.init(chartDom);
    myChart.setOption(option);
  }

  if (!myChart2) {
    myChart2 = echarts.init(chartDom2);
    myChart2.setOption(option2);
  }

  nextTick(() => {
    window.addEventListener('resize', function () {
      myChart.resize();
      myChart2.resize();
    })
  })
}
function fetchChartData(){
  myChart.showLoading();
  const date = rankDate.value,
    beginDate = date&&date[0].format("YYYY-MM-DD")||"",
    endDate = date&&date[1].format("YYYY-MM-DD")||"";
  storeHydro.fetchPOChartData({
    pageCode:"BubblrConfig",
    beginDate:beginDate,
    endDate:endDate,
    deviceType:"ResidualOil",
  }).then((resp)=>{
    // console.log("===ph_value=resp==",resp);
    updateTableData(resp);
  }).catch(err=>{
    // console.log("==err==",err);
    message.error((err&&(err.statusText||err.message||err.msg||err))||"数据错误!");
  });
}
function updateTableData(data){
  if(!myChart||isEmpty(data)) return;
  const opts = myChart.getOption(),
    yczdParamListVo = data.yczdParamListVo||[],
    data1 = yczdParamListVo[0],
    data2 = yczdParamListVo[0],
    xData = data1.parametervaluex,
    yData1 = data1.parametervaluey,
    yData2 = data1.parametervaluey2,
    xMarkline = data1.xstriplines[0] || [],
    yMarkline=data1.ystriplines[0] || [];
    // colors=data1.strColor;

  let yData:any[] = [];
  for(let i=0;i<yData1.length;i++){
    const y = yData1[i],
    y2 = yData2[i],
    name=data.deviceSum[i];
    yData.push({
    name: name,
    value: [xData[i], y], // x, y
    value2: y2,
    label: {
      show: true,
      formatter: (v: any) => v.name
    },
      itemStyle: {
        color: name === "股份" ? "red" : colors[i % colors.length], // 股份为红色，其它为色板
        shadowOffsetY: 5,
        shadowBlur: 10,
        shadowColor: 'rgba(120, 36, 50, 0.5)',
      }
    });
  }
  let yDataNew=yData.sort((a, b) => a.index - b.index);
  // console.log("==data==",yData);
  opts.title[0].subtext = data.querytime+ "\n" + "\n"+ " ".repeat(36) +"气泡的大小代表可裂化指数";
  //opts.title[0].subtext = data.querytime;
  //opts.xAxis[0].data = yDataNew.map(d=> d.index);
  opts.series[0].name=data1.parameterName;
  opts.series[0].data= yDataNew;
  opts.series[0].markLine.data=[
    {
      yAxis:yMarkline,
      symbol:"none",
    },
    {
      xAxis:toString(xMarkline),
      symbol:"none",
    },
  ]
  // console.log("====",opts.series[0].markLine.data)
  // console.log("===",ret.retResult.yczdParamListVo[0].parameterValue);
  myChart.setOption(opts);
  myChart.hideLoading();
}
function fetchChartData2(){
  myChart2.showLoading();
  const date = rankDate2.value,
    beginDate = date&&date[0].format("YYYY-MM-DD")||"",
    endDate = date&&date[1].format("YYYY-MM-DD")||"";
  storeHydro.fetchPOChartData({
    pageCode:"DesulfurizeBubCfg",
    beginDate:beginDate,
    endDate:endDate,
    deviceType:"ResidualOil",
  }).then((resp)=>{
    // console.log("===ph_value=resp==",resp);
    updateTableData2(resp);
  }).catch(err=>{
    // console.log("==err==",err);
    message.error((err&&(err.statusText||err.message||err.msg||err))||"数据错误!");
  });
}
function updateTableData2(data){
  if(!myChart2||isEmpty(data)) return;
  const opts = myChart2.getOption(),
    yczdParamListVo = data.yczdParamListVo||[],
    data1 = yczdParamListVo[0],
    data2 = yczdParamListVo[0],
    xData = data1.parametervaluex,
    yData1 = data1.parametervaluey,
    yData2 = data1.parametervaluey2,
    xMarkline=data1.xstriplines[0] || [],
    yMarkline=data1.ystriplines[0] || [];
    // colors=data1.strColor;

  let yData:any[] = [];
  for(let i=0;i<yData1.length;i++){
    const y = yData1[i],
    y2 = yData2[i],
    name=data.deviceSum[i];
    yData.push({
    name: name,
    value: [xData[i], y], // x, y
    value2: y2,
    label: {
      show: true,
      formatter: (v: any) => v.name
    },
      itemStyle: {
        color: name === "股份" ? "red" : colors[i % colors.length], // 股份为红色，其它为色板
        shadowOffsetY: 5,
        shadowBlur: 10,
        shadowColor: 'rgba(120, 36, 50, 0.5)',
      }
    });
  }
  let yDataNew=yData.sort((a, b) => a.index - b.index);
  // console.log("==data==",yData);
  opts.title[0].subtext = data.querytime+ "\n" + "\n"+ " ".repeat(36) +"气泡的大小代表抗爆指数损失";
  //opts.xAxis[0].data = yDataNew.map(d=> d.index);
  opts.series[0].name=data1.parameterName;
  opts.series[0].data= yDataNew;
  opts.series[0].markLine.data=[
    {
      yAxis:yMarkline,
      symbol:"none",
    },
    {
      xAxis:toString(xMarkline),
      symbol:"none",
    },
  ]
  // console.log("====",opts.series[0].markLine.data)
  // console.log("===",ret.retResult.yczdParamListVo[0].parameterValue);
  myChart2.setOption(opts);
  myChart2.hideLoading();
}
onMounted(()=>{
  initChart();
  fetchChartData();
  fetchChartData2();
})
onUnmounted(() => {
  myChart?.clear();
  myChart?.dispose();
  myChart = null;

  myChart2?.clear();
  myChart2?.dispose();
  myChart2 = null;
});


// const rankDate = ref<[Dayjs,Dayjs]>([dayjs().subtract(1,"week"),dayjs()]);
const rankDate = ref<any>(null);
const rankDate2 = ref<[Dayjs,Dayjs]>([dayjs().subtract(5,"years"),dayjs()]);
function doSearch(){
  fetchChartData();
}

function doSearch2(){
  fetchChartData2();
}
</script>
<template>
<div class="page ph_value">
  <div class="chart" id="chartId"></div>
  <p class="tip">总液收(%)</p>
  <div class="filter">
    <a-range-picker class="field"  v-model:value="rankDate"></a-range-picker>
    <a-button class="field" @click.stop="doSearch">确定</a-button>
  </div>
  <div class="chart" id="chartId2"></div>
  <p class="tip">平均反应温度(℃)</p>
  <div class="filter">
    <a-range-picker class="field"  v-model:value="rankDate2"></a-range-picker>
    <a-button class="field" @click.stop="doSearch2">确定</a-button>
  </div>
  <div class="tables">
    <CMPTable-Collapse />
  </div>
</div>
</template>
<style lang="less" scoped>
.page.ph_value{
  >.tip{
    font-size:12px;
    text-align: right;
    margin:-30px 100px 0 0;
  }
  >.chart{
    width:100%;height:600px;
  }
  >.filter{
    margin-top:10px;
    display:flex;
    justify-content: center;
    >.field{
      margin-left:10px;
      &:first-child{margin-left:0;}
    }
  }

  >.tables{
    margin-top:10px;
  }
}
</style>