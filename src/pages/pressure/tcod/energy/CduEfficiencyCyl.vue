<script lang="ts" setup>
import { ref,reactive, nextTick, onMounted,onUnmounted } from "vue";
import { message } from "ant-design-vue";
import * as echarts from "echarts";
import dayjs from "dayjs";
import type {Dayjs} from "dayjs";
import {isEmpty,round} from "lodash";
import CMPTableEnergyPressureTcod from "@/components/pressure/TableTcodEnergy.vue";

import usePressureStore from "@/store/pressure";
const storePressure = usePressureStore();
import useInteractStore from "@/store/interact";
const storeInteract = useInteractStore();
let deviceCalculationList=[];
const activeKey=ref(["CduEfficiencyCyl"]);
document.title="常压炉热效率综合分析-节能优化-装置理论计算-常减压";
type EChartsOption = echarts.EChartsOption;
const option: EChartsOption = reactive({
  title:{
    subtext:"",
    subtextStyle:{
      align:"right"
    },
    right: 160,
    top: -10
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
      crossStyle: {
        color: '#999'
      }
    }
  },
  legend:{
    right:120,
    top: 30,
    itemGap:20
  },
  xAxis: [
    {
      type: 'category',
      axisPointer: {
        type: 'shadow'
      },
      axisTick:{
        alignWithLabel:true
      },
      axisLabel:{
        // rotate:270,
        formatter: function (value) {
          return value.split('').join('\n')
        }
      }
    }
  ],
  yAxis: [
    {
      type: 'value',
      min:0,
      name:"%",
      axiosLine:{
        show:true
      },
      axisLine: {
        show: true
      },
      axisTick: {
        show: true
      }
    },
    {
      type: 'value',
      min:0,
      name:"℃",
      axiosLine:{
        show:true
      },
      axisLine: {
        show: true
      },
      axisTick: {
        show: true
      },
      splitLine: {
        show: false,
        lineStyle: {
          type: 'dashed'
        }
      }
    }
  ],
  series: [
    {
      type: 'bar',
      data: [],
      barMaxWidth: 25,
      label:{
        show:true,
        position:"top",
        formatter:function(v){
          const value = round(v.value,2);
          return value;
        }
      }
    },
    {
      type: 'line',
      data: []
    },
  ]
})
let myChart: any = null;
function initChart(){
  const doc: any = document,
    chartDom: any = doc.getElementById("chartId");

  if (!myChart) {
    myChart = echarts.init(chartDom);
    myChart.setOption(option);
  }

  myChart.on("click",(params)=>{
    // console.log("====click===",params,"====",deviceCalculationList);
    const dataIndex = params.dataIndex,
      seriesIndex = params.seriesIndex,
      data = deviceCalculationList[seriesIndex]||{deviceCode:[]},
      listDeviceCode = data&&data.deviceCode||[],
      deviceCode = listDeviceCode[dataIndex]||"",
      deviceName = params.name,
      date = rankDate.value,
      beginDate = date&&date[0].format("YYYY-MM-DD")||"",
      endDate = date&&date[1].format("YYYY-MM-DD")||"";
      // console.log("====click===",dataIndex,"====",seriesIndex,"====",deviceCode,"====",data);
      storeInteract.showChartDv({
        deviceType:"press",
	      menuType:"tcod",
        deviceCode,
        deviceName,
        beginDate,
        endDate,
        pageCode:"PowerPop",
      });
  })

  nextTick(() => {
    window.addEventListener('resize', function () {
      myChart.resize();
    })
  })
}
function fetchChartData(){
  myChart.showLoading();
  const date = rankDate.value,
    beginDate = date&&date[0].format("YYYY-MM-DD")||"",
    endDate = date&&date[1].format("YYYY-MM-DD")||"";
  storePressure.fetchTcodChartData({
    pageCode:"CduEfficiencyCyl",
    beginDate:beginDate,
    endDate:endDate,
  }).then((resp)=>{
    // console.log("===resp==",resp);
    updateTableData(resp);
  }).catch(err=>{
    // console.log("==err==",err);
    message.error((err&&(err.statusText||err.message||err.msg||err))||"数据错误!");
  });
}
function updateTableData(data){
  if(!myChart||isEmpty(data)) return;
  deviceCalculationList = data.deviceCalculationList||[];
  const opts = myChart.getOption(),
    data1:any = deviceCalculationList[0]||{parameterName:"",parameterValue:[]},
    data2:any=deviceCalculationList[1]||{parameterName:"",parameterValue:[]};
  // opts.title=data.querytime;
  // console.log("====",opts);
  opts.title[0].subtext = data.querytime;
  opts.xAxis[0].data = data.deviceSum;
  opts.series[0].name=data1.parameterName;
  opts.series[0].data = data1.parameterValue;
  opts.series[0].yAxisIndex = 0;

  opts.series[1].name=data2.parameterName;
  opts.series[1].data = data2.parameterValue;
  opts.series[1].yAxisIndex = 1;
  // console.log("===",ret.retResult.yczdParamListVo[0].parameterValue);
  myChart.setOption(opts);
  myChart.hideLoading();
}
onMounted(()=>{
  initChart();
  fetchChartData();
})
onUnmounted(() => {
  myChart?.clear();
  myChart?.dispose();
  myChart = null;
});


// const rankDate = ref<[Dayjs,Dayjs]>([dayjs().subtract(1,"week"),dayjs()]);
const rankDate = ref<any>(null);
function doSearch(){
  fetchChartData();
}
const listTable = ref([]);
function fetchTableData(){
  const date = rankDate.value,
    beginDate = date&&date[0].format("YYYY-MM-DD")||"",
    endDate = date&&date[1].format("YYYY-MM-DD")||"";
  storePressure.fetchTcodTableInfo({
    pageCode:"CduEfficiencyCyl",
    beginDate:beginDate,
    endDate:endDate,
  }).then(resp=>{
    listTable.value=resp;
  })
}
//fetchTableData();
</script>
<template>
<div class="page uopk">
  <div class="chart" id="chartId"></div>
  <div class="filter">
    <a-input type="text" style="width:140px;" placeholder="过滤装置"/>-
    <a-range-picker class="field" v-model:value="rankDate"></a-range-picker>
    <a-button class="field" @click.stop="doSearch">确定</a-button>
  </div>
  <a-tooltip placement="topLeft">
      <template #title>
         折线物理意义是排烟温度（℃）；柱状图物理意义是加热炉热效率（%）；鼠标悬停显示过剩氧含量1（%）。柱状图颜色：过剩氧含量1（%）在0-1.5%之间，黄色；在1.5-3%之间，蓝色；3%以上，橙色。     
      </template>
      <span class="chart-label-right">图形说明</span>
    </a-tooltip>
  <a-collapse v-model:activeKey="activeKey">
    <a-collapse-panel key="CduEfficiencyCyl" header="常压炉热效率综合分析相关参数一览表">
  <div class="tables">
    <CMPTable-Energy-Pressure-Tcod page-code="CduEfficiencyCyl" />
  </div>
  </a-collapse-panel>
  </a-collapse>
</div>
</template>
<style lang="less" scoped>
.page.uopk{
  >.chart{
    width:100%;height:600px;
  }
  >.filter{
    display:flex;
    justify-content: center;
    >.field{
      margin-left:10px;
      &:first-child{margin-left:0;}
    }
  }

  >.tables{
    margin-top:10px;
  }
  .chart-label-right {
  position: relative;
  width:100%;
  right: 10%;
  bottom: 50px;
  display: inline-block;
  //margin: 12px auto 0 auto;
  color: #070707;
  font-size: 14px;
  cursor: pointer;
  text-align: right;
  padding: 2px 10px;
  border-radius: 4px;
}
.chart-label-right.clicked {
  color: #999;            // 点击后变为灰色
}
}
</style>