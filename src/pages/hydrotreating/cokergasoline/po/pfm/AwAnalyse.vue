<script lang="ts" setup>
import { ref,reactive, nextTick, onMounted,onUnmounted } from "vue";
import { message } from "ant-design-vue";
import * as echarts from "echarts";
import dayjs from "dayjs";
import type {Dayjs} from "dayjs";
import {isEmpty,floor,toString} from "lodash";
import CMPTableCollapse from "@/components/hydro/coker/TableCollapse.vue";

import useHydroStore from "@/store/hydro.ts";
const storeHydro = useHydroStore();

import useInteractStore from "@/store/interact";
const storeInteract = useInteractStore();
let yczdParamListVo1=[],yczdParamListVo2=[],yczdParamListVo3=[];

document.title="酸性水分析-工艺防腐-生产运行概览-焦化汽油装置";
type EChartsOption = echarts.EChartsOption;
const option1: EChartsOption = reactive({
  title:{
    subtext:"",
    subtextStyle:{
      align:"right",
      lineHeight:16
    },
    right: 160,
    top: -10
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
      crossStyle: {
        color: '#999'
      }
    }
  },
  legend:{
    right:120,
    top: 30,
    itemGap:20
  },
  xAxis: [
    {
      type: 'category',
      axisPointer: {
        type: 'shadow'
      },
      axisTick:{
        alignWithLabel:true
      },
      axisLabel:{
        // rotate:270,
        formatter: function (value) {
          return value.split('').join('\n')
        }
      }
    }
  ],
  yAxis: [
    {
      type: 'value',
      min:0,
      //nameLocation:"center",
      //nameRotate:-270,
      //nameGap:40,
      name:"铁离子含量（mg/kg）",
      axiosLine:{
        show:true
      },
      axisLine: {
        show: true
      },
      axisTick: {
        show: true
      }
    },
  ],
  series: [
    {
      type: 'bar',
      data: [],
      barMaxWidth: 25,
      markLine:{
        symbol: 'none',
        silent:true,
        label:{
          color:"red",
          //position:"end",
          show:true,
          //formatter:"报警值：{c}"
          position:"insideEndBottom",
          formatter:"{c}"
        },
        lineStyle:{
          type:"solid",
          color:'red',
        },
        data:[
          {
            symbol:false,
            yAxis:"0",
          }
        ]
      }
    }
  ]
})
const option2: EChartsOption = reactive({
  title:{
    subtext:"",
    subtextStyle:{
      align:"right",
      lineHeight:16
    },
    right: 160,
    top: -10
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
      crossStyle: {
        color: '#999'
      }
    }
  },
  legend:{
    right:120,
    top: 30,
    itemGap:20
  },
  xAxis: [
    {
      type: 'category',
      axisPointer: {
        type: 'shadow'
      },
      axisTick:{
        alignWithLabel:true
      },
      axisLabel:{
        // rotate:270,
        formatter: function (value) {
          return value.split('').join('\n')
        }
      }
    }
  ],
  yAxis: [
    {
      type: 'value',
      name:"氯离子含量（mg/kg）",
      min:0,
      //nameLocation:"center",
      //nameRotate:-270,
      //nameGap:40,
      axiosLine:{
        show:true
      },
      axisLine: {
        show: true
      },
      axisTick: {
        show: true
      }
    },
  ],
  series: [
    {
      type: 'bar',
      data: [],
      barMaxWidth: 25,
      markLine:{
        symbol: 'none',
        silent:true,
        label:{
          color:"red",
          //position:"end",
          show:true,
          //formatter:"报警值：{c}"
          position:"insideEndBottom",
          formatter:"{c}"
        },
        lineStyle:{
          type:"solid",
          color:'red',
        },
        data:[
          {
            symbol:false,
            yAxis:"0"
          }
        ]
      }
    }
  ]
})
const option3: EChartsOption = reactive({
  title:{
    subtext:"",
    subtextStyle:{
      align:"right",
      lineHeight:16
    },
    right: 160,
    top: -10
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
      crossStyle: {
        color: '#999'
      }
    }
  },
  legend:{
    right:120,
    top: 30,
    itemGap:20
  },
  xAxis: [
    {
      type: 'category',
      axisPointer: {
        type: 'shadow'
      },
      axisTick:{
        alignWithLabel:true
      },
      axisLabel:{
        // rotate:270,
        formatter: function (value) {
          return value.split('').join('\n')
        }
      }
    }
  ],
  yAxis: [
    {
      type: 'value',
      name:"pH值",
      min:0,
      //nameLocation:"center",
      //nameRotate:-270,
      //nameGap:40,
      axiosLine:{
        show:true
      },
      axisLine: {
        show: true
      },
      axisTick: {
        show: true
      }
    },
  ],
  series: [
    {
      type: 'bar',
      data: [],
      barMaxWidth: 25,
      markArea:{
        data:[[{
          yAxis:"0",
        },{
          yAxis:"0"
        }]]
      }
    }
  ]
})
let myChart1: any = null;
let myChart2: any = null;
let myChart3: any = null;
function initChart(){
  const doc: any = document,
    chartDom1: any = doc.getElementById("chartId"),
    chartDom2: any = doc.getElementById("chartId1"),
    chartDom3: any = doc.getElementById("chartId2");

  if (!myChart1) {
    myChart1 = echarts.init(chartDom1);
    myChart1.setOption(option1);
  }
  if (!myChart2) {
    myChart2 = echarts.init(chartDom2);
    myChart2.setOption(option2);
  }
  if (!myChart3) {
    myChart3 = echarts.init(chartDom3);
    myChart3.setOption(option3);
  }
  myChart1.on("click",(params)=>{
    // console.log("====click===",params,"====",deviceCalculationList);
    const dataIndex = params.dataIndex,
      seriesIndex = params.seriesIndex,
      data = yczdParamListVo1[seriesIndex]||{deviceCode:[]},
      listDeviceCode = data&&data.deviceCode||[],
      deviceCode = listDeviceCode[dataIndex]||"",
      deviceName = params.name,
      date = rankDate.value,
      beginDate = date&&date[0].format("YYYY-MM-DD")||"",
      endDate = date&&date[1].format("YYYY-MM-DD")||"";
      // console.log("====click===",dataIndex,"====",seriesIndex,"====",deviceCode,"====",data);
      storeInteract.showChartDv({
        deviceType:"hydro",
	deviceType2:"Cokergasoline",
        deviceCode,
        deviceName,
        beginDate,
        endDate,
        pageCode:"AWAnalysePop",
      });
  })
  myChart2.on("click",(params)=>{
    // console.log("====click===",params,"====",deviceCalculationList);
    const dataIndex = params.dataIndex,
      seriesIndex = params.seriesIndex,
      data = yczdParamListVo2[seriesIndex]||{deviceCode:[]},
      listDeviceCode = data&&data.deviceCode||[],
      deviceCode = listDeviceCode[dataIndex]||"",
      deviceName = params.name,
      date = rankDate.value,
      beginDate = date&&date[0].format("YYYY-MM-DD")||"",
      endDate = date&&date[1].format("YYYY-MM-DD")||"";
      // console.log("====click===",dataIndex,"====",seriesIndex,"====",deviceCode,"====",data);
      storeInteract.showChartDv({
        deviceType:"hydro",
	deviceType2:"Cokergasoline",
        deviceCode,
        deviceName,
        beginDate,
        endDate,
        pageCode:"AWAnalysePop",
      });
  })
  myChart3.on("click",(params)=>{
    // console.log("====click===",params,"====",deviceCalculationList);
    const dataIndex = params.dataIndex,
      seriesIndex = params.seriesIndex,
      data = yczdParamListVo3[seriesIndex]||{deviceCode:[]},
      listDeviceCode = data&&data.deviceCode||[],
      deviceCode = listDeviceCode[dataIndex]||"",
      deviceName = params.name,
      date = rankDate.value,
      beginDate = date&&date[0].format("YYYY-MM-DD")||"",
      endDate = date&&date[1].format("YYYY-MM-DD")||"";
      // console.log("====click===",dataIndex,"====",seriesIndex,"====",deviceCode,"====",data);
      storeInteract.showChartDv({
        deviceType:"hydro",
	deviceType2:"Cokergasoline",
        deviceCode,
        deviceName,
        beginDate,
        endDate,
        pageCode:"AWAnalysePop",
      });
  })

  nextTick(() => {
    window.addEventListener('resize', function () {
      myChart1.resize();
      myChart2.resize();
      myChart3.resize();
    })
  })
}
function fetchChartData1(){
  myChart1.showLoading();
  const date = rankDate.value,
    beginDate = date&&date[0].format("YYYY-MM-DD")||"",
    endDate = date&&date[1].format("YYYY-MM-DD")||"";
  storeHydro.fetchPOChartData({
    pageCode:"PHValue_2",
    ismacd:1,
    beginDate:beginDate,
    endDate:endDate,
    deviceType:"Cokergasoline"
  }).then((resp)=>{
    // console.log("===ph_value=resp==",resp);
    updateTableData1(resp);
  }).catch(err=>{
    // console.log("==err==",err);
    message.error((err&&(err.statusText||err.message||err.msg||err))||"数据错误!");
  });
}
function fetchChartData2(){
  myChart2.showLoading();
  const date = rankDate2.value,
    beginDate = date&&date[0].format("YYYY-MM-DD")||"",
    endDate = date&&date[1].format("YYYY-MM-DD")||"";
  storeHydro.fetchPOChartData({
    pageCode:"PHValue_1",
    ismacd:1,
    beginDate:beginDate,
    endDate:endDate,
    deviceType:"Cokergasoline"
  }).then((resp)=>{
    // console.log("===ph_value=resp==",resp);
    updateTableData2(resp);
  }).catch(err=>{
    // console.log("==err==",err);
    message.error((err&&(err.statusText||err.message||err.msg||err))||"数据错误!");
  });
}
function fetchChartData3(){
  myChart3.showLoading();
  const date = rankDate3.value,
    beginDate = date&&date[0].format("YYYY-MM-DD")||"",
    endDate = date&&date[1].format("YYYY-MM-DD")||"";
  storeHydro.fetchPOChartData({
    pageCode:"PHValue_3",
    beginDate:beginDate,
    endDate:endDate,
    ismacd:1,
    deviceType:"Cokergasoline"
  }).then((resp)=>{
    // console.log("===ph_value=resp==",resp);
    updateTableData3(resp);
  }).catch(err=>{
    // console.log("==err==",err);
    message.error((err&&(err.statusText||err.message||err.msg||err))||"数据错误!");
  });
}
function updateTableData1(data){
  if(!myChart1||isEmpty(data)) return;
  yczdParamListVo1 = data.yczdParamListVo||[];
  const opts = myChart1.getOption(),
    data1 = yczdParamListVo1[0]||{parameterName:"",parameterValue:[]};

  opts.title[0].subtext = data.querytime+"\n红线代表报警值";
  opts.xAxis[0].data = data.deviceSum;
  opts.series[0].name=data1.parameterName;
  opts.series[0].data= data1.parameterValue;
  opts.series[0].markLine.data[0].yAxis = toString(floor(data1.macdValue[0],3));
  // console.log("===",ret.retResult.yczdParamListVo[0].parameterValue);
  myChart1.setOption(opts);
  myChart1.hideLoading();
}
function updateTableData2(data){
  if(!myChart2||isEmpty(data)) return;
  yczdParamListVo2 = data.yczdParamListVo||[];
  const opts = myChart2.getOption(),
    data1 = yczdParamListVo2[0]||{parameterName:"",parameterValue:[]};

  opts.title[0].subtext = data.querytime+"\n红线代表报警值";
  opts.xAxis[0].data = data.deviceSum;
  opts.series[0].name=data1.parameterName;
  opts.series[0].data= data1.parameterValue;
  opts.series[0].markLine.data[0].yAxis = toString(floor(data1.macdValue[0],3));
  // console.log("===",ret.retResult.yczdParamListVo[0].parameterValue);
  myChart2.setOption(opts);
  myChart2.hideLoading();
}
function updateTableData3(data){
  if(!myChart3||isEmpty(data)) return;
  yczdParamListVo3 = data.yczdParamListVo||[];
  const opts = myChart3.getOption(),
    data1 = yczdParamListVo3[0]||{parameterName:"",parameterValue:[]};

  opts.title[0].subtext = data.querytime+"\n5.5-9表示pH适宜区间";
  opts.xAxis[0].data = data.deviceSum;
  opts.series[0].name=data1.parameterName;
  opts.series[0].data= data1.parameterValue;
  // console.log("====",opts.series[0].markArea.data);
  opts.series[0].markArea.data[0][0].yAxis =9;
  opts.series[0].markArea.data[0][1].yAxis = 5.5;
  // console.log("===",ret.retResult.yczdParamListVo[0].parameterValue);
  myChart3.setOption(opts);
  myChart3.hideLoading();
}
onMounted(()=>{
  initChart();
  fetchChartData1();
  fetchChartData2();
  fetchChartData3();
})
onUnmounted(() => {
  myChart1?.clear();
  myChart1?.dispose();
  myChart1 = null;

  myChart2?.clear();
  myChart2?.dispose();
  myChart2 = null;

  myChart3?.clear();
  myChart3?.dispose();
  myChart3 = null;
});


// const rankDate = ref<[Dayjs,Dayjs]>([dayjs().subtract(1,"week"),dayjs()]);
const rankDate = ref<any>(null);
const rankDate2 = ref<any>(null);
const rankDate3 = ref<any>(null);
function doSearch(){
  fetchChartData1();
}
function doSearch2(){
  fetchChartData2();
}
function doSearch3(){
  fetchChartData3();
}
</script>
<template>
<div class="page ph_value">
  <div class="chart" id="chartId"></div>
  <div class="filter">
    <a-range-picker class="field"  v-model:value="rankDate"></a-range-picker>
    <a-button class="field" @click.stop="doSearch">确定</a-button>
  </div>
  <div class="chart2" id="chartId1"></div>
  <div class="filter">
    <a-range-picker class="field"  v-model:value="rankDate2"></a-range-picker>
    <a-button class="field" @click.stop="doSearch2">确定</a-button>
  </div>
  <div class="chart3" id="chartId2"></div>
  <div class="filter">
    <a-range-picker class="field"  v-model:value="rankDate3"></a-range-picker>
    <a-button class="field" @click.stop="doSearch3">确定</a-button>
  </div>
  <div class="tables">
    <CMPTable-Collapse />
  </div>
</div>
</template>
<style lang="less" scoped>
.page.ph_value{
  >.chart{
    width:100%;height:600px;
  }
  >.chart2{
    width:100%;height:600px;
  }
  >.chart3{
    width:100%;height:600px;
  }
  >.filter{
    display:flex;
    justify-content: center;
    >.field{
      margin-left:10px;
      &:first-child{margin-left:0;}
    }
  }

  >.tables{
    margin-top:10px;
  }
}
</style>