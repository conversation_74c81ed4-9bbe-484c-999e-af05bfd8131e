import {defineStore} from "pinia";
import szorbApi from "@/api/szorb";
import {normalReq} from "./request.ts";
import {addUrlQuery} from "@/utils/index";
import {has} from "lodash";
import catalyticApi from "@/api/catalytic.js";
export default defineStore("useMTBEStore",{
  state:()=>{
    return {
    }
  },
  getters:{

  },
  actions:{
    fetchPOChartData(form){
      let _url = szorbApi.url_po_chart;
      if(!_url) return;
      _url = addUrlQuery(_url,"pageencode",form.pageCode);
      _url = addUrlQuery(_url,"startTime",form.beginDate);
      _url = addUrlQuery(_url,"endTime",form.endDate);
      _url = addUrlQuery(_url,"ismacd",form.ismacd);
      _url = addUrlQuery(_url,"ismacdset",form.ismacdset);
      _url = addUrlQuery(_url,"type",form.type);
      _url = addUrlQuery(_url,"timetype",form.timetype);
      _url = addUrlQuery(_url,"dtype",form.dtype);
      
      return normalReq.get(_url);
    },
    fetchPOTableInfo(form){
      let _url = szorbApi.url_po_table;
      _url = addUrlQuery(_url,"pageencode",form.pageCode);
      return normalReq.get(_url);
    },
    fetchTcodData(form){
      let _url = szorbApi.url_tcod_data;
      _url = addUrlQuery(_url,"chartCode",form.chartCode);
      _url = addUrlQuery(_url,"starttime",form.beginDate);
      _url = addUrlQuery(_url,"endtime",form.endDate);
      _url = addUrlQuery(_url,"code",form.code);
      return normalReq.get(_url);
    },
    fetchTaodAlarmTotal(form){
      let _url = szorbApi.url_taod_alarm_total;
      _url = addUrlQuery(_url,"starttime",form.beginDate);
      _url = addUrlQuery(_url,"endtime",form.endDate);
      _url = addUrlQuery(_url,"device",form.device);
      return normalReq.get(_url);
    },
    fetchTaodAlarm(form){
      const urlMap = {
        taod_alarm:szorbApi.url_taod_alarm,
        taod_list_show:szorbApi.url_taod_get_list_show,
        taod_unify_query:szorbApi.url_taod_get_unify_query
      }
      let _url = urlMap[form.pageCode];
      if(!_url) return;
      _url = addUrlQuery(_url,"pageencode",form.pageCode);
      _url = addUrlQuery(_url,"startTime",form.beginDate);
      _url = addUrlQuery(_url,"endTime",form.endDate);
      _url = addUrlQuery(_url,"ismacd",form.ismacd);
      _url = addUrlQuery(_url,"ismacdset",form.ismacdset);
      _url = addUrlQuery(_url,"type",form.type);
      _url = addUrlQuery(_url,"timetype",form.timetype);
      _url = addUrlQuery(_url,"dtype",form.dtype);
      _url = addUrlQuery(_url,"atype",form.atype);
      _url = addUrlQuery(_url,"device",form.device);
      
      return normalReq.get(_url);
    },
    fetchTaodCompanyList(){
      return normalReq.get(szorbApi.url_taod_company_list);
    },
    doTaodCalculateA(form){
      let _url = szorbApi.url_taod_calculatea;
      _url = addUrlQuery(_url,"A",form.param1);
      _url = addUrlQuery(_url,"P",form.param2);
      _url = addUrlQuery(_url,"B",form.param3);
      _url = addUrlQuery(_url,"C",form.param4);
      _url = addUrlQuery(_url,"T",form.param5);
      _url = addUrlQuery(_url,"D",form.param6);
      _url = addUrlQuery(_url,"E",form.param6);
      return normalReq.get(_url);
    },
    doTaodCalculateB(form){
      let _url = szorbApi.url_taod_calculateb;
      _url = addUrlQuery(_url,"P",form.param1);
      _url = addUrlQuery(_url,"P2",form.param2);
      _url = addUrlQuery(_url,"B",form.param3);
      return normalReq.get(_url);
    },
    doTaodCalculateC(form){
      let _url = szorbApi.url_taod_calculatec;
      _url = addUrlQuery(_url,"A",form.param1);
      _url = addUrlQuery(_url,"P",form.param2);
      _url = addUrlQuery(_url,"B",form.param3);
      _url = addUrlQuery(_url,"C",form.param4);
      _url = addUrlQuery(_url,"T",form.param5);
      _url = addUrlQuery(_url,"D",form.param6);
      _url = addUrlQuery(_url,"E",form.param6);
      return normalReq.get(_url);
    },
    fetchTaodPfmList(form?:any){
      let _url = szorbApi.url_taod_get_pfm;
      _url = addUrlQuery(_url,"qy",form.qy);
      _url = addUrlQuery(_url,"nodeid",form.nodeid);
      _url = addUrlQuery(_url,"pageNum",form.page);
      _url = addUrlQuery(_url,"pageSize",form.pageSize);
      if(has(form,"type")){
        if(form.type==1){
          _url = addUrlQuery(_url,"type",1); // 1 生产数据点 0 质量数据点
        }else{
          _url = addUrlQuery(_url,"type",0); // 1 生产数据点 0 质量数据点
        }
      }
      _url = addUrlQuery(_url,"pointCode",form.pointCode);
      _url = addUrlQuery(_url,"describe",form.describe);
      _url = addUrlQuery(_url,"device",form.device);
      return normalReq.get(_url);
    },

    fetchTaodPfmParamList(form?:any){
      let _url = szorbApi.url_taod_get_pfmParams_list;
      if(form){
        _url = addUrlQuery(_url,"type",form.type);
        _url = addUrlQuery(_url,"pointCode",form.pointCode);
        _url = addUrlQuery(_url,"describe",form.describe);
        _url = addUrlQuery(_url,"device",form.device);
      }
      return normalReq.get(_url);
    },



    fetchTaodPfmUnifyChart(form){
      let _url = szorbApi.url_taod_pfm_unify_chart;
      if(form){
        _url = addUrlQuery(_url,"pointCodes",form.points);
        _url = addUrlQuery(_url,"startTime",form.beginDate);
        _url = addUrlQuery(_url,"endTime",form.endDate);
      }
      return normalReq.get(_url);
    },
    fetchTaodReport(form){
      let _url = szorbApi.url_taod_report;
      if(form){
        _url = addUrlQuery(_url,"nodeid",form.nodeid);
      }
      return normalReq.get(_url);
    },
    fetchTaodDailyReport(){
      let _url = szorbApi.url_taod_daily_report;
      return normalReq.get(_url);
    },
    fetchTaodSzorbTable(form){
      let _url = szorbApi.url_taod_szorb_table;
      if(form){
        _url = addUrlQuery(_url,"pageencode",form.pageCode);
        _url = addUrlQuery(_url,"index",form.index);
        _url = addUrlQuery(_url,"starttime",form.beginDate);
        _url = addUrlQuery(_url,"endtime",form.endDate);
      }
      return normalReq.get(_url);
    },
    fetchTaodDailyReportData(form){
      let _url = szorbApi.url_taod_daily_report_data;
      if(form){
        _url = addUrlQuery(_url,"pageencode",form.pageCode);
        _url = addUrlQuery(_url,"devicetype",form.deviceType);
        _url = addUrlQuery(_url,"starttime",form.beginDate);
        _url = addUrlQuery(_url,"endtime",form.endDate);
      }
      return normalReq.get(_url);
    }
  }
});