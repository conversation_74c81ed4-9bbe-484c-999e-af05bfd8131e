const path = require('path')
const appConfig = require('./src/app.config')

const CompressionPlugin = require('compression-webpack-plugin')
const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin

//gzip 压缩
const compressionPlugin = new CompressionPlugin({
  algorithm: 'gzip', // 使用gzip压缩
  test: /\.js$|\.html$|\.css$/, // 匹配文件名
  filename: '[path].gz[query]', // 压缩后的文件名(保持原文件名，后缀加.gz)
  minRatio: 1, // 压缩率小于1才会压缩
  threshold: 10240, // 对超过10k的数据压缩
  deleteOriginalAssets: false // 是否删除未压缩的源文件，谨慎设置，如果希望提供非gzip的资源，可不设置或者设置为false（比如删除打包后的gz后还可以加载到原始资源文件）
})

//分包处理
const splitChunkOption = {
  chunks: 'all',
  maxSize: 2048000,
  cacheGroups: {
    vendors: {
      name: 'chunk-vendors', // 抽离的模块名
      test: /[\\/]node_modules[\\/]/, // 匹配模块
      priority: 10, // 打包优先级
      chunks: 'initial' // 代码块类型
    },
    echarts: {
      name: 'chunk-echarts',
      priority: 20,
      test: /[\\/]node_modules[\\/]echarts(.*)/
    },
    elementUI: {
      name: 'chunk-elementUI',
      priority: 20,
      test: /[\\/]node_modules[\\/]element-ui(.*)/
    },
    wangEditor: {
      name: 'chunk-wangEditor',
      priority: 15,
      test: /[\\/]node_modules[\\/]wangeditor(.*)/
    },
    mockJS: {
      name: 'chunk-mockJS',
      priority: 15,
      test: /[\\/]node_modules[\\/]mockjs(.*)/
    },
    commons: {
      name: 'chunk-commons',
      minChunks: 2, // 最小引用的次数
      priority: 5,
      chunks: 'initial',
      reuseExistingChunk: true // 开启复用
    }
  }
}

module.exports = {
  publicPath: process.env.NODE_ENV !== 'development' ? `/${appConfig.projectName}/` : '/',
  parallel: require('os').cpus().length > 1, // 构建时开启多进程处理babel编译
  productionSourceMap: process.env.NODE_ENV === 'development', //是否开启source map
  outputDir: appConfig.projectName, //打包名称
  configureWebpack: {
    devtool: process.env.NODE_ENV === 'development' ? 'source-map' : undefined,
    module: {
      rules: [
        {
          test: /\.mjs$/,
          include: /node_modules/,
          type: 'javascript/auto'
        }
      ]
    },
    plugins: [compressionPlugin]
  },
  chainWebpack: config => {
    // 移除 prefetch 插件
    config.plugins.delete('prefetch')
    // 设置 meta description
    config.plugin('html').tap(args => {
      let { description, title, ico } = appConfig
      args[0].meta = { description, title, ico }
      return args
    })
    if (process.env.NODE_ENV !== 'development') {
      if (process.env.NODE_ENV === 'sit') {
        //增加打包代码分析
        config.plugin('webpack-bundle-analyzer').use(
          new BundleAnalyzerPlugin({
            analyzerHost: '127.0.0.1',
            analyzerPort: 10086
          })
        )
      }
      //分包
      config.optimization.splitChunks(splitChunkOption)

      //去掉生产环境log
      config.optimization.minimizer('terser').tap(args => {
        args[0].terserOptions.compress.drop_console = true
        args[0].terserOptions.compress.drop_debugger = true
        args[0].terserOptions.compress.pure_funcs = ['console.log']
        args[0].terserOptions.output = {
          comments: false
        }
        return args
      })
    }

    // 设置别名
    config.resolve.alias.set('@', path.join(__dirname, 'src')).set('_', path.join(__dirname, 'bee-inner-module/_modules'))
  },
  // 依赖编译 - es6语法降级兼容IE
  transpileDependencies: ['@vue', 'pinia', 'echarts', 'element-ui', '@iconify', 'vue-grid-layout', 'html2canvas', 'pinia-plugin-persist'],
  // 将组件中的 CSS 提取至一个独立的 CSS 文件 (而不是动态注入到 JavaScript 中的 inline 代码)
  css: {
    extract: process.env.NODE_ENV !== 'development' ? true : false,
    loaderOptions: {}
  },
  // 导入全局less变量
  pluginOptions: {
    'style-resources-loader': {
      preProcessor: 'less',
      patterns: [
        // 全局less变量
        path.resolve(__dirname, './bee-inner-module/_modules/_styles/common/variable.less'),
        path.resolve(__dirname, './bee-inner-module/_modules/_styles/common/flex.less')
      ]
    }
  },
  // error 将校验错误显示在浏览器里的浮层中, 关闭：false
  lintOnSave: 'error',
  devServer: {
    host: '0.0.0.0',
    port: 8889,
    open: true,
    overlay: {
      warnings: true,
      errors: true
    },
    proxy: {
      '/permission': {
        target: process.env.VUE_APP_PMS_URL,
        changeOrigin: true,
        ws: true,
        pathRewrite: {
          '^/permission': '/permission'
        }
      },
      '/pms': {
        target: process.env.VUE_APP_PMS_URL,
        changeOrigin: true,
        ws: true,
        pathRewrite: {
          '^/pms': '/pms'
        }
      },
      '/base': {
        target: process.env.VUE_APP_BASE_URL,
        changeOrigin: true,
        ws: true,
        pathRewrite: {
          '^/base': '/base'
        }
      },
      '/yczd': {
        target: process.env.VUE_APP_YCZD_URL,
        changeOrigin: true,
        ws: true,
        pathRewrite: {
          '^/yczd': '/yczd'
        }
      },
      '/WordTempelet': {
        target: process.env.VUE_APP_WordTempelet_URL,
        changeOrigin: true,
        ws: true,
        pathRewrite: {
          '^/WordTempelet': '/WordTempelet'
        }
      }
    }
  }
}
