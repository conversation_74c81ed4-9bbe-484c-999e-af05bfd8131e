name: Blog CI/CD

on:
  push:
    branches:
      - master  # 只在master上push触发部署
    paths-ignore:   # 下列文件的变更不触发部署，可以自行添加
      - README.md
      - LICENSE

jobs:
  build-production:

    runs-on: ubuntu-latest   # 使用ubuntu系统镜像运行自动化脚本(温馨提示：和你所使用的操作系统及服务器系统无关，我本地用的windows10,云服务器用的centos)
    strategy:
      matrix:
        node-version: [12.x] # 配置所需node版本
    steps:  # 自动化步骤
    - uses: actions/checkout@v2   # 第一步，检出仓库副本

    - name: Install dependencies  # 第二步，安装依赖
      run: npm install

    - name: Build                 # 第三步，打包代码
      run: npm run build 

    - name: Deploy to Server      # 第四步，rsync推送文件
      uses: AEnterprise/rsync-deploy@v1.0  # 使用别人包装好的步骤镜像
      env:
        # DEPLOY_KEY: ${{ secrets.DEPLOY_KEY }}   # 引用配置，SSH私钥
        ARGS: -avz --delete --exclude='*.pyc'   # rsync参数，排除.pyc文件
        SERVER_PORT: '22'  # SSH端口
        FOLDER: ./build/*  # 要推送的文件夹，路径相对于代码仓库的根目录
        SERVER_IP:**************  # 引用配置，服务器的host名（IP或者域名domain.com）
        USERNAME: iaas  # 引用配置，服务器登录名
        SERVER_DESTINATION: /usr/local/nginx/html # 部署到目标文件夹
