<template>
  <div class="ele-switch">
    <div
      :class="['el-switch-btn', { 'el-is-checked': getValue }, { 'el-is-disabled': getDisabled }]"
      ref="$elSwitch"
      v-bind="$attrs"
      :disabled="getDisabled"
      :name="propField"
      :active-text="getActiveText"
      :inactive-text="getInactiveText"
      @click="handleChange"
    >
      <span></span>
    </div>
    <span :class="['el-switch-text', { 'el-is-checked-text': getValue }, { 'el-is-disabled': getDisabled }]">{{
      getValue ? getActiveText : getInactiveText
    }}</span>
  </div>
</template>

<script>
  import MixinFormComponents from './MixinFormComponents'
  export default {
    name: 'VGenerateFormSwitch',
    mixins: [MixinFormComponents],
    computed: {
      //获取当前激活的text
      getActiveText() {
        return this.$t._.get(this.formOpts, 'elUI.activeText', false) || ''
      },
      //获取当前未激活的text
      getInactiveText() {
        return this.$t._.get(this.formOpts, 'elUI.inactiveText', false) || ''
      },
      //获取值
      getValue() {
        return this.formModel[this.propField] || this.formModel[this.column.prop]
      },
      // 获取是否禁用
      getDisabled() {
        return this.$t._.get(this.formOpts, 'elUI.disabled', false) || false
      }
    },
    methods: {
      //更新事件
      handleChange() {
        // 禁止状态过滤
        if (this.getDisabled) {
          return
        }
        let value = this.getValue
        value ? value-- : value++
        //强制更新
        this.$set(this.formModel, this.propField, value)
        const formOpts = this.column.formOpts
        if (typeof formOpts.changeHandler === 'function') {
          formOpts.changeHandler.call(this, this.columns, this.column, this.formModel, value)
        }
        this.$emit('on-switch-change', this.columns, this.column, this.formModel, value)
      }
    }
  }
</script>

<style lang="less" scoped>
  .ele-switch {
    display: flex;
    align-items: center;
    .el-switch-btn {
      display: inline-flex;
      align-items: center;
      position: relative;
      font-size: 14px;
      line-height: 20px;
      height: 20px;
      vertical-align: middle;
      cursor: pointer;
      &.el-is-checked {
        > span {
          padding: 0 22px 0 6px;
          margin: 0;
          position: relative;
          outline: 0;
          border-radius: 10px;
          box-sizing: border-box;
          background: #dcdfe6;
          transition: border-color 0.3s, background-color 0.3s;
          vertical-align: middle;
          border-color: var(--primary-color);
          background-color: var(--primary-color);
          &::after {
            left: 100%;
            margin-left: -18px;
          }
        }
      }
      > span {
        background-color: #a8a8a8;
        padding: 0 6px 0 22px;
        border-radius: 10px;
        color: #fff;
        min-width: 36px;
        height: 100%;
        width: 40px;
        &::after {
          content: '';
          position: absolute;
          top: 2px;
          left: 2px;
          border-radius: 100%;
          transition: all 0.3s;
          width: 16px;
          height: 16px;
          background-color: #fff;
        }
      }
    }
    .el-switch-text {
      display: inline-flex;
      justify-items: center;
      color: #c0c4cc;
      margin: 0 0 0 5px;
      &.el-is-checked-text {
        color: var(--primary-color);
      }
    }
    .el-is-disabled {
      cursor: not-allowed;
      opacity: 0.7;
    }
  }
</style>
