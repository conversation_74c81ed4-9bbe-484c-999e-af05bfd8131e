<script>
import dashboard from "@/store/dashboard";
const storeDashboard = dashboard();
import {forEach,map,find,set,assign,get} from "lodash";
export default {
  data(){
    return {
      currentTab:"1",
      listData:[]
    }
  },
  created(){
    this.fetchDeviceStop();
  },
  methods:{
    doChangeTab(tab) {
      this.currentTab = tab;
      this.fetchDeviceStop();
    },
    getTotalList(info){
      const obj = {};
      map(info,(v,key)=>{
        if(key==="companyName") return;
        const green = find(v,{type:'0'}),
          yellow = find(v,{type:'1'}),
          red = find(v,{type:'2'});
        set(obj,key,{
          green:green&&green.tip||"",
          yellow:yellow&&yellow.tip||"",
          red:red&&red.tip||""
        });
      })
      // console.log("=====obj==",obj);
      return obj;
    },
    getCompanyList(info,infoTotal){
      const totalInfo = assign({},{project:"总计"},this.getTotalList(infoTotal));
      const obj = {};
      map(info,(v,key)=>{
        if(key==="companyName") return;
        // console.log("======v===",key,"===",v,"===",v,totalInfo);
        // 给行增加total数据
        set(obj,key,assign({},{type:(v&&v.type||-1)},{total:get(totalInfo,key.replace(/\d$/,""))}));
        // console.log("====obj===",obj,"===",v);
      })
      return obj;
    },
    initListData(data){
      this.listData=[];

      let infoTotal = data.hjData||{},
        list = data.resultList||[];

      this.listData.push(assign({},{project:"总计"},this.getCompanyList(list[0],infoTotal)));
      forEach(list,(v)=>{
        this.listData.push(assign({},{project:v.companyName},this.getCompanyList(v,infoTotal)));
      })
      // console.log("====init data==",this.listData);
    },
    fetchDeviceStop(){
      if(this.isFetching) return;
      this.isFetching = true;
      
      storeDashboard.fetchDeviceStop(this.currentTab).then((resp)=>{
        const respData = resp.retResult||[];
        // console.log("===resp=stop=",respData);
        this.initListData(respData);
        // console.log("==data==",this.listData);
      }).finally(()=>{
        this.isFetching = false;
      });
    },
    arraySpanMethod({row,column,rowIndex,columnIndex}){
      // console.log("====",row,rowIndex,column,columnIndex)
      let arr = [1,1];
      if(rowIndex===0){
        switch(columnIndex){
          case 1:
          case 11:{
            arr= [1,2];
            break;
          }
          case 5:{
            arr= [1,6];
          }break;
          case 15:{
            arr= [1,3];
          }break;
          case 2:
          case 6:
          case 7:
          case 8:
          case 9:
          case 10:
          case 12:
          case 16:
          case 17:{
            arr =[0,0];
          }
        }
      }
      return arr;
    }
  }
}
</script>
<template>
<div class="panel device_stop">
  <div class="head">
    <h4 class="title">装置开停工-览表</h4>
  </div>
  <div class="content">
    <div class="legends">
      <div class="legend normal">正常</div>
      <div class="legend warn">中断</div>
      <div class="legend stop">停工</div>
    </div>
    <ul class="tabs">
      <li class="tab" :class="{'current':currentTab==='1'}" @click.stop="doChangeTab('1')">临氢装置</li>
      <li class="tab" :class="{'current':currentTab==='2'}" @click.stop="doChangeTab('2')">非临氢装置</li>
      <li class="tab" :class="{'current':currentTab==='3'}" @click.stop="doChangeTab('3')">硫磺</li>
    </ul>
    <el-table 
      :span-method="arraySpanMethod"
      stripe
      :loading="isFetching"
      :data="listData" 
      class="table">
      <el-table-column fixed prop='project' width="60"></el-table-column>
      <el-table-column label="加裂" align="center">
        <el-table-column prop='hydrogenation1' label="1#" align="center">
          <template slot-scope="scope">
            <div v-if="scope.$index===0">
              <div class="tips">
                <div class="icon green">{{ scope.row.hydrogenation1.total.green }}</div>
                <div class="icon yellow">{{ scope.row.hydrogenation1.total.yellow }}</div>
                <div class="icon red">{{ scope.row.hydrogenation1.total.red }}</div>
              </div>
            </div>
            <div v-else>
              <div class="icon" :class="{
                'red':scope.row.hydrogenation1.type==2,
                'green':scope.row.hydrogenation1.type==0,
                'yellow':scope.row.hydrogenation1.type==1}"></div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop='hydrogenation2' label="2#" align="center">
          <template slot-scope="scope">
            <div v-if="scope.$index===0">
              <div class="tips">
                <div class="icon green">{{ scope.row.hydrogenation2.total.green }}</div>
                <div class="icon yellow">{{ scope.row.hydrogenation2.total.yellow }}</div>
                <div class="icon red">{{ scope.row.hydrogenation2.total.red }}</div>
              </div>
            </div>
            <div v-else>
              <div class="icon" :class="{
                'red':scope.row.hydrogenation2.type==2,
                'green':scope.row.hydrogenation2.type==0,
                'yellow':scope.row.hydrogenation2.type==1}"></div>
            </div>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="渣油" align="center">
        <el-table-column width="100" prop='residualoil' label="1#" align="center">
          <template slot-scope="scope">
            <div v-if="scope.$index===0">
              <div class="tips">
                <div class="icon green">{{ scope.row.residualoil.total.green }}</div>
                <div class="icon yellow">{{ scope.row.residualoil.total.yellow }}</div>
                <div class="icon red">{{ scope.row.residualoil.total.red }}</div>
              </div>
            </div>
            <div v-else>
              <div class="icon" :class="{
                'red':scope.row.residualoil.type==2,
                'green':scope.row.residualoil.type==0,
                'yellow':scope.row.residualoil.type==1}"></div>
            </div>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="蜡油" align="center">
        <el-table-column width="100" prop='hydroWax1' label="1#" align="center">
          <template slot-scope="scope">
            <div v-if="scope.$index===0">
              <div class="tips">
                <div class="icon green">{{ scope.row.hydroWax1.total.green }}</div>
                <div class="icon yellow">{{ scope.row.hydroWax1.total.yellow }}</div>
                <div class="icon red">{{ scope.row.hydroWax1.total.red }}</div>
              </div>
            </div>
            <div v-else>
              <div class="icon" :class="{
                'red':scope.row.hydroWax1.type==2,
                'green':scope.row.hydroWax1.type==0,
                'yellow':scope.row.hydroWax1.type==1}"></div>
            </div>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="柴油" align="center">
        <el-table-column prop='dieseloil1' label="1#" align="center">
          <template slot-scope="scope">
            <div v-if="scope.$index===0">
              <div class="tips">
                <div class="icon green">{{ scope.row.dieseloil1.total.green }}</div>
                <div class="icon yellow">{{ scope.row.dieseloil1.total.yellow }}</div>
                <div class="icon red">{{ scope.row.dieseloil1.total.red }}</div>
              </div>
            </div>
            <div v-else>
              <div class="icon" :class="{
                'red':scope.row.dieseloil1.type==2,
                'green':scope.row.dieseloil1.type==0,
                'yellow':scope.row.dieseloil1.type==1}"></div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop='dieseloil2' label="2#" align="center">
          <template slot-scope="scope">
            <div v-if="scope.$index===0">
              <div class="tips">
                <div class="icon green">{{ scope.row.dieseloil2.total.green }}</div>
                <div class="icon yellow">{{ scope.row.dieseloil2.total.yellow }}</div>
                <div class="icon red">{{ scope.row.dieseloil2.total.red }}</div>
              </div>
            </div>
            <div v-else>
              <div class="icon" :class="{
                'red':scope.row.dieseloil2.type==2,
                'green':scope.row.dieseloil2.type==0,
                'yellow':scope.row.dieseloil2.type==1}"></div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop='dieseloil3' label="3#" align="center">
          <template slot-scope="scope">
            <div v-if="scope.$index===0">
              <div class="tips">
                <div class="icon green">{{ scope.row.dieseloil3.total.green }}</div>
                <div class="icon yellow">{{ scope.row.dieseloil3.total.yellow }}</div>
                <div class="icon red">{{ scope.row.dieseloil3.total.red }}</div>
              </div>
            </div>
            <div v-else>
              <div class="icon" :class="{
                'red':scope.row.dieseloil3.type==2,
                'green':scope.row.dieseloil3.type==0,
                'yellow':scope.row.dieseloil3.type==1}"></div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop='dieseloil4' label="4#" align="center">
          <template slot-scope="scope">
            <div v-if="scope.$index===0">
              <div class="tips">
                <div class="icon green">{{ scope.row.dieseloil4.total.green }}</div>
                <div class="icon yellow">{{ scope.row.dieseloil4.total.yellow }}</div>
                <div class="icon red">{{ scope.row.dieseloil4.total.red }}</div>
              </div>
            </div>
            <div v-else>
              <div class="icon" :class="{
                'red':scope.row.dieseloil4.type==2,
                'green':scope.row.dieseloil4.type==0,
                'yellow':scope.row.dieseloil4.type==1}"></div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop='dieseloil5' label="5#" align="center">
          <template slot-scope="scope">
            <div v-if="scope.$index===0">
              <div class="tips">
                <div class="icon green">{{ scope.row.dieseloil5.total.green }}</div>
                <div class="icon yellow">{{ scope.row.dieseloil5.total.yellow }}</div>
                <div class="icon red">{{ scope.row.dieseloil5.total.red }}</div>
              </div>
            </div>
            <div v-else>
              <div class="icon" :class="{
                'red':scope.row.dieseloil5.type==2,
                'green':scope.row.dieseloil5.type==0,
                'yellow':scope.row.dieseloil5.type==1}"></div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop='dieseloil6' label="6#" align="center">
          <template slot-scope="scope">
            <div v-if="scope.$index===0">
              <div class="tips">
                <div class="icon green">{{ scope.row.dieseloil6.total.green }}</div>
                <div class="icon yellow">{{ scope.row.dieseloil6.total.yellow }}</div>
                <div class="icon red">{{ scope.row.dieseloil6.total.red }}</div>
              </div>
            </div>
            <div v-else>
              <div class="icon" :class="{
                'red':scope.row.dieseloil6.type==2,
                'green':scope.row.dieseloil6.type==0,
                'yellow':scope.row.dieseloil6.type==1}"></div>
            </div>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="航煤" align="center">
        <el-table-column prop='coaloil1' label="1#" align="center">
          <template slot-scope="scope">
            <div v-if="scope.$index===0">
              <div class="tips">
                <div class="icon green">{{ scope.row.coaloil1.total.green }}</div>
                <div class="icon yellow">{{ scope.row.coaloil1.total.yellow }}</div>
                <div class="icon red">{{ scope.row.coaloil1.total.red }}</div>
              </div>
            </div>
            <div v-else>
              <div class="icon" :class="{
                'red':scope.row.coaloil1.type==2,
                'green':scope.row.coaloil1.type==0,
                'yellow':scope.row.coaloil1.type==1}"></div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop='coaloil2' label="2#" align="center">
          <template slot-scope="scope">
            <div v-if="scope.$index===0">
              <div class="tips">
                <div class="icon green">{{ scope.row.coaloil2.total.green }}</div>
                <div class="icon yellow">{{ scope.row.coaloil2.total.yellow }}</div>
                <div class="icon red">{{ scope.row.coaloil2.total.red }}</div>
              </div>
            </div>
            <div v-else>
              <div class="icon" :class="{
                'red':scope.row.coaloil2.type==2,
                'green':scope.row.coaloil2.type==0,
                'yellow':scope.row.coaloil2.type==1}"></div>
            </div>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="催汽" align="center">
        <el-table-column width="100" prop='petrol' label="1#" align="center">
          <template slot-scope="scope">
            <div v-if="scope.$index===0">
              <div class="tips">
                <div class="icon green">{{ scope.row.petrol.total.green }}</div>
                <div class="icon yellow">{{ scope.row.petrol.total.yellow }}</div>
                <div class="icon red">{{ scope.row.petrol.total.red }}</div>
              </div>
            </div>
            <div v-else>
              <div class="icon" :class="{
                'red':scope.row.petrol.type==2,
                'green':scope.row.petrol.type==0,
                'yellow':scope.row.petrol.type==1}">
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="焦汽" align="center">
        <el-table-column width="100" prop='cokergasoline' label="1#" align="center">
          <template slot-scope="scope">
            <div v-if="scope.$index===0">
              <div class="tips">
                <div class="icon green">{{ scope.row.cokergasoline.total.green }}</div>
                <div class="icon yellow">{{ scope.row.cokergasoline.total.yellow }}</div>
                <div class="icon red">{{ scope.row.cokergasoline.total.red }}</div>
              </div>
            </div>
            <div v-else>
              <div class="icon" :class="{
                'red':scope.row.cokergasoline.type==2,
                'green':scope.row.cokergasoline.type==0,
                'yellow':scope.row.cokergasoline.type==1}">
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="制氢" align="center">
        <el-table-column prop='hgu1' label="1#" align="center">
          <template slot-scope="scope">
            <div v-if="scope.$index===0">
              <div class="tips">
                <div class="icon green">{{ scope.row.hgu1.total.green }}</div>
                <div class="icon yellow">{{ scope.row.hgu1.total.yellow }}</div>
                <div class="icon red">{{ scope.row.hgu1.total.red }}</div>
              </div>
            </div>
            <div v-else>
              <div class="icon" :class="{
                'red':scope.row.hgu1.type==2,
                'green':scope.row.hgu1.type==0,
                'yellow':scope.row.hgu1.type==1}">
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop='hgu2' label="2#" align="center">
          <template slot-scope="scope">
            <div v-if="scope.$index===0">
              <div class="tips">
                <div class="icon green">{{ scope.row.hgu2.total.green }}</div>
                <div class="icon yellow">{{ scope.row.hgu2.total.yellow }}</div>
                <div class="icon red">{{ scope.row.hgu2.total.red }}</div>
              </div>
            </div>
            <div v-else>
              <div class="icon" :class="{
                'red':scope.row.hgu2.type==2,
                'green':scope.row.hgu2.type==0,
                'yellow':scope.row.hgu2.type==1}">
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop='hgu3' label="3#" align="center">
          <template slot-scope="scope">
            <div v-if="scope.$index===0">
              <div class="tips">
                <div class="icon green">{{ scope.row.hgu3.total.green }}</div>
                <div class="icon yellow">{{ scope.row.hgu3.total.yellow }}</div>
                <div class="icon red">{{ scope.row.hgu3.total.red }}</div>
              </div>
            </div>
            <div v-else>
              <div class="icon" :class="{
                'red':scope.row.hgu3.type==2,
                'green':scope.row.hgu3.type==0,
                'yellow':scope.row.hgu3.type==1}">
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table-column>
    </el-table>
  </div>
</div>
</template>
<style lang="less" scoped>
@import "../style.less";
.panel.device_stop{
  >.content{
    position:relative;
    >.legends{
      position:absolute;right:0;top:0;
      display:flex;
      align-items:center;
      padding:.02rem .04rem;
      margin-right:.1rem;
      >.legend{
        margin-left:.1rem;
        &:before{
          content:"";
          width:.1rem;
          height:.1rem;
          border-radius:50%;
          display:inline-block;
          margin-right:.06rem;
        }
        &.normal{
          // 42d39e
          &:before{
            background-color:#42d39e;
          }
        }
        &.warn{
          // f7b62c
          &:before{
            background-color:#f7b62c;
          }
        }
        &.stop{
          // ea4344
          &:before{
            background-color:#ea4344;
          }
        }
      }
    }
    .table{
      .tips{
        display:flex;
        align-items:center;
        justify-content: center;
        >.icon{
          margin:0 .1rem 0 0;
        }
      }
      .icon{
        min-width:.26rem;
        max-width:.26rem;
        height:.26rem;
        border-radius:50%;
        color:white;
        margin:0 auto;
        &.green{
          background-color:#60d7a7;
        }
        &.yellow{
          background-color:#f4bc32;
        }
        &.red{
          background-color:#e54d4f;
        }
      }
    }
  }
}
</style>