<script lang="ts" setup>
import { ref,reactive, nextTick, onMounted,onUnmounted } from "vue";
import { message } from "ant-design-vue";
import * as echarts from "echarts";
import dayjs from "dayjs";
import type {Dayjs} from "dayjs";
import {isEmpty,round} from "lodash";
import CMPTableCollapse from "@/components/catalytic/TableCollapse.vue";

import useCatalyticStore from "@/store/catalytic";
const storeCatalytic = useCatalyticStore();

import useInteractStore from "@/store/interact";
const storeInteract = useInteractStore();
let yczdParamListVo=[];

document.title="低温腐蚀速率-生产运行概览-催化";
type EChartsOption = echarts.EChartsOption;
const option: EChartsOption = reactive({
  title:{
    subtext:"",
    subtextStyle:{
      align:"right"
    },
    right: 160,
    top: -10
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
      crossStyle: {
        color: '#999'
      }
    }
  },
  legend:{
    right:120,
    top: 30,
    itemGap:20
  },
  xAxis: [
    {
      type: 'category',
      axisPointer: {
        type: 'shadow'
      },
      axisTick:{
        alignWithLabel:true
      },
      axisLabel:{
        // rotate:270,
        formatter: function (value) {
          return value.split('').join('\n')
        }
      }
    }
  ],
  yAxis: [
    {
      type: 'value',
      min:0,
      name:"毫米/年",
      axiosLine:{
        show:true
      },
      axisLine: {
        show: true
      },
      axisTick: {
        show: true
      }
    }
  ],
  series: [
    {
      type: 'bar',
      barMaxWidth: 25,
      label:{
        show:true,
        formatter:function(v){
          return round(v.value,2)
        }
      },
      data: []
    },
    {
      type: 'bar',
      barMaxWidth: 25,
      label:{
        show:true,
        formatter:function(v){
          return round(v.value,2)
        }
      },
      data: []
    }
  ]
})
let myChart: any = null;
function initChart(){
  const doc: any = document,
    chartDom: any = doc.getElementById("chartId");
  if (!myChart) {
    myChart = echarts.init(chartDom);
    myChart.setOption(option);
  }  
  nextTick(() => {
    window.addEventListener('resize', function () {
      myChart.resize();
    })
  })
}
function fetchChartData(){
  myChart.showLoading();
  const date = rankDate.value,
    beginDate = date&&date[0].format("YYYY-MM-DD")||"",
    endDate = date&&date[1].format("YYYY-MM-DD")||"";
  storeCatalytic.fetchChartData({
    pageCode:"LowCorrosionRate",
    beginDate:beginDate,
    endDate:endDate,
  }).then((resp)=>{
    //console.log("===uopk=resp==",resp);
    updateTableData(resp);
  }).catch(err=>{
    //console.log("==err==",err);
    message.error((err&&(err.statusText||err.message||err.msg||err))||"数据错误!");
  });
}
function updateTableData(data){
  if(!myChart||isEmpty(data)) return;
  yczdParamListVo = data.yczdParamListVo||[];
  const opts = myChart.getOption(),
    data1:any = yczdParamListVo[0]||{parameterName:"",parameterValue:[]},
    data2:any=yczdParamListVo[1]||{parameterName:"",parameterValue:[]};
  // opts.title=data.querytime;
  // console.log("====",opts,"===",data1.parameterValue);
  opts.title[0].subtext = data.querytime;
  opts.xAxis[0].data = data.deviceSum;
  opts.series[0].name=data1.parameterName;
  opts.series[0].data = data1.parameterValue;
  opts.series[1].name=data2.parameterName;
  opts.series[1].data = data2.parameterValue;
  // console.log("===",ret.retResult.yczdParamListVo[0].parameterValue);
  myChart.setOption(opts);
  myChart.hideLoading();
}
onMounted(()=>{
  initChart();
  fetchChartData();
})
onUnmounted(() => {
  myChart?.clear();
  myChart?.dispose();
  myChart = null;
});

// const rankDate = ref<[Dayjs,Dayjs]>([dayjs().subtract(1,"week"),dayjs()]);
const rankDate = ref<any>(null);
function doSearch(){
  fetchChartData();
}
</script>
<template>
<div class="page uopk">
  <div class="chart" id="chartId"></div>
  <div class="filter">
    <a-range-picker class="field"  v-model:value="rankDate"></a-range-picker>
    <a-button class="field" @click.stop="doSearch">确定</a-button>
  </div>
  <div class="tables">
    <CMPTable-Collapse />
  </div>
</div>
</template>
<style lang="less" scoped>
.page.uopk{
  >.chart{
    width:100%;height:600px;
  }
  >.filter{
    display:flex;
    justify-content: center;
    >.field{
      margin-left:10px;
      &:first-child{margin-left:0;}
    }
  }

  >.tables{
    margin-top:10px;
  }
}
</style>