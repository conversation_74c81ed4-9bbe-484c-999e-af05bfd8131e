<script lang="ts" setup>
import {ref, reactive,computed,watch} from "vue";
import { message } from "ant-design-vue";
import {filter} from "lodash";
import useCatalyticStore from "@/store/catalytic";
import { checkIsDeep } from "@/utils/index";
const storeCatalytic = useCatalyticStore();
document.title="基础数据表-技术报告-装置技术分析-催化裂化装置";
// 基础数据表-技术报告
const activeKey=ref([""]);
const listPFM = ref([]);
fetchList();
const listFilter = computed(()=>{
  return filter(listPFM.value,(v)=>{
    const text = searchText.value;
    if(!text) return true;
    return !!v.keyWord&&v.keyWord===text;
  });
})
const listRoot = computed(()=>{
  return filter(listFilter.value,(v)=>{ return !v.childList||v.childList.length<=0});
})
const isEmptyListRoot = computed(()=>{
  return !listRoot.value||listRoot.value.length<=0?true:false;
})
const listOther = computed(()=>{
  const list = filter(listFilter.value,(v)=>{ return v.childList&&v.childList.length>0;});
  activeKey.value = list[0]&&list[0].nodeId;
  return list;
})
function fetchList(){
  storeCatalytic.fetchTaodPfmList({nodeId:112}).then((resp)=>{
    listPFM.value = resp.toperationTreeList||[];
  }).catch(err=>{
    // console.log("==err==",err);
    message.error((err&&(err.statusText||err.message||err.msg||err))||"数据错误!");
  });
}
const searchText = ref<string>("");
function doSearch(){

}
const currentPfmUrl = ref<string>("");
interface iDv{
  isShow:boolean
  url:string
  title:string
}
const dvForm = reactive<iDv>({
  isShow:false,
  url:"",
  title:""
});
function doSeePfm(item,parentName?:string){
  dvForm.isShow=true;
  dvForm.url=item.nodeUrl;
  if(parentName){
    dvForm.title=parentName+"-"+item.nodeName;
  }else{
    dvForm.title=item.nodeName;
  }
}
function onCloseDvPreview(){
  dvForm.isShow=false;
  dvForm.url="";
  dvForm.title="";
}
function checkIsDeepList(item){
  return checkIsDeep(item);
}
</script>
<template>

<div class="page taod_equipment_data">
  <a-modal 
    class="dv preview" 
    width="90%" 
    v-model:open="dvForm.isShow"
    :footer="null"
    :title="dvForm.title"
    @cancel="onCloseDvPreview">
    <iframe class="preview" :src="dvForm.url" />
  </a-modal>
  <div class="filter">
    <div class="field">
      <label>查询条件</label>
      <a-input v-model:value="searchText"></a-input>
    </div>
    <div class="field">
      <a-button @click.stop="doSearch">搜索</a-button>
    </div>
    <div class="tip">输入企业拼音首字母。例如：搜索燕山请输入ys</div>
  </div>
  <div class="panel" v-if="!isEmptyListRoot">
    <a-list class="list" :border="false">
      <a-list-item 
        class="item"
        v-for="item in listRoot" 
        :key="item.nodeUrl"
        :class="{'current':currentPfmUrl===item.nodeUrl}"
        @click.stop="doSeePfm(item)">{{item.nodeName}}</a-list-item>
    </a-list>
  </div>
  <a-collapse class="collapse" v-model:activeKey="activeKey">
    <a-collapse-panel 
      v-for="area in listOther" 
      :key="area.nodeId" 
      :header="area.nodeName">
      <template v-if="checkIsDeepList(area)">
        <div class="panel" v-for="item in area.childList">
          <div class="title">{{item.nodeName}}</div>
          <a-list class="list" :border="false">
            <a-list-item 
              class="item"
              v-for="it in item.childList"
              :key="it.nodeUrl"
              @click.stop="doSeePfm(it,item.nodeName)">{{it.nodeName}}</a-list-item>
          </a-list>
        </div>
      </template>
      <template v-else>
        <a-list class="list" :border="false">
          <a-list-item 
            class="item"
            v-for="item in area.childList"
            :key="item.nodeUrl"
            @click.stop="doSeePfm(item,area.nodeName)">{{item.nodeName}}</a-list-item>
        </a-list>
      </template>
    </a-collapse-panel>
  </a-collapse>
</div>
</template>
<style lang="less" scoped>
@import "@/assets/less/collapse";
.dv.preview{
  .preview{
    border:none;
    width:100%;min-height:680px;
  }
}
.page.taod_equipment_data{
  >.filter{
    display:flex;
    justify-content: center;
    align-items:center;
    margin-bottom:10px;
    >.field{
      margin-left:10px;
      display:flex;
      align-items: center;
      &:first-child{margin-left:0;}
      >label{
        white-space:nowrap;
        margin-right:10px;
      }
    }
    >.tip{
      margin-left:10px;
      font-size:14px;
      color:gray;
    }
  }
  .collapse();
}
</style>
