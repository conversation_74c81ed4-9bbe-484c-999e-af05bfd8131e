<script lang="ts" setup>
/**
 * 操作条件table
 * Author: menglj
 * Date: 20240510
 */
import { ref,reactive} from "vue"
import { iTableColumn,DataItem } from "@/interface/index";
import type { TableProps, TableColumnType } from "ant-design-vue";
import {formatExcelData,exportDataToExcel} from "@/utils/index";
const columns: TableColumnType<iTableColumn>[] = [
  {
    title: "序号",
    width: 80,
    dataIndex: "index",
    key: "index",
    fixed: "left",
  },
  {
    title: "装置名称",
    dataIndex: "deviceName",
    key: "deviceName",
    fixed: "left",
    // ellipsis: true,
  },
  {
    title: "设计寿命(月)",
    dataIndex: "t2C1",
    key: "t2C1",
    // ellipsis: true,
  },
  {
    title: "剩余寿命(月)",
    dataIndex: "t2C2",
    key: "t2C2",
    // ellipsis: true,
  },
  {
    title: "反应温度(℃)",
    dataIndex: "t2C3",
    key: "t2C3",
    // ellipsis: true,
  },
  {
    title: "精制床层温升(℃)",
    dataIndex: "t2C4",
    key: "t2C4",
    // ellipsis: true,
  },
  {
    title: "最大径向温差(℃)",
    dataIndex: "t2C5",
    key: "t2C5",
    // ellipsis: true,
  },
  {
    title: "床层最高温度(℃)",
    dataIndex: "t2C6",
    key: "t2C6",
    // ellipsis: true,
  },
  {
    title: "入口压力(MPa)",
    dataIndex: "t2C7",
    key: "t2C7",
    // ellipsis: true,
  },
  {
    title: "反应器压降(MPa)",
    dataIndex: "t2C8",
    key: "t2C8",
    // ellipsis: true,
  },
  {
    title: "分离罐压力(MPa)",
    dataIndex: "t2C9",
    key: "t2C9",
    // ellipsis: true,
  },
  {
    title: "空速(/h)",
    dataIndex: "t2C10",
    key: "t2C10",
    // ellipsis: true,
  }
];
const isFetching = ref<boolean>(false);
const props = defineProps({
  list:{
    type:Array,
    default(){
      return []
    },
    required:true
  }
})
const rowSelection: TableProps["rowSelection"] = {
  onSelect: (record: DataItem, selected: boolean, selectedRows: DataItem[]) => {
    let arr: any = [];
    selectedRows.forEach((item, index) => {
      if (item != undefined) {
        arr.push(item);
      }
    });
  },
  onSelectAll: (
    selected: boolean,
    selectedRows: DataItem[],
    changeRows: DataItem[]
  ) => {
    let arr: any = [];
    selectedRows.forEach((item, index) => {
      if (item != undefined) {
        arr.push(item);
      }
    });
  },
};
const scrollTableOption = reactive({ x: "max-content"});
// const scrollTableOption = reactive({ x: 1200, y: 0 });
// function updateScrollY() {
//   scrollTableOption.y = getTableScrollHeight(".table");
// }
// // 列表
// onMounted(() => {
//   nextTick(() => {
//     updateScrollY()
//   })
// })
function displayText(text:string){
  let _obj = text&&text.split(",")||"",
    _text = _obj&&_obj[0]!="null"&&_obj[0]!="NULL"?_obj[0]:"-";
  return _text;
}
function doExport(){
  const excelData = formatExcelData(columns,props.list);
  // console.log("===do export===",columns,"====",listTableData.value);
  // console.log("====",excelData);

  exportDataToExcel(excelData,"table_refining_reactor","操作条件");
}
defineExpose({
  doExport
});
</script>
<template>
<div class="cmp table_refining_reactor">
  <a-table 
    class="table" 
    bordered 
    :columns="columns" 
    :data-source="props.list" 
    :rowKey="(record) => { return 'refining-reactor-' + record.device_NAME; }" 
    :footer="null"
    :scroll="scrollTableOption"
    :pagination="false"
    :loading="isFetching">
    <template #bodyCell="{ column, record, index }">
      <template v-if="column.key==='index'">
        {{ index+1 }}
      </template>
      <template v-if="column.key==='t2C1'">
        <div class="value" :class="{'warn':(record.t2C1&&record.t2C1.split(',')[1]==1?true:false)}">{{ displayText(record.t2C1) }}</div>
      </template>
      <template v-if="column.key==='t2C2'">
        <div class="value" :class="{'warn':(record.t2C2&&record.t2C2.split(',')[1]==1?true:false)}">{{ displayText(record.t2C2) }}</div>
      </template>
      <template v-if="column.key==='t2C3'">
        <div class="value" :class="{'warn':(record.t2C3&&record.t2C3.split(',')[1]==1?true:false)}">{{ displayText(record.t2C3) }}</div>
      </template>
      <template v-if="column.key==='t2C4'">
        <div class="value" :class="{'warn':(record.t2C4&&record.t2C4.split(',')[1]==1?true:false)}">{{ displayText(record.t2C4) }}</div>
      </template>
      <template v-if="column.key==='t2C5'">
        <div class="value" :class="{'warn':(record.t2C5&&record.t2C5.split(',')[1]==1?true:false)}">{{ displayText(record.t2C5) }}</div>
      </template>
      <template v-if="column.key==='t2C6'">
        <div class="value" :class="{'warn':(record.t2C6&&record.t2C6.split(',')[1]==1?true:false)}">{{ displayText(record.t2C6) }}</div>
      </template>
      <template v-if="column.key==='t2C7'">
        <div class="value" :class="{'warn':(record.t2C7&&record.t2C7.split(',')[1]==1?true:false)}">{{ displayText(record.t2C7) }}</div>
      </template>
      <template v-if="column.key==='t2C8'">
        <div class="value" :class="{'warn':(record.t2C8&&record.t2C8.split(',')[1]==1?true:false)}">{{ displayText(record.t2C8) }}</div>
      </template>
    </template>
  </a-table>
</div>
</template>
<style lang="less" scoped>
.cmp.table_refining_reactor{
  height:600px;
  overflow: auto;
  &:deep(.table){
    .warn{
      color:red;
      font-weight:bold;
    }
  }
  // 加深 a-table 标题行底色
  &:deep(.ant-table-thead > tr > th) {
    background: #e6e6e6 !important; // 可根据需要调整颜色深浅
    color: #222;
  }
}
</style>