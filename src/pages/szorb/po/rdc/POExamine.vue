<script lang="ts" setup>
import { ref,reactive, nextTick, onMounted,onUnmounted } from "vue";
import { message } from "ant-design-vue";
import * as echarts from "echarts";
import dayjs from "dayjs";
import type {Dayjs} from "dayjs";
import {isEmpty} from "lodash";
import CMPTablePOExamine from "@/components/szorb/TablePoExamine.vue";

import useSzorbStore from "@/store/szorb";
const storeSzorb = useSzorbStore();
document.title="装置竞赛排名-装置竞赛排名-生产运行概览-Szorb装置";
type EChartsOption = echarts.EChartsOption;
const option: EChartsOption = reactive({
  title:{
    subtext:"",
    subtextStyle:{
      align:"right"
    },
    right: 160,
    top: -10
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
      crossStyle: {
        color: '#999'
      }
    }
  },
  legend:{
    width: "70%", // 控制图例区域宽度
    type: "scroll",
    right: 120,
    itemGap: 20,
    top: 20,
    textStyle: {
      width: 200, // 控制每个图例项的最大宽度
      overflow: "break",
      rich: {}
    }
  },
  xAxis: [
    {
      type: 'category',
      axisPointer: {
        type: 'shadow'
      },
      axisTick:{
        alignWithLabel:true
      },
      axisLabel:{
        // rotate:270,
        formatter: function (value) {
          return value.split('').join('\n')
        }
      }
    }
  ],
  yAxis: [
    {
      type: 'value',
      min:0,
      name:"分",
      axiosLine:{
        show:true
      },
      axisLine: {
        show: true
      },
      axisTick: {
        show: true
      }
    }
  ],
  series: [
    {
      type: 'bar',
      stack: 'total',
      barMaxWidth: 25,
      data: []
    },
    {
      type: 'bar',
      stack: 'total',
      barMaxWidth: 25,
      data: []
    },
    {
      type: 'bar',
      stack: 'total',
      barMaxWidth: 25,
      data: []
    },
    {
      type: 'bar',
      stack: 'total',
      barMaxWidth: 25,
      data: []
    },
    {
      type: 'bar',
      stack: 'total',
      barMaxWidth: 25,
      data: []
    },
    {
      type: 'bar',
      stack: 'total',
      barMaxWidth: 25,
      data: []
    },
    {
      type: 'bar',
      stack: 'total',
      barMaxWidth: 25,
      data: []
    },
    {
      type: 'bar',
      stack: 'total',
      barMaxWidth: 25,
      data: []
    },
    {
      type: 'bar',
      stack: 'total',
      barMaxWidth: 25,
      data: []
    },
  ]
})
let myChart: any = null;
function initChart(){
  const doc: any = document,
    chartDom: any = doc.getElementById("chartId");

  if (!myChart) {
    myChart = echarts.init(chartDom);
    myChart.setOption(option);
  }

  nextTick(() => {
    window.addEventListener('resize', function () {
      myChart.resize();
    })
  })
}
function fetchChartData(){
  myChart.showLoading();
  const date = rankDate.value,
    beginDate = date&&date[0].format("YYYY-MM-DD")||"",
    endDate = date&&date[1].format("YYYY-MM-DD")||"";
  storeSzorb.fetchPOChartData({
    pageCode:"POExamine",
    beginDate:beginDate,
    endDate:endDate,
  }).then((resp)=>{
    // console.log("===uopk=resp==",resp);
    updateTableData(resp);
  }).catch(err=>{
    // console.log("==err==",err);
    message.error((err&&(err.statusText||err.message||err.msg||err))||"数据错误!");
  });
}
function updateTableData(data){
  if(!myChart||isEmpty(data)) return;
  const opts = myChart.getOption(),
    yczdParamListVo = data.yValues||[],
    data1:any = yczdParamListVo[0]||{chartName:"",chartData:0},
    data2:any=yczdParamListVo[1]||{chartName:"",chartData:0},
    data3:any=yczdParamListVo[2]||{chartName:"",chartData:0},
    data4:any = yczdParamListVo[3]||{chartName:"",chartData:0},
    data5:any=yczdParamListVo[4]||{chartName:"",chartData:0},
    data6:any=yczdParamListVo[5]||{chartName:"",chartData:0},
    data7:any = yczdParamListVo[6]||{chartName:"",chartData:0},
    data8:any=yczdParamListVo[7]||{chartName:"",chartData:0},
    data9:any=yczdParamListVo[8]||{chartName:"",chartData:0};
  // opts.title=data.querytime;
  // console.log("====",opts);
  opts.title[0].subtext = data.querytime;
  opts.xAxis[0].data = data.xValues;
  opts.series[0].name=data1.chartName;
  opts.series[0].data = data1.chartData;

  opts.series[1].name=data2.chartName;
  opts.series[1].data = data2.chartData;

  opts.series[2].name=data3.chartName;
  opts.series[2].data = data3.chartData;

  opts.series[3].name=data4.chartName;
  opts.series[3].data = data4.chartData;

  opts.series[4].name=data5.chartName;
  opts.series[4].data = data5.chartData;

  opts.series[5].name=data6.chartName;
  opts.series[5].data = data6.chartData;

  opts.series[6].name=data7.chartName;
  opts.series[6].data = data7.chartData;

  opts.series[7].name=data8.chartName;
  opts.series[7].data = data8.chartData;

  opts.series[8].name=data9.chartName;
  opts.series[8].data = data9.chartData;

  // console.log("===",ret.retResult.yczdParamListVo[0].chartData);
  myChart.setOption(opts);
  myChart.hideLoading();
}
onMounted(()=>{
  initChart();
  fetchChartData();
})
onUnmounted(() => {
  myChart?.clear();
  myChart?.dispose();
  myChart = null;
});

// const rankDate = ref<[Dayjs,Dayjs]>([dayjs().subtract(1,"week"),dayjs()]);
const rankDate = ref<any>(null);
function doSearch(){
  fetchChartData();
}
</script>
<template>
<div class="page uopk">
  <div class="chart" id="chartId"></div>
  <div class="filter">
    <a-range-picker class="field"  v-model:value="rankDate"></a-range-picker>
    <a-button class="field" @click.stop="doSearch">确定</a-button>
  </div>
  <div class="tables">
    <CMPTable-PO-Examine />
  </div>
</div>
</template>
<style lang="less" scoped>
.page.uopk{
  >.chart{
    width:100%;height:600px;
  }
  >.filter{
    display:flex;
    justify-content: center;
    >.field{
      margin-left:10px;
      &:first-child{margin-left:0;}
    }
  }

  >.tables{
    margin-top:10px;
  }
}
</style>