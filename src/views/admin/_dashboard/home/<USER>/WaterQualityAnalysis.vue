<script>
import dashboard from "@/store/dashboard";
const storeDashboard = dashboard();
export default {
  data(){
    return {
      listData:[]
    }
  },
  created(){
    this.fetchRawMatrial();
  },
  methods:{
    fetchRawMatrial(){
      if(this.isFetching) return;
      this.isFetching = true;
      storeDashboard.fetchWatherPhValue().then((resp)=>{
        // const respData = resp.retResult||[];
        // // console.log("===resp==",respData);
        // for(let i=0;i<10;i++) {
        //   let record = respData[i],
        //     sourceData = this.listData[i]||{};
        //   for(let j=0;j<9;j++){
        //     const d = record&&record[j]||'-';
        //     // console.log("====",record,"===",d);
        //     set(sourceData,`t1c${j+1}`,d);
        //   }
        //   this.listData[i] = sourceData;
        // }
        this.listData = resp.retResult||[];
        // console.log("==data==",this.listData);
      }).finally(()=>{
        this.isFetching = false;
      });
    }
  }
}
</script>
<template>
<div class="panel overview">
  <div class="head">
    <h4 class="title">水质分析表</h4>
  </div>
  <div class="content">
    <el-table 
      :data="listData" 
      class="table">
      <el-table-column fixed label="" prop="project"></el-table-column>
      <el-table-column label="常减压" align="center">
        <el-table-column prop='c1' label="初顶水">
          <template #default="scope" >
            <el-tooltip :content="scope.row.c1_device" placement="top">
              <div>{{ scope.row.c1 }}</div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop='c2' label="常顶水">
          <template #default="scope" >
            <el-tooltip :content="scope.row.c2_device" placement="top">
              <div>{{ scope.row.c2 }}</div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop='c3' label="减顶水">
          <template #default="scope" >
            <el-tooltip :content="scope.row.c3_device" placement="top">
              <div>{{ scope.row.c3 }}</div>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="催化裂化" align="center">
        <el-table-column prop='c4' width="100" label="分馏塔顶水">
          <template #default="scope" >
            <el-tooltip :content="scope.row.c4_device" placement="top">
              <div>{{ scope.row.c4 }}</div>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="延迟焦化" align="center">
        <el-table-column prop='c5' width="100" label="分馏塔顶水">
          <template #default="scope" >
            <el-tooltip :content="scope.row.c5_device" placement="top">
              <div>{{ scope.row.c5 }}</div>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="催化重整" align="center">
        <el-table-column prop='c6' width="120" label="预加氢高分水">
          <template #default="scope" >
            <el-tooltip :content="scope.row.c6_device" placement="top">
              <div>{{ scope.row.c6 }}</div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop='c6_2' width="120" label="汽提塔酸性水">
          <template #default="scope" >
            <el-tooltip :content="scope.row.c6_2_device" placement="top">
              <div>{{ scope.row.c6_2 }}</div>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="加裂" align="center">
        <el-table-column prop='c7' label="高分水">
          <template #default="scope" >
            <el-tooltip :content="scope.row.c7_device" placement="top">
              <div>{{ scope.row.c7 }}</div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop='c8' label="酸性水">
          <template #default="scope" >
            <el-tooltip :content="scope.row.c8_device" placement="top">
              <div>{{ scope.row.c8 }}</div>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="催汽" align="center">
        <el-table-column prop='c9' label="酸性水">
          <template #default="scope" >
            <el-tooltip :content="scope.row.c9_device" placement="top">
              <div>{{ scope.row.c9 }}</div>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="焦汽" align="center">
        <el-table-column prop='c10' label="酸性水">
          <template #default="scope" >
            <el-tooltip :content="scope.row.c10_device" placement="top">
              <div>{{ scope.row.c10 }}</div>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="柴油" align="center">
        <el-table-column prop='c11' label="酸性水">
          <template #default="scope" >
            <el-tooltip :content="scope.row.c11_device" placement="top">
              <div>{{ scope.row.c11 }}</div>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="蜡油" align="center">
        <el-table-column prop='c12' label="高分水">
          <template #default="scope" >
            <el-tooltip :content="scope.row.c12_device" placement="top">
              <div>{{ scope.row.c12 }}</div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop='c13' label="酸性水">
          <template #default="scope" >
            <el-tooltip :content="scope.row.c13_device" placement="top">
              <div>{{ scope.row.c13 }}</div>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="硫磺" align="center">
        <el-table-column prop='c14' label="急冷水">
          <template #default="scope" >
            <el-tooltip :content="scope.row.c14_device" placement="top">
              <div>{{ scope.row.c14 }}</div>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="制氢" align="center">
        <el-table-column prop='c15' width="90" label="锅炉给水">
          <template #default="scope" >
            <el-tooltip :content="scope.row.c15_device" placement="top">
              <div>{{ scope.row.c15 }}</div>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="MTBE" align="center">
        <el-table-column prop='c16' label="萃取水">
          <template #default="scope" >
            <el-tooltip :content="scope.row.c16_device" placement="top">
              <div>{{ scope.row.c16 }}</div>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table-column>
    </el-table>
  </div>
</div>
</template>
<style lang="less" scoped>
@import "../style.less";
</style>