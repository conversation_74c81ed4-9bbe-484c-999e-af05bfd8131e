<script lang="ts" setup>
document.title="首页 - 中国石化炼油技术分析及远程诊断系统"
import { ref } from "vue";
const currentNav = ref<string[]>(["nav_2_1_1"]);
const urlIframe = ref("/yczd-auth-web/catalytic/po/rmp/uopk-factor");
const currentNavName = ref("原料UOP K因子");

function gotoNav(url: string,key:string,title:string) {
  urlIframe.value =url;
  currentNav.value=[key];
  currentNavName.value = title;
}

</script>
<template>
  <a-layout class="page home">
    <a-layout-sider class="sider" collapsible>
      <h3 class="nav_title">蜡油加氢装置</h3>
      <a-menu v-model:selectedKeys="currentNav" mode="inline">
        <a-sub-menu key="nav_1">
          <template #title>装置技术分析</template>
          <a-sub-menu key="nav_1_1">
            <template #title>报警管理</template>
            <a-menu-item key="nav_1_1_1">
              <div @click.stop="gotoNav('/yczd-auth-web/hydrotreating/hydrowax/taod/alarm?code=yanshan2','nav_1_1_1','燕山2')">燕山2</div>
            </a-menu-item>
          </a-sub-menu>
          <a-sub-menu key="nav_1_2">
            <template #title>生产实时</template>
            <a-menu-item key="nav_1_2_1">
              <div @click.stop="gotoNav('/yczd-auth-web/hydrotreating/hydrowax/taod/product/pfm','nav_1_2_1','工艺流程展示')">工艺流程展示</div>
            </a-menu-item>
            <a-menu-item key="nav_1_2_2">
              <div @click.stop="gotoNav('/yczd-auth-web/hydrotreating/hydrowax/taod/product/pfm-params','nav_1_2_2','工艺参数查询')">工艺参数查询</div>
            </a-menu-item>
            <a-menu-item key="nav_1_2_3">
              <div @click.stop="gotoNav('/yczd-auth-web/hydrotreating/hydrowax/taod/product/unify-query','nav_1_2_3','质量参数查询')">质量参数查询</div>
            </a-menu-item>
            <a-menu-item key="nav_1_2_4">
              <div @click.stop="gotoNav('/yczd-auth-web/hydrotreating/hydrowax/taod/product/pfm-data','nav_1_2_4','工艺数据运维')">工艺数据运维</div>
            </a-menu-item>
          </a-sub-menu>
          <a-sub-menu key="nav_1_3">
            <template #title>技术报告</template>
            <a-menu-item key="nav_1_3_1">
              <div @click.stop="gotoNav('/yczd-auth-web/hydrotreating/hydrowax/taod/equipment','nav_1_3_1','技术报告')">技术报告</div>
            </a-menu-item>
            <a-menu-item key="nav_1_3_2">
              <div @click.stop="gotoNav('/yczd-auth-web/hydrotreating/hydrowax/taod/equipment/data-table','nav_1_3_2','基础数据表')">基础数据表</div>
            </a-menu-item>
            <a-menu-item key="nav_1_3_3">
              <div @click.stop="gotoNav('/yczd-auth-web/hydrotreating/hydrowax/taod/equipment/entry','nav_1_3_3','补充录入')">补充录入</div>
            </a-menu-item>
            <a-menu-item key="nav_1_3_4">
              <div @click.stop="gotoNav('/yczd-auth-web/hydrotreating/hydrowax/taod/equipment/pfm','nav_1_3_4','工艺指标')">工艺指标</div>
            </a-menu-item>
            <a-menu-item key="nav_1_3_5">
              <div @click.stop="gotoNav('/yczd-auth-web/hydrotreating/hydrowax/taod/equipment/report-statistic-info','nav_1_3_5','上报情况统计')">上报情况统计</div>
            </a-menu-item>
          </a-sub-menu>
          <a-menu-item key="nav_1_4">
            <div @click.stop="gotoNav('/yczd-auth-web/hydrotreating/hydrowax/taod/calculation','nav_2_2','结晶温度计算')">结晶温度计算</div>
          </a-menu-item>
        </a-sub-menu>
      </a-menu>
    </a-layout-sider>
    <a-layout-content>
      <h1 class="title">{{ currentNavName }}</h1>
      <iframe class="bd" :src="urlIframe" />
    </a-layout-content>
  </a-layout>
</template>
<style lang="less" scoped>
.page.home {
  width: 100%;
  height: 100vh;
  background: #f7f7fc !important;

  >.sider {
    .title {
      color: #fff;
    }
  }

  .title,
  .nav_title {
    color: #fff;
    background-color: #001529;
    height: 60px;
    line-height: 60px;
    padding: 0;
    margin: 0;
  }
  .bd{
    padding: 10px;
    width: 100%;
    // height: 100%;
    border: none;
    height: calc(100% - 60px);
  }
}
</style>