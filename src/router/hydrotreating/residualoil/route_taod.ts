// 装置技术分析 hydrotreaing(hy)-residualoil(ro)
import PageHYROTAODHome from "../../../pages/hydrotreating/residualoil/taod/index.vue";

import PageHYROTAODAlarm from "../../../pages/hydrotreating/residualoil/taod/alarm.vue";

import PageHYROTCODProductPFM from "../../../pages/hydrotreating/residualoil/taod/product/pfm.vue";
import PageHYROTCODProductPFMParams from "../../../pages/hydrotreating/residualoil/taod/product/PFMParams.vue";
import PageHYROTCODProductPFMData from "../../../pages/hydrotreating/residualoil/taod/product/PFMData.vue";
import PageHYROTCODProductUnifyQuery from "../../../pages/hydrotreating/residualoil/taod/product/UnifyQuery.vue";

import PageHYROTCODEquipment from "../../../pages/hydrotreating/residualoil/taod/equipment/index.vue";
import PageHYROTCODEquipmentDataTable from "../../../pages/hydrotreating/residualoil/taod/equipment/DataTable.vue";
import PageHYROTCODEquipmentReportStatisticInfo from "../../../pages/hydrotreating/residualoil/taod/equipment/ReportStatisticInfo.vue";
import PageHYROTCODEquipmentEntry from "../../../pages/hydrotreating/residualoil/taod/equipment/entry.vue";
import PageHYROTCODEquipmentPfm from "../../../pages/hydrotreating/residualoil/taod/equipment/pfm.vue";

import PageHYROTCODCalculate from "../../../pages/hydrotreating/residualoil/taod/calculate.vue";

import PageTAODResidueCompositionMaterialsResidue from "../../../pages/hydrotreating/residualoil/taod/residue/CompositionMaterialsResidue.vue";
import PageTAODResidueNatureOfCrudeOilResidue from "../../../pages/hydrotreating/residualoil/taod/residue/NatureOfCrudeOilResidue.vue";
import PageTAODResidueOperationParamsResidue from "../../../pages/hydrotreating/residualoil/taod/residue/OperatingParametersResidue.vue";
import PageTAODResidueOperationResidue from "../../../pages/hydrotreating/residualoil/taod/residue/OperationResidue.vue";
import PageTAODResidueProductDistributionResidue from "../../../pages/hydrotreating/residualoil/taod/residue/ProductDistributionResidue.vue";

export default {
  path:"taod",
  name:"hr-taod",
  children:[
    {
      path:"home",
      name:"hr-taod-home",
      component:PageHYROTAODHome
    },
    {
      path:"alarm",
      name:"hr-taod-alarm",
      component:PageHYROTAODAlarm
    },
    {
      path:"calculate",
      name:"hr-taod-calculate",
      component:PageHYROTCODCalculate
    },
    {
      path:"residue", // 渣油加氢装置
      name:"hh-taod-residue",
      children:[
        {
          path:"composition-material", // 原料组成
          name:"hh-taod-residue-composition-material",
          component:PageTAODResidueCompositionMaterialsResidue
        },
        {
          path:"nature-of-crude-oil", // 原料性质
          name:"hh-taod-residue-nature-of-crude-oil",
          component:PageTAODResidueNatureOfCrudeOilResidue
        },
        {
          path:"operation-params", // 运行参数
          name:"hh-taod-residue-operation-params",
          component:PageTAODResidueOperationParamsResidue
        },
        {
          path:"operation", // 运行状况
          name:"hh-taod-residue-operation",
          component:PageTAODResidueOperationResidue
        },
        {
          path:"product-distribution", // 产品分布
          name:"hh-taod-residue-product-distribution",
          component:PageTAODResidueProductDistributionResidue
        }
      ]
    },
    {
      path:"product",
      name:"hr-taod-product",
      children:[
        {
          path:"pfm",
          name:"hr-taod-product-pfm",
          component:PageHYROTCODProductPFM,
        },
        {
          path:"pfm-params",
          name:"hr-taod-product-pfm-params",
          component:PageHYROTCODProductPFMParams,
        },
        {
          path:"pfm-data",
          name:"hr-taod-product-pfm-data",
          component:PageHYROTCODProductPFMData,
        },
        {
          path:"unify-query",
          name:"hr-taod-product-unify-query",
          component:PageHYROTCODProductUnifyQuery,
        }
      ]
    },
    {
      path:"equipment",
      name:"hr-taod-equipment",
      children:[
        {
          path:"",
          name:"hr-taod-equipment-index",
          component:PageHYROTCODEquipment,
        },
        {
          path:"data-table",
          name:"hr-taod-equipment-data-table",
          component:PageHYROTCODEquipmentDataTable,
        },
        {
          path:"entry",
          name:"hr-taod-equipment-entry",
          component:PageHYROTCODEquipmentEntry,
        },
        {
          path:"pfm",
          name:"hr-taod-equipment-pfm",
          component:PageHYROTCODEquipmentPfm,
        },
        {
          path:"report-statistic-info",
          name:"hr-taod-equipment-report-statistic-info",
          component:PageHYROTCODEquipmentReportStatisticInfo,
        }
      ]
    }
  ]
}