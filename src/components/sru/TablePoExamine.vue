<script lang="ts" setup>
/**
 * 装置竞赛排名table
 * Author: menglj
 * Date: 20240612
 */
import { ref,reactive} from "vue"
import { message } from "ant-design-vue";
import { iTableColumn,DataItem } from "@/interface/index";
import type { TableProps, TableColumnType } from "ant-design-vue";
import {formatExcelData,exportDataToExcel} from "@/utils/index";
import {forEach,assign,toNumber,orderBy} from "lodash";
import {iPagination} from "@/interface/index";

import useSruStore from "@/store/sru";
const storeSru = useSruStore();
const TableData = [
  {
    "devicename": "安庆1",
    "zfpm": null,
    "zfdf": null,
    "fjhtgdf": 18,
    "fjhtgts": null,
    "fjhtgcs": null,
    "nhdf": "0.0",
    "nhz": "0",
    "czqyxdf": "5.0",
    "czqyxts": "2929",
    "czqyxlyl": null,
    "qysldf": "0",
    "qyslz": "96",
    "yjljhsdf": "0",
    "yjljhsz": "0.97",
    "gqdf": "0.0",
    "gqz": "1",
    "sjdf": "0",
    "sjz": "0",
    "qyrondf": "0",
    "qyronz": "39",
    "cycdddf": "0",
    "cycddz": "-5"
  },
  {
    "devicename": "安庆2",
    "zfpm": null,
    "zfdf": null,
    "fjhtgdf": null,
    "fjhtgts": null,
    "fjhtgcs": null,
    "nhdf": "0.0",
    "nhz": "0",
    "czqyxdf": "5.0",
    "czqyxts": "2929",
    "czqyxlyl": null,
    "qysldf": "0",
    "qyslz": "96",
    "yjljhsdf": "0",
    "yjljhsz": "0.97",
    "gqdf": "0.0",
    "gqz": "1",
    "sjdf": "0",
    "sjz": "0",
    "qyrondf": "0",
    "qyronz": "39",
    "cycdddf": "0",
    "cycddz": "-5"
  },
  {
    "devicename": "安庆3",
    "zfpm": null,
    "zfdf": null,
    "fjhtgdf": null,
    "fjhtgts": null,
    "fjhtgcs": null,
    "nhdf": "0.0",
    "nhz": "0",
    "czqyxdf": "5.0",
    "czqyxts": "2929",
    "czqyxlyl": null,
    "qysldf": "0",
    "qyslz": "96",
    "yjljhsdf": "0",
    "yjljhsz": "0.97",
    "gqdf": "0.0",
    "gqz": "1",
    "sjdf": "0",
    "sjz": "0",
    "qyrondf": "0",
    "qyronz": "39",
    "cycdddf": "0",
    "cycddz": "-5"
  },
  {
    "devicename": "巴陵",
    "zfpm": null,
    "zfdf": null,
    "fjhtgdf": null,
    "fjhtgts": null,
    "fjhtgcs": null,
    "nhdf": "0.0",
    "nhz": "0",
    "czqyxdf": "5.0",
    "czqyxts": "2929",
    "czqyxlyl": null,
    "qysldf": "0",
    "qyslz": "96",
    "yjljhsdf": "0",
    "yjljhsz": "0.97",
    "gqdf": "0.0",
    "gqz": "1",
    "sjdf": "0",
    "sjz": "0",
    "qyrondf": "0",
    "qyronz": "39",
    "cycdddf": "0",
    "cycddz": "-5"
  },
  {
    "devicename": "北海",
    "zfpm": null,
    "zfdf": null,
    "fjhtgdf": null,
    "fjhtgts": null,
    "fjhtgcs": null,
    "nhdf": "0.0",
    "nhz": "0",
    "czqyxdf": "5.0",
    "czqyxts": "2929",
    "czqyxlyl": null,
    "qysldf": "0",
    "qyslz": "96",
    "yjljhsdf": "0",
    "yjljhsz": "0.97",
    "gqdf": "0.0",
    "gqz": "1",
    "sjdf": "0",
    "sjz": "0",
    "qyrondf": "0",
    "qyronz": "39",
    "cycdddf": "0",
    "cycddz": "-5"
  },
  {
    "devicename": "沧州",
    "zfpm": null,
    "zfdf": null,
    "fjhtgdf": null,
    "fjhtgts": null,
    "fjhtgcs": null,
    "nhdf": "0.0",
    "nhz": "0",
    "czqyxdf": "5.0",
    "czqyxts": "2929",
    "czqyxlyl": null,
    "qysldf": "0",
    "qyslz": "96",
    "yjljhsdf": "0",
    "yjljhsz": "0.97",
    "gqdf": "0.0",
    "gqz": "1",
    "sjdf": "0",
    "sjz": "0",
    "qyrondf": "0",
    "qyronz": "39",
    "cycdddf": "0",
    "cycddz": "-5"
  },
  {
    "devicename": "长岭1",
    "zfpm": null,
    "zfdf": null,
    "fjhtgdf": null,
    "fjhtgts": null,
    "fjhtgcs": null,
    "nhdf": "0.0",
    "nhz": "0",
    "czqyxdf": "5.0",
    "czqyxts": "2929",
    "czqyxlyl": null,
    "qysldf": "0",
    "qyslz": "96",
    "yjljhsdf": "0",
    "yjljhsz": "0.97",
    "gqdf": "0.0",
    "gqz": "1",
    "sjdf": "0",
    "sjz": "0",
    "qyrondf": "0",
    "qyronz": "39",
    "cycdddf": "0",
    "cycddz": "-5"
  },
  {
    "devicename": "长岭2",
    "zfpm": null,
    "zfdf": null,
    "fjhtgdf": null,
    "fjhtgts": null,
    "fjhtgcs": null,
    "nhdf": "0.0",
    "nhz": "0",
    "czqyxdf": "5.0",
    "czqyxts": "2929",
    "czqyxlyl": null,
    "qysldf": "0",
    "qyslz": "96",
    "yjljhsdf": "0",
    "yjljhsz": "0.97",
    "gqdf": "0.0",
    "gqz": "1",
    "sjdf": "0",
    "sjz": "0",
    "qyrondf": "0",
    "qyronz": "39",
    "cycdddf": "0",
    "cycddz": "-5"
  },
  {
    "devicename": "东兴1",
    "zfpm": null,
    "zfdf": null,
    "fjhtgdf": 20,
    "fjhtgts": null,
    "fjhtgcs": null,
    "nhdf": "0.0",
    "nhz": "0",
    "czqyxdf": "5.0",
    "czqyxts": "2929",
    "czqyxlyl": null,
    "qysldf": "0",
    "qyslz": "96",
    "yjljhsdf": "0",
    "yjljhsz": "0.97",
    "gqdf": "0.0",
    "gqz": "1",
    "sjdf": "0",
    "sjz": "0",
    "qyrondf": "0",
    "qyronz": "39",
    "cycdddf": "0",
    "cycddz": "-5"
  },
  {
    "devicename": "东兴2",
    "zfpm": null,
    "zfdf": null,
    "fjhtgdf": null,
    "fjhtgts": null,
    "fjhtgcs": null,
    "nhdf": "0.0",
    "nhz": "0",
    "czqyxdf": "5.0",
    "czqyxts": "2929",
    "czqyxlyl": null,
    "qysldf": "0",
    "qyslz": "96",
    "yjljhsdf": "0",
    "yjljhsz": "0.97",
    "gqdf": "0.0",
    "gqz": "1",
    "sjdf": "0",
    "sjz": "0",
    "qyrondf": "0",
    "qyronz": "39",
    "cycdddf": "0",
    "cycddz": "-5"
  },
  {
    "devicename": "高桥2",
    "zfpm": null,
    "zfdf": null,
    "fjhtgdf": null,
    "fjhtgts": null,
    "fjhtgcs": null,
    "nhdf": "0.0",
    "nhz": "0",
    "czqyxdf": "5.0",
    "czqyxts": "2929",
    "czqyxlyl": null,
    "qysldf": "0",
    "qyslz": "96",
    "yjljhsdf": "0",
    "yjljhsz": "0.97",
    "gqdf": "0.0",
    "gqz": "1",
    "sjdf": "0",
    "sjz": "0",
    "qyrondf": "0",
    "qyronz": "39",
    "cycdddf": "0",
    "cycddz": "-5"
  },
  {
    "devicename": "高桥3",
    "zfpm": null,
    "zfdf": null,
    "fjhtgdf": null,
    "fjhtgts": null,
    "fjhtgcs": null,
    "nhdf": "0.0",
    "nhz": "0",
    "czqyxdf": "5.0",
    "czqyxts": "2929",
    "czqyxlyl": null,
    "qysldf": "0",
    "qyslz": "96",
    "yjljhsdf": "0",
    "yjljhsz": "0.97",
    "gqdf": "0.0",
    "gqz": "1",
    "sjdf": "0",
    "sjz": "0",
    "qyrondf": "0",
    "qyronz": "39",
    "cycdddf": "0",
    "cycddz": "-5"
  },
  {
    "devicename": "广州1",
    "zfpm": null,
    "zfdf": null,
    "fjhtgdf": null,
    "fjhtgts": null,
    "fjhtgcs": null,
    "nhdf": "0.0",
    "nhz": "0",
    "czqyxdf": "5.0",
    "czqyxts": "2929",
    "czqyxlyl": null,
    "qysldf": "0",
    "qyslz": "96",
    "yjljhsdf": "0",
    "yjljhsz": "0.97",
    "gqdf": "0.0",
    "gqz": "1",
    "sjdf": "0",
    "sjz": "0",
    "qyrondf": "0",
    "qyronz": "39",
    "cycdddf": "0",
    "cycddz": "-5"
  },
  {
    "devicename": "广州2",
    "zfpm": null,
    "zfdf": null,
    "fjhtgdf": null,
    "fjhtgts": null,
    "fjhtgcs": null,
    "nhdf": "0.0",
    "nhz": "0",
    "czqyxdf": "5.0",
    "czqyxts": "2929",
    "czqyxlyl": null,
    "qysldf": "0",
    "qyslz": "96",
    "yjljhsdf": "0",
    "yjljhsz": "0.97",
    "gqdf": "0.0",
    "gqz": "1",
    "sjdf": "0",
    "sjz": "0",
    "qyrondf": "0",
    "qyronz": "39",
    "cycdddf": "0",
    "cycddz": "-5"
  },
  {
    "devicename": "海南",
    "zfpm": null,
    "zfdf": null,
    "fjhtgdf": null,
    "fjhtgts": null,
    "fjhtgcs": null,
    "nhdf": "0.0",
    "nhz": "0",
    "czqyxdf": "5.0",
    "czqyxts": "2929",
    "czqyxlyl": null,
    "qysldf": "0",
    "qyslz": "96",
    "yjljhsdf": "0",
    "yjljhsz": "0.97",
    "gqdf": "0.0",
    "gqz": "1",
    "sjdf": "0",
    "sjz": "0",
    "qyrondf": "0",
    "qyronz": "39",
    "cycdddf": "0",
    "cycddz": "-5"
  },
  {
    "devicename": "济南1",
    "zfpm": null,
    "zfdf": null,
    "fjhtgdf": null,
    "fjhtgts": null,
    "fjhtgcs": null,
    "nhdf": "0.0",
    "nhz": "0",
    "czqyxdf": "5.0",
    "czqyxts": "2929",
    "czqyxlyl": null,
    "qysldf": "0",
    "qyslz": "96",
    "yjljhsdf": "0",
    "yjljhsz": "0.97",
    "gqdf": "0.0",
    "gqz": "1",
    "sjdf": "0",
    "sjz": "0",
    "qyrondf": "0",
    "qyronz": "39",
    "cycdddf": "0",
    "cycddz": "-5"
  },
  {
    "devicename": "济南2",
    "zfpm": null,
    "zfdf": null,
    "fjhtgdf": null,
    "fjhtgts": null,
    "fjhtgcs": null,
    "nhdf": "0.0",
    "nhz": "0",
    "czqyxdf": "5.0",
    "czqyxts": "2929",
    "czqyxlyl": null,
    "qysldf": "0",
    "qyslz": "96",
    "yjljhsdf": "0",
    "yjljhsz": "0.97",
    "gqdf": "0.0",
    "gqz": "1",
    "sjdf": "0",
    "sjz": "0",
    "qyrondf": "0",
    "qyronz": "39",
    "cycdddf": "0",
    "cycddz": "-5"
  },
  {
    "devicename": "荆门1",
    "zfpm": null,
    "zfdf": null,
    "fjhtgdf": null,
    "fjhtgts": null,
    "fjhtgcs": null,
    "nhdf": "0.0",
    "nhz": "0",
    "czqyxdf": "5.0",
    "czqyxts": "2929",
    "czqyxlyl": null,
    "qysldf": "0",
    "qyslz": "96",
    "yjljhsdf": "0",
    "yjljhsz": "0.97",
    "gqdf": "0.0",
    "gqz": "1",
    "sjdf": "0",
    "sjz": "0",
    "qyrondf": "0",
    "qyronz": "39",
    "cycdddf": "0",
    "cycddz": "-5"
  },
  {
    "devicename": "荆门2",
    "zfpm": null,
    "zfdf": null,
    "fjhtgdf": null,
    "fjhtgts": null,
    "fjhtgcs": null,
    "nhdf": "0.0",
    "nhz": "0",
    "czqyxdf": "5.0",
    "czqyxts": "2929",
    "czqyxlyl": null,
    "qysldf": "0",
    "qyslz": "96",
    "yjljhsdf": "0",
    "yjljhsz": "0.97",
    "gqdf": "0.0",
    "gqz": "1",
    "sjdf": "0",
    "sjz": "0",
    "qyrondf": "0",
    "qyronz": "39",
    "cycdddf": "0",
    "cycddz": "-5"
  },
  {
    "devicename": "金陵1",
    "zfpm": null,
    "zfdf": null,
    "fjhtgdf": null,
    "fjhtgts": null,
    "fjhtgcs": null,
    "nhdf": "0.0",
    "nhz": "0",
    "czqyxdf": "5.0",
    "czqyxts": "2929",
    "czqyxlyl": null,
    "qysldf": "0",
    "qyslz": "96",
    "yjljhsdf": "0",
    "yjljhsz": "0.97",
    "gqdf": "0.0",
    "gqz": "1",
    "sjdf": "0",
    "sjz": "0",
    "qyrondf": "0",
    "qyronz": "39",
    "cycdddf": "0",
    "cycddz": "-5"
  },
  {
    "devicename": "金陵2",
    "zfpm": null,
    "zfdf": null,
    "fjhtgdf": null,
    "fjhtgts": null,
    "fjhtgcs": null,
    "nhdf": "0.0",
    "nhz": "0",
    "czqyxdf": "5.0",
    "czqyxts": "2929",
    "czqyxlyl": null,
    "qysldf": "0",
    "qyslz": "96",
    "yjljhsdf": "0",
    "yjljhsz": "0.97",
    "gqdf": "0.0",
    "gqz": "1",
    "sjdf": "0",
    "sjz": "0",
    "qyrondf": "0",
    "qyronz": "39",
    "cycdddf": "0",
    "cycddz": "-5"
  },
  {
    "devicename": "金陵3",
    "zfpm": null,
    "zfdf": null,
    "fjhtgdf": null,
    "fjhtgts": null,
    "fjhtgcs": null,
    "nhdf": "0.0",
    "nhz": "0",
    "czqyxdf": "5.0",
    "czqyxts": "2929",
    "czqyxlyl": null,
    "qysldf": "0",
    "qyslz": "96",
    "yjljhsdf": "0",
    "yjljhsz": "0.97",
    "gqdf": "0.0",
    "gqz": "1",
    "sjdf": "0",
    "sjz": "0",
    "qyrondf": "0",
    "qyronz": "39",
    "cycdddf": "0",
    "cycddz": "-5"
  },
  {
    "devicename": "九江1",
    "zfpm": null,
    "zfdf": null,
    "fjhtgdf": null,
    "fjhtgts": null,
    "fjhtgcs": null,
    "nhdf": "0.0",
    "nhz": "0",
    "czqyxdf": "5.0",
    "czqyxts": "2929",
    "czqyxlyl": null,
    "qysldf": "0",
    "qyslz": "96",
    "yjljhsdf": "0",
    "yjljhsz": "0.97",
    "gqdf": "0.0",
    "gqz": "1",
    "sjdf": "0",
    "sjz": "0",
    "qyrondf": "0",
    "qyronz": "39",
    "cycdddf": "0",
    "cycddz": "-5"
  },
  {
    "devicename": "九江2",
    "zfpm": null,
    "zfdf": null,
    "fjhtgdf": null,
    "fjhtgts": null,
    "fjhtgcs": null,
    "nhdf": "0.0",
    "nhz": "0",
    "czqyxdf": "5.0",
    "czqyxts": "2929",
    "czqyxlyl": null,
    "qysldf": "0",
    "qyslz": "96",
    "yjljhsdf": "0",
    "yjljhsz": "0.97",
    "gqdf": "0.0",
    "gqz": "1",
    "sjdf": "0",
    "sjz": "0",
    "qyrondf": "0",
    "qyronz": "39",
    "cycdddf": "0",
    "cycddz": "-5"
  },
  {
    "devicename": "洛阳1",
    "zfpm": null,
    "zfdf": null,
    "fjhtgdf": null,
    "fjhtgts": null,
    "fjhtgcs": null,
    "nhdf": "0.0",
    "nhz": "0",
    "czqyxdf": "5.0",
    "czqyxts": "2929",
    "czqyxlyl": null,
    "qysldf": "0",
    "qyslz": "96",
    "yjljhsdf": "0",
    "yjljhsz": "0.97",
    "gqdf": "0.0",
    "gqz": "1",
    "sjdf": "0",
    "sjz": "0",
    "qyrondf": "0",
    "qyronz": "39",
    "cycdddf": "0",
    "cycddz": "-5"
  },
  {
    "devicename": "洛阳2",
    "zfpm": null,
    "zfdf": null,
    "fjhtgdf": null,
    "fjhtgts": null,
    "fjhtgcs": null,
    "nhdf": "0.0",
    "nhz": "0",
    "czqyxdf": "5.0",
    "czqyxts": "2929",
    "czqyxlyl": null,
    "qysldf": "0",
    "qyslz": "96",
    "yjljhsdf": "0",
    "yjljhsz": "0.97",
    "gqdf": "0.0",
    "gqz": "1",
    "sjdf": "0",
    "sjz": "0",
    "qyrondf": "0",
    "qyronz": "39",
    "cycdddf": "0",
    "cycddz": "-5"
  },
  {
    "devicename": "茂名2",
    "zfpm": null,
    "zfdf": null,
    "fjhtgdf": null,
    "fjhtgts": null,
    "fjhtgcs": null,
    "nhdf": "0.0",
    "nhz": "0",
    "czqyxdf": "5.0",
    "czqyxts": "2929",
    "czqyxlyl": null,
    "qysldf": "0",
    "qyslz": "96",
    "yjljhsdf": "0",
    "yjljhsz": "0.97",
    "gqdf": "0.0",
    "gqz": "1",
    "sjdf": "0",
    "sjz": "0",
    "qyrondf": "0",
    "qyronz": "39",
    "cycdddf": "0",
    "cycddz": "-5"
  },
  {
    "devicename": "茂名3",
    "zfpm": null,
    "zfdf": null,
    "fjhtgdf": null,
    "fjhtgts": null,
    "fjhtgcs": null,
    "nhdf": "0.0",
    "nhz": "0",
    "czqyxdf": "5.0",
    "czqyxts": "2929",
    "czqyxlyl": null,
    "qysldf": "0",
    "qyslz": "96",
    "yjljhsdf": "0",
    "yjljhsz": "0.97",
    "gqdf": "0.0",
    "gqz": "1",
    "sjdf": "0",
    "sjz": "0",
    "qyrondf": "0",
    "qyronz": "39",
    "cycdddf": "0",
    "cycddz": "-5"
  },
  {
    "devicename": "茂名4",
    "zfpm": null,
    "zfdf": null,
    "fjhtgdf": null,
    "fjhtgts": null,
    "fjhtgcs": null,
    "nhdf": "0.0",
    "nhz": "0",
    "czqyxdf": "5.0",
    "czqyxts": "2929",
    "czqyxlyl": null,
    "qysldf": "0",
    "qyslz": "96",
    "yjljhsdf": "0",
    "yjljhsz": "0.97",
    "gqdf": "0.0",
    "gqz": "1",
    "sjdf": "0",
    "sjz": "0",
    "qyrondf": "0",
    "qyronz": "39",
    "cycdddf": "0",
    "cycddz": "-5"
  },
  {
    "devicename": "齐鲁2",
    "zfpm": null,
    "zfdf": null,
    "fjhtgdf": null,
    "fjhtgts": null,
    "fjhtgcs": null,
    "nhdf": "0.0",
    "nhz": "0",
    "czqyxdf": "5.0",
    "czqyxts": "2929",
    "czqyxlyl": null,
    "qysldf": "0",
    "qyslz": "96",
    "yjljhsdf": "0",
    "yjljhsz": "0.97",
    "gqdf": "0.0",
    "gqz": "1",
    "sjdf": "0",
    "sjz": "0",
    "qyrondf": "0",
    "qyronz": "39",
    "cycdddf": "0",
    "cycddz": "-5"
  },
  {
    "devicename": "齐鲁3",
    "zfpm": null,
    "zfdf": null,
    "fjhtgdf": null,
    "fjhtgts": null,
    "fjhtgcs": null,
    "nhdf": "0.0",
    "nhz": "0",
    "czqyxdf": "5.0",
    "czqyxts": "2929",
    "czqyxlyl": null,
    "qysldf": "0",
    "qyslz": "96",
    "yjljhsdf": "0",
    "yjljhsz": "0.97",
    "gqdf": "0.0",
    "gqz": "1",
    "sjdf": "0",
    "sjz": "0",
    "qyrondf": "0",
    "qyronz": "39",
    "cycdddf": "0",
    "cycddz": "-5"
  },
  {
    "devicename": "清江1",
    "zfpm": null,
    "zfdf": null,
    "fjhtgdf": null,
    "fjhtgts": null,
    "fjhtgcs": null,
    "nhdf": "0.0",
    "nhz": "0",
    "czqyxdf": "5.0",
    "czqyxts": "2929",
    "czqyxlyl": null,
    "qysldf": "0",
    "qyslz": "96",
    "yjljhsdf": "0",
    "yjljhsz": "0.97",
    "gqdf": "0.0",
    "gqz": "1",
    "sjdf": "0",
    "sjz": "0",
    "qyrondf": "0",
    "qyronz": "39",
    "cycdddf": "0",
    "cycddz": "-5"
  },
  {
    "devicename": "清江2",
    "zfpm": null,
    "zfdf": null,
    "fjhtgdf": null,
    "fjhtgts": null,
    "fjhtgcs": null,
    "nhdf": "0.0",
    "nhz": "0",
    "czqyxdf": "5.0",
    "czqyxts": "2929",
    "czqyxlyl": null,
    "qysldf": "0",
    "qyslz": "96",
    "yjljhsdf": "0",
    "yjljhsz": "0.97",
    "gqdf": "0.0",
    "gqz": "1",
    "sjdf": "0",
    "sjz": "0",
    "qyrondf": "0",
    "qyronz": "39",
    "cycdddf": "0",
    "cycddz": "-5"
  },
  {
    "devicename": "青炼",
    "zfpm": null,
    "zfdf": null,
    "fjhtgdf": null,
    "fjhtgts": null,
    "fjhtgcs": null,
    "nhdf": "0.0",
    "nhz": "0",
    "czqyxdf": "5.0",
    "czqyxts": "2929",
    "czqyxlyl": null,
    "qysldf": "0",
    "qyslz": "96",
    "yjljhsdf": "0",
    "yjljhsz": "0.97",
    "gqdf": "0.0",
    "gqz": "1",
    "sjdf": "0",
    "sjz": "0",
    "qyrondf": "0",
    "qyronz": "39",
    "cycdddf": "0",
    "cycddz": "-5"
  },
  {
    "devicename": "青石",
    "zfpm": null,
    "zfdf": null,
    "fjhtgdf": null,
    "fjhtgts": null,
    "fjhtgcs": null,
    "nhdf": "0.0",
    "nhz": "0",
    "czqyxdf": "5.0",
    "czqyxts": "2929",
    "czqyxlyl": null,
    "qysldf": "0",
    "qyslz": "96",
    "yjljhsdf": "0",
    "yjljhsz": "0.97",
    "gqdf": "0.0",
    "gqz": "1",
    "sjdf": "0",
    "sjz": "0",
    "qyrondf": "0",
    "qyronz": "39",
    "cycdddf": "0",
    "cycddz": "-5"
  },
  {
    "devicename": "上海2",
    "zfpm": null,
    "zfdf": null,
    "fjhtgdf": null,
    "fjhtgts": null,
    "fjhtgcs": null,
    "nhdf": "0.0",
    "nhz": "0",
    "czqyxdf": "5.0",
    "czqyxts": "2929",
    "czqyxlyl": null,
    "qysldf": "0",
    "qyslz": "96",
    "yjljhsdf": "0",
    "yjljhsz": "0.97",
    "gqdf": "0.0",
    "gqz": "1",
    "sjdf": "0",
    "sjz": "0",
    "qyrondf": "0",
    "qyronz": "39",
    "cycdddf": "0",
    "cycddz": "-5"
  },
  {
    "devicename": "石炼1",
    "zfpm": null,
    "zfdf": null,
    "fjhtgdf": null,
    "fjhtgts": null,
    "fjhtgcs": null,
    "nhdf": "0.0",
    "nhz": "0",
    "czqyxdf": "5.0",
    "czqyxts": "2929",
    "czqyxlyl": null,
    "qysldf": "0",
    "qyslz": "96",
    "yjljhsdf": "0",
    "yjljhsz": "0.97",
    "gqdf": "0.0",
    "gqz": "1",
    "sjdf": "0",
    "sjz": "0",
    "qyrondf": "0",
    "qyronz": "39",
    "cycdddf": "0",
    "cycddz": "-5"
  },
  {
    "devicename": "石炼3",
    "zfpm": null,
    "zfdf": null,
    "fjhtgdf": null,
    "fjhtgts": null,
    "fjhtgcs": null,
    "nhdf": "0.0",
    "nhz": "0",
    "czqyxdf": "5.0",
    "czqyxts": "2929",
    "czqyxlyl": null,
    "qysldf": "0",
    "qyslz": "96",
    "yjljhsdf": "0",
    "yjljhsz": "0.97",
    "gqdf": "0.0",
    "gqz": "1",
    "sjdf": "0",
    "sjz": "0",
    "qyrondf": "0",
    "qyronz": "39",
    "cycdddf": "0",
    "cycddz": "-5"
  },
  {
    "devicename": "天津",
    "zfpm": null,
    "zfdf": null,
    "fjhtgdf": null,
    "fjhtgts": null,
    "fjhtgcs": null,
    "nhdf": "0.0",
    "nhz": "0",
    "czqyxdf": "5.0",
    "czqyxts": "2929",
    "czqyxlyl": null,
    "qysldf": "0",
    "qyslz": "96",
    "yjljhsdf": "0",
    "yjljhsz": "0.97",
    "gqdf": "0.0",
    "gqz": "1",
    "sjdf": "0",
    "sjz": "0",
    "qyrondf": "0",
    "qyronz": "39",
    "cycdddf": "0",
    "cycddz": "-5"
  },
  {
    "devicename": "武汉1",
    "zfpm": null,
    "zfdf": null,
    "fjhtgdf": null,
    "fjhtgts": null,
    "fjhtgcs": null,
    "nhdf": "0.0",
    "nhz": "0",
    "czqyxdf": "5.0",
    "czqyxts": "2929",
    "czqyxlyl": null,
    "qysldf": "0",
    "qyslz": "96",
    "yjljhsdf": "0",
    "yjljhsz": "0.97",
    "gqdf": "0.0",
    "gqz": "1",
    "sjdf": "0",
    "sjz": "0",
    "qyrondf": "0",
    "qyronz": "39",
    "cycdddf": "0",
    "cycddz": "-5"
  },
  {
    "devicename": "武汉2",
    "zfpm": null,
    "zfdf": null,
    "fjhtgdf": null,
    "fjhtgts": null,
    "fjhtgcs": null,
    "nhdf": "0.0",
    "nhz": "0",
    "czqyxdf": "5.0",
    "czqyxts": "2929",
    "czqyxlyl": null,
    "qysldf": "0",
    "qyslz": "96",
    "yjljhsdf": "0",
    "yjljhsz": "0.97",
    "gqdf": "0.0",
    "gqz": "1",
    "sjdf": "0",
    "sjz": "0",
    "qyrondf": "0",
    "qyronz": "39",
    "cycdddf": "0",
    "cycddz": "-5"
  },
  {
    "devicename": "扬子2",
    "zfpm": null,
    "zfdf": null,
    "fjhtgdf": null,
    "fjhtgts": null,
    "fjhtgcs": null,
    "nhdf": "0.0",
    "nhz": "0",
    "czqyxdf": "5.0",
    "czqyxts": "2929",
    "czqyxlyl": null,
    "qysldf": "0",
    "qyslz": "96",
    "yjljhsdf": "0",
    "yjljhsz": "0.97",
    "gqdf": "0.0",
    "gqz": "1",
    "sjdf": "0",
    "sjz": "0",
    "qyrondf": "0",
    "qyronz": "39",
    "cycdddf": "0",
    "cycddz": "-5"
  },
  {
    "devicename": "燕山2",
    "zfpm": null,
    "zfdf": null,
    "fjhtgdf": null,
    "fjhtgts": null,
    "fjhtgcs": null,
    "nhdf": "0.0",
    "nhz": "0",
    "czqyxdf": "5.0",
    "czqyxts": "2929",
    "czqyxlyl": null,
    "qysldf": "0",
    "qyslz": "96",
    "yjljhsdf": "0",
    "yjljhsz": "0.97",
    "gqdf": "0.0",
    "gqz": "1",
    "sjdf": "0",
    "sjz": "0",
    "qyrondf": "0",
    "qyronz": "39",
    "cycdddf": "0",
    "cycddz": "-5"
  },
  {
    "devicename": "燕山3",
    "zfpm": null,
    "zfdf": null,
    "fjhtgdf": null,
    "fjhtgts": null,
    "fjhtgcs": null,
    "nhdf": "0.0",
    "nhz": "0",
    "czqyxdf": "5.0",
    "czqyxts": "2929",
    "czqyxlyl": null,
    "qysldf": "0",
    "qyslz": "96",
    "yjljhsdf": "0",
    "yjljhsz": "0.97",
    "gqdf": "0.0",
    "gqz": "1",
    "sjdf": "0",
    "sjz": "0",
    "qyrondf": "0",
    "qyronz": "39",
    "cycdddf": "0",
    "cycddz": "-5"
  },
  {
    "devicename": "镇海1",
    "zfpm": null,
    "zfdf": null,
    "fjhtgdf": null,
    "fjhtgts": null,
    "fjhtgcs": null,
    "nhdf": "0.0",
    "nhz": "0",
    "czqyxdf": "5.0",
    "czqyxts": "2929",
    "czqyxlyl": null,
    "qysldf": "0",
    "qyslz": "96",
    "yjljhsdf": "0",
    "yjljhsz": "0.97",
    "gqdf": "0.0",
    "gqz": "1",
    "sjdf": "0",
    "sjz": "0",
    "qyrondf": "0",
    "qyronz": "39",
    "cycdddf": "0",
    "cycddz": "-5"
  },
  {
    "devicename": "镇海2",
    "zfpm": null,
    "zfdf": null,
    "fjhtgdf": null,
    "fjhtgts": null,
    "fjhtgcs": null,
    "nhdf": "0.0",
    "nhz": "0",
    "czqyxdf": "5.0",
    "czqyxts": "2929",
    "czqyxlyl": null,
    "qysldf": "0",
    "qyslz": "96",
    "yjljhsdf": "0",
    "yjljhsz": "0.97",
    "gqdf": "0.0",
    "gqz": "1",
    "sjdf": "0",
    "sjz": "0",
    "qyrondf": "0",
    "qyronz": "39",
    "cycdddf": "0",
    "cycddz": "-5"
  }
]
const columns: TableColumnType<iTableColumn>[] = [
  {
    title: "装置名称",
    dataIndex: "devicename",
    key: "devicename",
  },
  {
    title: "总分\n(满分100分)",
    children:[
      {
        title:"排名",
        dataIndex: "rank",
        key: "rank",
      },
      {
        title:"总分",
        dataIndex: "total",
        key: "total",
      },
    ]
  },
  {
    title: "非计划停工次数\n(满分20分)",
    children:[
      {
        title:"得分",
        dataIndex: "fjhtgdf",
        key: "fjhtgdf",
      },
      {
        title:"天数",
        dataIndex: "fjhtgts",
        key: "fjhtgts",
      },
      {
        title:"次数",
        dataIndex: "fjhtgcs",
        key: "fjhtgcs",
      }
    ]
  },
  {
    title: "能耗/10+损失\n(满分25分)",
    children:[
      {
        title:"得分",
        dataIndex: "nhdf",
        key: "nhdf",
      },
      {
        title:"值",
        dataIndex: "nhz",
        key: "nhz",
      }
    ]
  },
  {
    title: "长周期运行天数\n(满分5分)",
    children:[
      {
        title:"得分",
        dataIndex: "czqyxdf",
        key: "czqyxdf",
      },
      {
        title:"天数",
        dataIndex: "czqyxts",
        key: "czqyxts",
      },
      {
        title:"利用率",
        dataIndex: "czqyxlyl",
        key: "czqyxlyl",
      },
    ]
  },
  {
    title: "尾气排放合格率\n(满分10分)",
    children:[
      {
        title:"得分",
        dataIndex: "yymddf",
        key: "yymddf",
        width:120,
      },
      {
        title:"数值",
        dataIndex: "yymdz",
        key: "yymdz",
        width:120,
      },
    ]
  },
  {
    title: "吸收塔净化尾气H2S含量\n(满分5分)",
    children:[
      {
        title:"得分",
        dataIndex: "jrlrxldf",
        key: "jrlrxldf",
        width:100,
      },
      {
        title:"值",
        dataIndex: "jrlrxlz",
        key: "jrlrxlz",
        width:100,
      },
    ]
  }
];
const isFetching = ref<boolean>(false);
const rowSelection: TableProps["rowSelection"] = {
  onSelect: (record: DataItem, selected: boolean, selectedRows: DataItem[]) => {
    let arr: any = [];
    selectedRows.forEach((item, index) => {
      if (item != undefined) {
        arr.push(item);
      }
    });
  },
  onSelectAll: (
    selected: boolean,
    selectedRows: DataItem[],
    changeRows: DataItem[]
  ) => {
    let arr: any = [];
    selectedRows.forEach((item, index) => {
      if (item != undefined) {
        arr.push(item);
      }
    });
  },
};
const scrollTableOption = reactive({ x: "max-content"});
// const scrollTableOption = reactive({ x: 1200, y: 0 });
// function updateScrollY() {
//   scrollTableOption.y = getTableScrollHeight(".table");
// }
// // 列表
// onMounted(() => {
//   nextTick(() => {
//     updateScrollY()
//   })
// })

function displayText(text:string){
  let _obj = text&&text.split(",")||"",
    _text = _obj&&_obj[0]!="null"&&_obj[0]!="NULL"?_obj[0]:"-";
  // console.log("===text==",text,"===",_text);
  return _text;
}
// 计算总分和排名
function caclTotal(list:any[]){
  let newlist=[];
  // 计算总分
  forEach(list,(v)=>{
    const _total = toNumber(v.fjhtgdf)+toNumber(v.nhdf)+toNumber(v.czqyxdf)+toNumber(v.yymddf)+toNumber(v.jrlrxldf);
    assign(v,{total:_total});
    newlist.push(v);
  })
  // 根据总分排名
  newlist = orderBy(newlist,"total","desc");
  // 排序
  // forEach(newlist,(v,idx)=>{
  //   assign(v,{rank:idx+1})
  // })
  return newlist;
}
const listTableData = ref([]);
function fetchTableData(){
  storeSru.fetchTablePoExamine().then((resp)=>{
    listTableData.value = caclTotal(resp);
  }).catch((err)=>{
    // listTableData.value = caclTotal(TableData);
    // console.log("==err==",err)
    message.error(err.statusText||err.message||err.msg||err||"数据错误!");
  })
}
fetchTableData();

function doExport(){
  const excelData = formatExcelData(columns,listTableData.value);
  // console.log("===do export===",columns,"====",listTableData.value);
  // console.log("====",excelData);

  exportDataToExcel(excelData,"table_device_stability_rate","装置平稳率");
}
defineExpose({
  doExport
});
const currentPage = ref<number>(0);
function onTableChange(pagination:iPagination){
  currentPage.value = (pagination.current)*pagination.pageSize;
}
</script>
<template>
<div class="cmp table_po_examine">
  <a-table 
    :title="()=>{ return '得分排名表'}"
    class="table" 
    bordered 
    :columns="columns" 
    :data-source="listTableData" 
    :rowKey="(record) => { return 'po-examine-' + record.devicename; }" 
    :footer="null"
    :scroll="scrollTableOption"
    :loading="isFetching"
    @change="onTableChange">
    <template #bodyCell="{ column, record, index }">
      <template v-if="column.key==='rank'">{{ index+1+currentPage }}</template>
    </template>
  </a-table>
</div>
</template>
<style lang="less" scoped>
.cmp.table_po_examine{
  &:deep(.table){
    .ant-table-title{text-align:center;}
    .warn{
      color:red;
      font-weight:bold;
    }
  }
}
</style>