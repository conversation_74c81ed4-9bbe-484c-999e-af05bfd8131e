import { defineStore } from "pinia";
import hydroApi from "@/api/hydro.ts";
import { normalReq } from "./request.ts";
import { addUrlQuery } from "@/utils/index";
export default defineStore("useHydroStore", {
  state: () => {
    return {
    }
  },
  getters: {

  },
  actions: {
    fetchCMLifespanProspect(form) {
      let _url = hydroApi.url_cm_lifespan_prospect;
      _url = addUrlQuery(_url, "code", form.code);
      _url = addUrlQuery(_url, "devicetype", form.deviceType);
      _url = addUrlQuery(_url, "pageencode", form.pageCode);
      return normalReq.get(_url);
    },
    // 催化剂分析-节能优化下table接口
    getDeviceCalculationHydroList(form) {
      let _url = hydroApi.url_analyze_energy_table;
      _url = addUrlQuery(_url, "tcode", form.code);
      return normalReq.get(_url);
    },
    // 催化剂分析-节能优化下图表接口
    getDeviceCalculationHydroChart(form) {
      let _url = hydroApi.url_analyze_energy_chart;
      _url = addUrlQuery(_url, "tcode", form.code);
      return normalReq.get(_url);
    },
    fetchRelationInfo(form) {
      let _url = hydroApi.url_relation_info;
      _url = addUrlQuery(_url, "pageencode", form.pageCode);
      _url = addUrlQuery(_url, "devicetype", form.deviceType);
      _url = addUrlQuery(_url, "code", form.code);
      return normalReq.get(_url);
    },
    fetchRelationCatalyzerInfo(form) {
      let _url = hydroApi.url_relation_catalyzer_info;
      _url = addUrlQuery(_url, "devicetype", form.deviceType);
      _url = addUrlQuery(_url, "code", form.code);
      return normalReq.get(_url);
    },
    fetchCatalyzerInfo(form) {
      let _url = hydroApi.url_catalyzer_info;
      _url = addUrlQuery(_url, "devicetype", form.deviceType);
      return normalReq.get(_url);
    },
    fetchDCU(form) {
      let _url = hydroApi.url_dcu;
      _url = addUrlQuery(_url, "devicetype", form.deviceType);
      _url = addUrlQuery(_url, "code", form.code);
      return normalReq.get(_url);
    },
    fetchTableHeatTempDiff(form) {
      let _url = hydroApi.url_po_table_heat_temp_diff;
      _url = addUrlQuery(_url, "type", form.type);
      return normalReq.get(_url);
    },
    fetchLifespanNumberData(form) {
      let _url = hydroApi.url_lifespan_number;
      _url = addUrlQuery(_url, "nodeid", form.nodeId);
      return normalReq.get(_url);
    },
    fetchLifespanNumberDataNew(form) {
      let _url = hydroApi.url_lifespan_numbernew;
      _url = addUrlQuery(_url, "nodeid", form.nodeId);
      return normalReq.get(_url);
    },
    fetchPOChartData(form) {
      let _url = hydroApi.url_po_chart;
      _url = addUrlQuery(_url, "pageencode", form.pageCode);
      _url = addUrlQuery(_url, "starttime", form.beginDate);
      _url = addUrlQuery(_url, "endtime", form.endDate);
      _url = addUrlQuery(_url, "ismacd", form.ismacd);
      _url = addUrlQuery(_url, "ismacdset", form.ismacdset);
      _url = addUrlQuery(_url, "type", form.type);
      _url = addUrlQuery(_url, "timetype", form.timetype);
      _url = addUrlQuery(_url, "dtype", form.dtype);
      _url = addUrlQuery(_url, "devicetype", form.deviceType);
      _url = addUrlQuery(_url, "brand", form.brand);

      return normalReq.get(_url);
    },
    fetchPOTableInfo(form) {
      let _url = hydroApi.url_po_table_info;
      _url = addUrlQuery(_url, "pageenname", form.pageCode);
      _url = addUrlQuery(_url, "devicetype", form.deviceType);
      return normalReq.get(_url);
    },
    // 装置竞赛排名Table
    fetchTablePoExamine() {
      return normalReq.get(hydroApi.url_table_po_examine);
    },
    // 装置竞赛排名Table
    fetchTablePoExamineNew(form) {
      let _url = hydroApi.url_table_po_examineNew;
      _url = addUrlQuery(_url, "devicetype", form.deviceType);
      return normalReq.get(_url);
    },
    // 装置平稳率table表
    fetchTableDeviceStabilityRate(form) {
      let _url = hydroApi.url_table_device_stability_rate;
      _url = addUrlQuery(_url, "devicetype", form.deviceType);
      return normalReq.get(_url);
    },
    // 装置平稳率table表
    fetchTableDeviceStabilityRateNew(form) {
      let _url = hydroApi.url_table_device_stability_ratenew;
      _url = addUrlQuery(_url, "devicetype", form.deviceType);
      return normalReq.get(_url);
    },
    fetchTaodAlarm(form) {
      let _url = hydroApi.url_taod_alarm;
      _url = addUrlQuery(_url, "pageencode", form.pageCode);
      _url = addUrlQuery(_url, "starttime", form.beginDate);
      _url = addUrlQuery(_url, "endtime", form.endDate);
      _url = addUrlQuery(_url, "type", form.type);
      _url = addUrlQuery(_url, "atype", form.atype);
      _url = addUrlQuery(_url, "alarmid", form.alarmid);
      _url = addUrlQuery(_url, "device", form.device);
      return normalReq.get(_url);
    },
    fetchTaodCompanyList() {
      return normalReq.get(hydroApi.url_taod_company_list);
    },
    fetchTcodBalancehydroData(form) {
      let _url = hydroApi.url_tcod_balance_hydro_data;
      _url = addUrlQuery(_url, "pageencode", form.pageCode);
      _url = addUrlQuery(_url, "code", form.deviceCode);
      _url = addUrlQuery(_url, "devicetype", form.deviceType);
      return normalReq.get(_url);
    },
    doTaodCalculate(form) {
      let _url = hydroApi.url_taod_calculate;
      _url = addUrlQuery(_url, "param1", form.param1);
      _url = addUrlQuery(_url, "param2", form.param2);
      _url = addUrlQuery(_url, "param3", form.param3);
      _url = addUrlQuery(_url, "param4", form.param4);
      _url = addUrlQuery(_url, "param5", form.param5);
      _url = addUrlQuery(_url, "param6", form.param6);
      return normalReq.get(_url);
    },
    fetchTaodPfmList(form?: any) {
      let _url = hydroApi.url_taod_pfm_list;
      if (form) {
        _url = addUrlQuery(_url, "nodeid", form.nodeId);
      }
      return normalReq.get(_url);
    },
    fetchTaodEqEntry(form?: any) {
      let _url = hydroApi.url_taod_eq_entry;
      if (form) {
        _url = addUrlQuery(_url, "nodeid", form.nodeId);
      }
      return normalReq.get(_url);
    },
    fetchTaodEqEntryNew(form?: any) {
      let _url = hydroApi.url_taod_eq_entrynew;
      if (form) {
        _url = addUrlQuery(_url, "nodeid", form.nodeId);
      }
      return normalReq.get(_url);
    },
    fetchTaodEqReport(form?: any) {
      let _url = hydroApi.url_taod_eq_report;
      if (form) {
        _url = addUrlQuery(_url, "devicetype", form.deviceType);
      }
      return normalReq.get(_url);
    },
    fetchTaodPfmUnifyList(form) {
      let _url = hydroApi.url_taod_pfm_unify;
      if (form) {
        _url = addUrlQuery(_url, "pageencode", form.pageCode);
        _url = addUrlQuery(_url, "point", form.point);
        _url = addUrlQuery(_url, "pointmark", form.pointMark);
        _url = addUrlQuery(_url, "devicename", form.deviceName);
      }
      return normalReq.get(_url);
    },
    fetchTaodPfmUnifyChart(form) {
      let _url = hydroApi.url_taod_pfm_unify_chart;
      if (form) {
        _url = addUrlQuery(_url, "pageencode", form.pageCode);
        _url = addUrlQuery(_url, "points", form.points);
        _url = addUrlQuery(_url, "starttime", form.beginDate);
        _url = addUrlQuery(_url, "endtime", form.endDate);
      }
      return normalReq.get(_url);
    },
    fetchTaodDailyReport(form) {
      let _url = hydroApi.url_taod_daily_report;
      if (form) {
        _url = addUrlQuery(_url, "pageencode", form.pageCode);
        _url = addUrlQuery(_url, "devicetype", form.deviceType);
        _url = addUrlQuery(_url, "starttime", form.beginDate);
        _url = addUrlQuery(_url, "endtime", form.endDate);
      }
      return normalReq.get(_url);
    }
  }
});