<script lang="ts" setup>
import {ref, reactive,computed,nextTick} from "vue";
import { message } from "ant-design-vue";
import * as echarts from "echarts";
import {filter,isEmpty,round} from "lodash";
import useHydroStore from "@/store/hydro";
import { checkIsDeep } from "@/utils";
const storeHydro = useHydroStore();
document.title="按牌号汇总-催化剂剩余寿命-生产运行概览-加氢催化汽油装置";
const activeKey=ref([""]);
const listPFM = ref([]);
fetchList();
const listFilter = computed(()=>{
  return filter(listPFM.value,(v)=>{
    const text = searchText.value;
    if(!text) return true;
    return !!v.keyWord&&v.keyWord===text;
  });
})
const listRoot = computed(()=>{
  return filter(listFilter.value,(v)=>{ return !v.childList||v.childList.length<=0});
})
const isEmptyListRoot = computed(()=>{
  return !listRoot.value||listRoot.value.length<=0?true:false;
})
const listOther = computed(()=>{
  const list = filter(listFilter.value,(v)=>{ return v.childList&&v.childList.length>0;});
  activeKey.value = list[0]&&list[0].nodeId;
  return list;
})
function fetchList(){
  storeHydro.fetchLifespanNumberDataNew({nodeId:70003}).then((resp)=>{
    listPFM.value = resp.toperationTreeList||[];
  }).catch(err=>{
    // console.log("==err==",err);
    message.error((err&&(err.statusText||err.message||err.msg||err))||"数据错误!");
  });
}
const searchText = ref<string>("");
function doSearch(){

}
const currentPfmUrl = ref<string>("");
interface iDv{
  isShow:boolean
  url:string
  title:string
}
const dvForm = reactive<iDv>({
  isShow:false,
  url:"",
  title:""
});
function doSeePfm(item,parentName?:string){
  dvForm.isShow=true;
  if(parentName){
    dvForm.title=parentName+"-"+item.nodeName;
  }else{
    dvForm.title=item.nodeName;
  }
  
  nextTick(()=>{
    // 加载图表
    initChart();

    fetchChartData(item.nodeName);
  })
}
function onCloseDvPreview(){
  dvForm.isShow=false;
  dvForm.url="";
  dvForm.title="";

  myChart?.clear();
  myChart?.dispose();
  myChart = null;
}
function checkIsDeepList(item){
  return checkIsDeep(item);
}
let myChart:any=null;
type EChartsOption = echarts.EChartsOption;
const option: EChartsOption = reactive({
  title:{
    text:"",
    subtext:"",
    subtextStyle:{
      align:"right"
    },
    right: 160,
    top: -10
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
      crossStyle: {
        color: '#999'
      }
    }
  },
  legend:{
    width:"70%",
    type:"scroll",
    right:120,
    top: 20,
    itemGap:20,
    textStyle:{
      width:320,
      overflow:"break"
    }
  },
  xAxis: [
    {
      type: 'category',
      axisPointer: {
        type: 'shadow'
      },
      axisTick:{
        alignWithLabel:true
      },
      axisLabel:{
        // rotate:270,
        formatter: function (value) {
          return value.split('').join('\n')
        }
      }
    }
  ],
  yAxis: [
    {
      type: 'value',
      min:0,
      name:"月",
      //nameLocation:"center",
      //nameRotate:-270,
      //nameGap:40,
      axiosLine:{
        show:true
      },
      axisLine: {
        show: true
      },
      axisTick: {
        show: true
      }
    }
  ],
  series: [
    {
      type: 'bar',
      data: [],
      barMaxWidth: 25,
      label:{
        show:true,
        position:"top",
        formatter:function(v){
          const value = round(v.value,2);
          return value;
        }
      }
    },
    {
      type: 'bar',
      data: [],
      stack: 'total',
      label:{
        show:true,
        position:"top",
        formatter:function(v){
          const value = round(v.value,2);
          return value;
        }
      }
    },
    {
      type: 'bar',
      data: [],
      stack: 'total',
      label:{
        show:true,
        position:"top",
        formatter:function(v){
          const value = round(v.value,2);
          return value;
        }
      }
    },
  ]
})
function initChart(){
  const doc: any = document,
    chartDom: any = doc.getElementById("chartId");

  if (!myChart) {
    myChart = echarts.init(chartDom);
    myChart.setOption(option);
  }
  nextTick(() => {
    window.addEventListener('resize', function () {
      myChart.resize();
    })
  })
}
function fetchChartData(nodeName:string){
  myChart.showLoading();
  storeHydro.fetchPOChartData({
    pageCode:"POLifespanProspect",
    brand:nodeName,
    deviceType:"Petrol"
  }).then((resp)=>{
    // console.log("===uopk=resp==",resp);
    updateTableData(resp);
  }).catch(err=>{
    // console.log("==err==",err);
    message.error((err&&(err.statusText||err.message||err.msg||err))||"数据错误!");
  });
}
function updateTableData(data){
  if(!myChart||isEmpty(data)) return;
  const opts = myChart.getOption(),
    yczdParamListVo = data.yczdParamListVo||[],
    params:any = yczdParamListVo[0]||{deviceType:"",yczdParamList:[]},
    data1=params.yczdParamList||[];
  // opts.title=data.querytime;
  // console.log("====",opts);
  // opts.title[0].text = params.deviceType;
  opts.title[0].subtext = data.querytime||"";
  opts.xAxis[0].data = data.deviceSum;
  opts.series[0].name=data1[0].parameterName;
  opts.series[0].data = data1[0].parameterValue;

  opts.series[1].name=data1[1].parameterName;
  opts.series[1].data = data1[1].parameterValue;

  opts.series[2].name=data1[2].parameterName;
  opts.series[2].data = data1[2].parameterValue;

  // console.log("===",ret.retResult.yczdParamListVo[0].parameterValue);
  myChart.setOption(opts);
  myChart.hideLoading();
}
</script>
<template>

<div class="page taod_pfm">
  <a-modal 
    class="dv preview" 
    width="90%" 
    v-model:open="dvForm.isShow"
    :footer="null"
    :title="dvForm.title"
    @cancel="onCloseDvPreview">
    <div class="preview" id="chartId"></div>
  </a-modal>
  <div class="filter">
    <div class="field">
      <label>查询条件</label>
      <a-input v-model:value="searchText"></a-input>
    </div>
    <div class="field">
      <a-button @click.stop="doSearch">搜索</a-button>
    </div>
    <div class="tip">输入企业拼音首字母。例如：搜索燕山请输入ys</div>
  </div>
  <div class="panel" v-if="!isEmptyListRoot">
    <a-list class="list" :border="false">
      <a-list-item 
        class="item"
        v-for="item in listRoot" 
        :key="item.nodeUrl"
        :class="{'current':currentPfmUrl===item.nodeUrl}"
        @click.stop="doSeePfm(item)">{{item.nodeName}}</a-list-item>
    </a-list>
  </div>
  <a-collapse class="collapse" v-model:activeKey="activeKey">
    <a-collapse-panel 
      v-for="area in listOther" 
      :key="area.nodeId" 
      :header="area.nodeName">
      <template v-if="checkIsDeepList(area)">
        <div class="panel" v-for="item in area.childList">
          <div class="title">{{item.nodeName}}</div>
          <a-list class="list" :border="false">
            <a-list-item 
              class="item"
              v-for="it in item.childList"
              :key="it.nodeUrl"
              @click.stop="doSeePfm(it,item.nodeName)">{{it.nodeName}}</a-list-item>
          </a-list>
        </div>
      </template>
      <template v-else>
        <a-list class="list" :border="false">
          <a-list-item 
            class="item"
            v-for="item in area.childList"
            :key="item.nodeUrl"
            @click.stop="doSeePfm(item,area.nodeName)">{{item.nodeName}}</a-list-item>
        </a-list>
      </template>
    </a-collapse-panel>
  </a-collapse>
</div>
</template>
<style lang="less" scoped>
@import "@/assets/less/collapse";
.dv.preview{
  .preview{
    border:none;
    width:100%;min-height:680px;
  }
}
.page.taod_pfm{
  >.filter{
    display:flex;
    justify-content: center;
    align-items:center;
    margin-bottom:10px;
    >.field{
      margin-left:10px;
      display:flex;
      align-items: center;
      &:first-child{margin-left:0;}
      >label{
        white-space:nowrap;
        margin-right:10px;
      }
    }
    >.tip{
      margin-left:10px;
      font-size:14px;
      color:gray;
    }
  }
  .collapse();
}
</style>

