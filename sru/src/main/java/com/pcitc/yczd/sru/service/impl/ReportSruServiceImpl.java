package com.pcitc.yczd.sru.service.impl;

import com.pcitc.yczd.sru.service.ICalcFormulaConfSruService;
import com.pcitc.yczd.sru.service.IRTDBSruService;
import com.pcitc.yczd.sru.service.IReportSruService;
import com.pcitc.yczd.sru.utils.YczdCataUtils;
import com.pcitc.yczd.sru.vo.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @program: yczd-server
 * @description: ReportSruServiceImpl
 * @author: pengchong.li
 **/
@Service
public class ReportSruServiceImpl implements IReportSruService {

    @Autowired
    private ICalcFormulaConfSruService iCalcFormulaConfSruService;
    @Autowired
    private IRTDBSruService irtdbSruService;

    public Object GetCommonDeviceTagDataList(String reporttype,String elementid, String searchDate, String endDate)
    {

        Object list = new ArrayList<>();

        List<DevicePropertiesVO> Devicelist = new ArrayList<>();
        Devicelist = GetDeviceTagConfigSave(elementid, searchDate, endDate);
        //list 去重 返回list
        List<DevicePropertiesVO> devicenamelist =
                Devicelist.stream().filter(YczdCataUtils.distinctByKey(o -> o.getDevicename())).collect(Collectors.toList());
        for (int i = 0; i < Devicelist.size(); i++)
        {
            switch (reporttype)
            {
                case "Sru":
                    list = GetSruList(devicenamelist, Devicelist);
                    break;
                case "Hydro":
                    list = GetHydroList(devicenamelist, Devicelist);
                    break;
                case "GasSeparation":
                    list = GetGasSeparationList(devicenamelist, Devicelist);
                    break;
                case "Mtbe":
                    list = GetMtbeList(devicenamelist, Devicelist);
                    break;
                case "Coke":
                    list = GetCokeList(devicenamelist, Devicelist);
                    break;
                case "SolventDeasphalting":
                    list = GetSolventDeasphaltingList(devicenamelist, Devicelist);
                    break;
                case "ContinuousReforming":
                    list = GetContinuousReformingList(devicenamelist, Devicelist);
                    break;
                case "FixedBedReforming":
                    list = GetFixedBedReformingList(devicenamelist, Devicelist);
                    break;
                case "CatalyticGasolineHydro":
                    list = GetCatalyticGasolineHydroList(devicenamelist, Devicelist);
                    break;
                case "CatalyticGasolinePost":
                    list = GetCatalyticGasolinePostList(devicenamelist, Devicelist);
                    break;
                case "AviationKeroseneHydro":
                    list = GetAviationKeroseneHydroList(devicenamelist, Devicelist);
                    break;
                case "Szorb":
                    list = GetSzorbList(devicenamelist, Devicelist);
                    break;
                case "DieselHydro":
                    list = GetDieselHydroList(devicenamelist, Devicelist);
                    break;
                case "WaxOilHydro":
                    list = GetWaxOilHydroList(devicenamelist, Devicelist);
                    break;
                case "ResidueHydro":
                    list = GetResidueHydroList(devicenamelist, Devicelist);
                    break;
                case "HydrogenProduction":
                    list = GetHydrogenProductionList(devicenamelist, Devicelist);
                    break;
                case "SulfurRecovery":
                    list = GetSulfurRecoveryList(devicenamelist, Devicelist);
                    break;
                case "SewageStripping":
                    list = GetSewageStrippingList(devicenamelist, Devicelist);
                    break;
                case "SolventRefining":
                    list = GetSolventRefiningList(devicenamelist, Devicelist);
                    break;
                case "SolventDewaxing":
                    list = GetSolventDewaxingList(devicenamelist, Devicelist);
                    break;
            }
        }

        return list;

    }
    public List<DevicePropertiesVO> GetDeviceTagConfigSave(String MainOperatingConditionsOfTheDeviceid,
                                                          String searchDate, String endDate)
    {

        List<DevicePropertiesVO> Devicelist = new ArrayList<>();
        DeviceTagConfigVO confmodel = new DeviceTagConfigVO();
        confmodel.setReportId(MainOperatingConditionsOfTheDeviceid);
        List<DeviceTagConfigVO> DeviceCalcullist = GetDevicePropertiesVOList(confmodel, searchDate, endDate);
        DeviceCalcullist.stream().forEach(item->{
            DevicePropertiesVO model = new DevicePropertiesVO();
            model.setDevicename(item.getInstallationName());
            model.setField(item.getId().toString());
            model.setFieldName(item.getParameterName());
            model.setDvalue(irtdbSruService.calculateSru(item.getCodeCal(),Integer.parseInt(item.getCodeType()),searchDate,
                    endDate));
            Devicelist.add(model);
        });


        return Devicelist;
    }
    public List<DeviceTagConfigVO> GetDevicePropertiesVOList(DeviceTagConfigVO deviceTagConfigVO, String StartTime,
                                                           String EndTime)
    {
        DeviceTagConfigVO devicemodel = new DeviceTagConfigVO();
        devicemodel.setReportId(deviceTagConfigVO.getReportId());
        devicemodel.setInstallationCode(deviceTagConfigVO.getInstallationCode());
        List<DeviceTagConfigVO> replacelist = iCalcFormulaConfSruService.GetDeviceTagConfigDataList(devicemodel);
        return replacelist;
    }

    /// <summary>
    /// 常减压装置
    /// </summary>
    /// <param name="devicenamelist"></param>
    /// <param name="Devicelist"></param>
    /// <returns></returns>
    public Object GetSruList(List<DevicePropertiesVO> devicenamelist, List<DevicePropertiesVO> Devicelist)
    {
        List<PressReportVO> list = new ArrayList<>();
        for (int i = 0; i < devicenamelist.size(); i++)
        {
            final String devicename=devicenamelist.get(i).getDevicename();
            PressReportVO model = new PressReportVO();
            List<DevicePropertiesVO> Devicelistlist =
                    Devicelist.stream().filter(dev->dev.getDevicename().equals(devicename)).collect(Collectors.toList());
            for (int j = 0; j < Devicelistlist.size(); j++)
            {
                model.setDeviceName(Devicelistlist.get(j).getDevicename());
                model.setShortForInstallation(Devicelistlist.get(j).getDevicename());
                if (Devicelistlist.get(j).getFieldName().equals("加工能力(万吨/年)"))
                {
                    model.setProcessCapacityP(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("加工量(万吨)"))
                {
                    model.setProcessQuantityP(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("负荷率(%)"))
                {
                    model.setLoadRateP(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("运行天数(天)"))
                {
                    model.setDaysOfOperationP(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("累计运行天数(天)"))
                {
                    model.setDaysOfOperationTotalP(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("非计划停工天数(天)"))
                {
                    model.setUnpDowntimeDaysP(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("非计划停工次数(次)"))
                {
                    model.setUnpDowntimenumberP(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("平均硫含量(%)"))
                {
                    model.setSulfurContentAvgP(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("硫含量设防值(%)"))
                {
                    model.setSulfurContentPvP(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("平均密度(kg/m³)"))
                {
                    model.setDensityAvgP(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("原油酸值(KOHmg/g)"))
                {
                    model.setAcidValueOfCrudeOilP(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("脱前含盐(mg/L)"))
                {
                    model.setSaContBeforeRemovalP(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("脱后含盐(mg/L)"))
                {
                    model.setSaContafterRemovalP(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("脱盐合格率(%)"))
                {
                    model.setQualifiedRateDesalinationP(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("脱后含水(%)"))
                {
                    model.setMoistureContentAfterDehP(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("排水含油(μg/g)"))
                {
                    model.setDrainageContainingOilP(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("脱盐率(%)"))
                {
                    model.setDesaltingRateP(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("(初顶＋常顶)气体(%)"))
                {
                    model.setGasP(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("初常顶油(%)"))
                {
                    model.setInitialTopOilP(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("常一线(%)"))
                {
                    model.setChangYixianP(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("常二线(%)"))
                {
                    model.setChangErxianP(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("常三线(%)"))
                {
                    model.setChangSanxianP(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("常四线(%)"))
                {
                    model.setChangSixianP(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("常渣(%)"))
                {
                    model.setConstantResidueP(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("减顶气(%)"))
                {
                    model.setDecapitationGasP(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("减顶油(%)"))
                {
                    model.setReducedTopOilP(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("减一线(%)"))
                {
                    model.setJianYiXianP(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("减二线(%)"))
                {
                    model.setJianErXianP(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("减三线(%)"))
                {
                    model.setJianSanXianP(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("减四线(%)"))
                {
                    model.setJianSiXianP(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("减五线(%)"))
                {
                    model.setJianWuXianP(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("减渣(%)"))
                {
                    model.setSlagReductionP(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("加工损失(%)"))
                {
                    model.setProcessingLossP(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("一次轻收(%)"))
                {
                    model.setOneLightHarvestP(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("总罢"))
                {
                    model.setGeneralStrikeP(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("常渣350℃含量(%)"))
                {
                    model.setConstantResidue350P(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("减渣500℃含量(%)"))
                {
                    model.setConstantResidue500P(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("常一线95%馏出温度(℃)"))
                {
                    model.setChangYixian95P(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("常二线5%馏出温度(℃)"))
                {
                    model.setChangErxian5P(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("常一线与常二线重叠度(℃)"))
                {
                    model.setChangYixianErxianP(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("换热终温(℃)"))
                {
                    model.setFinalheExchaTemperatureP(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("减压塔真空度(kPa)"))
                {
                    model.setVacuumDegreeP(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("初馏塔顶水中铁离子(mg/L)"))
                {
                    model.setPrimaryDistillTowerTieP(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("常压塔顶水中铁离子(mg/L)"))
                {
                    model.setAtmosphericTowerTieP(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("减压塔顶水中铁离子(mg/L)"))
                {
                    model.setVacuumTowerTieP(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("初馏塔顶水中氯离子(mg/L)"))
                {
                    model.setPrimaryDistillTowerClP(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("常压塔顶水中氯离子(mg/L)"))
                {
                    model.setAtmosphericTowerClP(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("减压塔顶水中氯离子(mg/L)"))
                {
                    model.setVacuumTowerClP(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("初馏塔顶水pH值"))
                {
                    model.setPrimaryDistillTowerPHP(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("常压塔顶水pH值"))
                {
                    model.setAtmosphericTowerPHP(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("减压塔顶水pH值"))
                {
                    model.setVacuumTowerPHP(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("常压炉热效率(%)"))
                {
                    model.setTEfAtmosphericFurnaceP(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("减压炉出口温度(℃)"))
                {
                    model.setPreReducFurnaceOutletTeP(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("减压炉热效率(%)"))
                {
                    model.setPreReducFurnacePressP(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("加热炉平均热效率(%)"))
                {
                    model.setEfficiencyOfHeatingAvgP(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("能耗(千克标油/吨)"))
                {
                    model.setEnergyConsumptionP(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("燃料单耗(千克/吨)"))
                {
                    model.setUnitFuelConsumptionP(Devicelistlist.get(j).getDvalue());
                }

            }
            list.add(model);
        }

        return list;
    }

    /// <summary>
    /// 加氢裂化装置
    /// </summary>
    /// <param name="devicenamelist"></param>
    /// <param name="Devicelist"></param>
    public Object GetHydroList(List<DevicePropertiesVO> devicenamelist, List<DevicePropertiesVO> Devicelist)
    {
        List<HydroReportVO> list = new ArrayList<>();
        for (int i = 0; i < devicenamelist.size(); i++)
        {
            HydroReportVO model = new HydroReportVO();
            final String devicename=devicenamelist.get(i).getDevicename();
            List<DevicePropertiesVO> Devicelistlist = Devicelist.stream().filter(dev->dev.getDevicename().equals(devicename)).collect(Collectors.toList());
            for (int j = 0; j < Devicelistlist.size(); j++)
            {
                model.setDeviceName(Devicelistlist.get(j).getDevicename());
                model.setShortForInstallation(Devicelistlist.get(j).getDevicename());
                if (Devicelistlist.get(j).getFieldName().equals("加工能力(万吨/年)"))
                {
                    model.setProcessCapacityH(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("加工量(万吨)"))
                {
                    model.setProcessQuantityH(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("负荷率(%)"))
                {
                    model.setLoadRateH(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("运行天数(天)"))
                {
                    model.setDaysOfOperationH(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("累计运行天数(天)"))
                {
                    model.setDaysOfOperationTotalH(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("非计划停工天数(天)"))
                {
                    model.setUnpDowntimeDaysH(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("非计划停工次数(次)"))
                {
                    model.setUnpDowntimenumberH(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("总液收+原料硫含量+原料氮含量×5%"))
                {
                    model.setRawMaterialSNSumH(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("原料含硫量(%)"))
                {
                    model.setRawMaterialSH(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("原料含氮量(%)"))
                {
                    model.setRawMaterialNH(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("进料密度(20℃)(kg/m³)"))
                {
                    model.setFeedDensityH(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("10%馏出温度(℃)"))
                {
                    model.setDistillationTemper10H(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("30%馏出温度(℃)"))
                {
                    model.setDistillationTemper30H(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("50%馏出温度(℃)"))
                {
                    model.setDistillationTemper50H(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("70%馏出温度(℃)"))
                {
                    model.setDistillationTemper70H(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("90%馏出温度(℃)"))
                {
                    model.setDistillationTemper90H(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("直馏蜡油比例(%)"))
                {
                    model.setStraightRunWaxOilH(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("焦化蜡油比例(%)"))
                {
                    model.setCokerGasOilH(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("脱沥青油比例(%)"))
                {
                    model.setDeasphaltingOilH(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("原料中柴油比例(%)"))
                {
                    model.setMediumDieselFuelH(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("原料中直柴比例"))
                {
                    model.setZhongzhiChaiH(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("原料中焦柴比例"))
                {
                    model.setZhongjiaoChaiH(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("原料中催柴比例"))
                {
                    model.setZhongCuiChaiH(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("干气(%)"))
                {
                    model.setDryGasH(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("液化气(%)"))
                {
                    model.setLiquefiedGasH(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("轻石脑油(%)"))
                {
                    model.setLightNaphthaH(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("重石脑油(%)"))
                {
                    model.setHeavyNaphthaH(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("煤油(%)"))
                {
                    model.setKeroseneH(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("柴油(%)"))
                {
                    model.setDieselOilH(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("尾油产率(%)"))
                {
                    model.setTailOilYieldH(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("加工损失(%)"))
                {
                    model.setProcessingLossH(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("总液收(%)"))
                {
                    model.setLiquidRecoveryTotH(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("尾油BMCI值"))
                {
                    model.setTailOilH(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("新氢耗量(万吨)"))
                {
                    model.setHydroConsumptionH(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("循环比(%)"))
                {
                    model.setCycleRatioH(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("循环氢纯度(%)"))
                {
                    model.setRecycledHydroPurityH(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("循环氢流量(万标立/小时)"))
                {
                    model.setRecycledHydroFlowH(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("化学耗氢(ripp)"))
                {
                    model.setChemicalHydroConRiH(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("化学耗氢(fripp)"))
                {
                    model.setChemicalHydroConFriH(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("氢气利用效率(ripp)"))
                {
                    model.setUtilizationEfficiencyRiH(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("氢气利用效率(fripp)"))
                {
                    model.setUtilizationEfficiencyFriH(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("加热炉热效率(%)"))
                {
                    model.setEfficiencyOfHeatingH(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("能耗(千克标油/吨)"))
                {
                    model.setEnergyConsumptionH(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("燃料单耗(千克/吨)"))
                {
                    model.setUnitFuelConsumptionH(Devicelistlist.get(j).getDvalue());
                }


            }
            list.add(model);
        }

        return list;
    }

    /// <summary>
    /// 气体分离装置
    /// </summary>
    /// <param name="devicenamelist"></param>
    /// <param name="Devicelist"></param>
    public Object GetGasSeparationList(List<DevicePropertiesVO> devicenamelist, List<DevicePropertiesVO> Devicelist)
    {
        List<GasSeparationReportVO> list = new ArrayList<>();
        for (int i = 0; i < devicenamelist.size(); i++)
        {
            GasSeparationReportVO model = new GasSeparationReportVO();
            final String devicename=devicenamelist.get(i).getDevicename();
            List<DevicePropertiesVO> Devicelistlist = Devicelist.stream().filter(dev->dev.getDevicename().equals(devicename)).collect(Collectors.toList());
            for (int j = 0; j < Devicelistlist.size(); j++)
            {
                model.setDeviceName(Devicelistlist.get(j).getDevicename());
                model.setShortForInstallation(Devicelistlist.get(j).getDevicename());
                if (Devicelistlist.get(j).getFieldName().equals("加工能力(万吨/年)"))
                {
                    model.setProcessCapacityGas(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("加工量(万吨)"))
                {
                    model.setProcessQuantityGas(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("负荷率(%)"))
                {
                    model.setLoadRateGas(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("运行天数(天)"))
                {
                    model.setDaysOfOperationGas(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("累计运行天数(天)"))
                {
                    model.setDaysOfOperationTotalGas(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("非计划停工天数(天)"))
                {
                    model.setUnpDowntimeDaysGas(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("非计划停工次数(次)"))
                {
                    model.setUnpDowntimenumberGas(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("原料中丙烯含量(%)"))
                {
                    model.setPropyleneContentGas(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("原料中异丁烯含量(%)"))
                {
                    model.setIsobuteneContentGas(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("原料总硫含量(ppm)"))
                {
                    model.setSulfurContenTotGas(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("原料中焦化液化气比例(%)"))
                {
                    model.setMediumCokingGas(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("碳二收率(%)"))
                {
                    model.setCarbonDioxideGas(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("丙烯收率(%)"))
                {
                    model.setPropyleneGas(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("丙烷收率(%)"))
                {
                    model.setPropaneGas(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("轻碳四收率(%)"))
                {
                    model.setLightCarbonGas(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("重碳四收率(%)"))
                {
                    model.setHeavyCarbonGas(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("碳五收率(%)"))
                {
                    model.setC5Gas(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("加工损失(%)"))
                {
                    model.setProcessingLossGas(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("丙烯纯度(%)"))
                {
                    model.setFinenessGas(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("能耗(千克标油/吨)"))
                {
                    model.setEnergyConsumptionGas(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("低温热占总热源比例 %"))
                {
                    model.setHeatOccupationGas(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("蒸汽耗量(吨/吨原料)"))
                {
                    model.setSteamConsumptionGas(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("耗蒸汽压力等级(MPa)"))
                {
                    model.setPressureLevelGas(Devicelistlist.get(j).getDvalue());
                }

            }
            list.add(model);
        }

        return list;
    }

    /// <summary>
    /// MTBE装置
    /// </summary>
    /// <param name="devicenamelist"></param>
    /// <param name="Devicelist"></param>
    public Object GetMtbeList(List<DevicePropertiesVO> devicenamelist, List<DevicePropertiesVO> Devicelist)
    {
        List<MtbeReportVO> list = new ArrayList<>();
        for (int i = 0; i < devicenamelist.size(); i++)
        {
            MtbeReportVO model = new MtbeReportVO();
            final String devicename=devicenamelist.get(i).getDevicename();
            List<DevicePropertiesVO> Devicelistlist = Devicelist.stream().filter(dev->dev.getDevicename().equals(devicename)).collect(Collectors.toList());
            for (int j = 0; j < Devicelistlist.size(); j++)
            {
                model.setDeviceName(Devicelistlist.get(j).getDevicename());
                model.setShortForInstallation(Devicelistlist.get(j).getDevicename());
                if (Devicelistlist.get(j).getFieldName().equals("加工能力(万吨/年)"))
                {
                    model.setProcessCapacityMTBE(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("液化气处理量(万吨)"))
                {
                    model.setLiquefiedGasMTBE(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("MTBE产量(万吨)"))
                {
                    model.setOutputMTBE(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("负荷率(%)"))
                {
                    model.setLoadRateMTBE(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("运行天数(天)"))
                {
                    model.setDaysOfOperationMTBE(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("累计运行天数(天)"))
                {
                    model.setDaysOfOperationTotalMTBE(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("甲醇处理量(万吨)"))
                {
                    model.setCarbinolMTBE(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("非计划停工天数(天)"))
                {
                    model.setUnpDowntimeDaysMTBE(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("非计划停工次数(次)"))
                {
                    model.setUnpDowntimenumberMTBE(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("甲醇处理量/液化气处理量"))
                {
                    model.setCarbinolLiquefiedGasMTBE(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("液化气中异丁烯含量(%)"))
                {
                    model.setIsobuteneContentMTBE(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("液化气总硫含量(ppm)"))
                {
                    model.setSulfurContenTotMTBE(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("碳二收率(%)"))
                {
                    model.setCarbonDioxideMTBE(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("丙烯收率(%)"))
                {
                    model.setPropyleneMTBE(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("丙烷收率(%)"))
                {
                    model.setPropaneMTBE(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("轻碳四收率(%)"))
                {
                    model.setLightCarbonMTBE(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("重碳四收率(%)"))
                {
                    model.setHeavyCarbonMTBE(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("碳五收率(%)"))
                {
                    model.setC5MTBE(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("加工损失(%)"))
                {
                    model.setProcessingLossMTBE(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("异丁烯转化率(%)"))
                {
                    model.setIsobuteneMTBE(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("MTBE纯度(%)"))
                {
                    model.setFinenessMTBE(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("MTBE硫含量(ppm)"))
                {
                    model.setSulfurContentMTBE(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("能耗(千克标油/吨)"))
                {
                    model.setEnergyConsumptionMTBE(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("燃料单耗(千克/吨)"))
                {
                    model.setUnitFuelConsumptionMTBE(Devicelistlist.get(j).getDvalue());
                }

            }
            list.add(model);
        }

        return list;
    }

    /// <summary>
    /// 延迟焦化装置
    /// </summary>
    /// <param name="devicenamelist"></param>
    /// <param name="Devicelist"></param>
    public Object GetCokeList(List<DevicePropertiesVO> devicenamelist, List<DevicePropertiesVO> Devicelist)
    {
        List<CokeReportVO> list = new ArrayList<>();
        for (int i = 0; i < devicenamelist.size(); i++)
        {
            CokeReportVO model = new CokeReportVO();
            final String devicename=devicenamelist.get(i).getDevicename();
            List<DevicePropertiesVO> Devicelistlist = Devicelist.stream().filter(dev->dev.getDevicename().equals(devicename)).collect(Collectors.toList());
            for (int j = 0; j < Devicelistlist.size(); j++)
            {
                model.setDeviceName(Devicelistlist.get(j).getDevicename());
                model.setShortForInstallation(Devicelistlist.get(j).getDevicename());
                if (Devicelistlist.get(j).getFieldName().equals("加工能力(万吨/年)"))
                {
                    model.setProcessCapacitycoke(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("加工量(万吨)"))
                {
                    model.setProcessQuantitycoke(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("负荷率(%)"))
                {
                    model.setLoadRatecoke(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("运行天数(天)"))
                {
                    model.setDaysOfOperationcoke(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("累计运行天数(天)"))
                {
                    model.setDaysOfOperationTotalcoke(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("非计划停工天数(天)"))
                {
                    model.setUnpDowntimeDayscoke(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("非计划停工次数(次)"))
                {
                    model.setUnpDowntimenumbercoke(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("原料密度×总液收"))
                {
                    model.setDensityZyscoke(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("生焦系数"))
                {
                    model.setCokingCoefficientcoke(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("生焦指数(%)"))
                {
                    model.setCokingIndexcoke(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("催化油浆比例(%)"))
                {
                    model.setCatalyticSlurrycoke(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("掺炼污泥、浮渣、油泥比例(%)"))
                {
                    model.setBlendedSludgecoke(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("脱油沥青比例(%)"))
                {
                    model.setDeoiledAsphaltcoke(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("原料硫含量(%)"))
                {
                    model.setSulfurContencoke(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("密度(kg/m³)"))
                {
                    model.setDensitycoke(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("原料残碳(%)"))
                {
                    model.setCarbonResiduecoke(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("总氮(%)"))
                {
                    model.setTotalNitrogencoke(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("Ni含量(μg/g)"))
                {
                    model.setContentNicoke(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("沥青质含量(%)"))
                {
                    model.setAsphaltenecoke(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("500℃馏出量(%)"))
                {
                    model.setDistillatecoke(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("干气产率(%)"))
                {
                    model.setDryGasYieldcoke(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("液化气(%)"))
                {
                    model.setLiquefiedGascoke(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("汽油(%)"))
                {
                    model.setGasolinecoke(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("柴油(%)"))
                {
                    model.setDieselOilcoke(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("蜡油(%)"))
                {
                    model.setWaxOilcoke(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("甩油(%)"))
                {
                    model.setOilJettisoncoke(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("石油焦产率(%)"))
                {
                    model.setPetroleumCokecoke(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("加工损失(%)"))
                {
                    model.setProcessingLosscoke(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("轻油收率(%)"))
                {
                    model.setLightOilYieldcoke(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("C3+总液收(%)"))
                {
                    model.setLiquidRecoveryC3coke(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("C5+总液收(%)"))
                {
                    model.setLiquidRecoveryC5coke(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("干气中C3以上含量(%)"))
                {
                    model.setDryGasC3coke(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("焦化蜡油硫含量(%)"))
                {
                    model.setSulfurContentcoke(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("焦化蜡油氮含量(%)"))
                {
                    model.setNitrogenContentcoke(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("焦化蜡油残炭(%)"))
                {
                    model.setCarbonResiduecokeJh(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("焦化蜡油95%馏出温度/℃"))
                {
                    model.setDistillation95coke(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("石油焦硫含量(%)"))
                {
                    model.setSulfurContentoilcoke(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("石油焦挥发分(%)"))
                {
                    model.setVolatileMattercoke(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("焦化柴油硫含量(%)"))
                {
                    model.setSulfurContentOilcokejh(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("焦化柴油氮含量(%)"))
                {
                    model.setNitrogenContentOilcoke(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("焦化柴油95%馏出温度(℃)"))
                {
                    model.setDistillation95Oilcoke(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("循环比"))
                {
                    model.setCycleRatiocoke(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("生焦时间(小时)"))
                {
                    model.setCokingTimecoke(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("分馏塔底温度(℃)"))
                {
                    model.setTowerTemperaturecoke(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("焦炭塔顶温度(℃)"))
                {
                    model.setTopTemperaturecoke(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("焦炭塔塔顶压力(MPa)"))
                {
                    model.setTopPressurecoke(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("加热炉出口温度(℃)"))
                {
                    model.setOutletTempercoke(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("焦炭塔底进料温度(℃)"))
                {
                    model.setFeedTempercoke(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("焦炭塔顶急冷油前温度(℃)"))
                {
                    model.setTemperatureBeforeOilcoke(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("分馏塔上抽出温度/℃"))
                {
                    model.setExtractionTemperTopcoke(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("分馏塔上返塔温度/℃"))
                {
                    model.setReturnTowerTemperTopcoke(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("分馏塔中抽出温度/℃"))
                {
                    model.setExtractionTempercentcoke(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("分馏塔中返塔温度/℃"))
                {
                    model.setReturnTowerTempercentcoke(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("分馏塔下抽出温度/℃"))
                {
                    model.setExtractionTemperDowcoke(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("分馏塔下返塔温度/℃"))
                {
                    model.setReturnTowerTemperDowcoke(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("加热炉热效率(%)"))
                {
                    model.setEfficiencyOfHeatingcoke(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("石油焦扣水比例(%)"))
                {
                    model.setWaterDeductioncoke(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("能耗(千克标油/吨)"))
                {
                    model.setEnergyConsumptioncoke(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("燃料单耗(千克/吨)"))
                {
                    model.setUnitFuelConsumptioncoke(Devicelistlist.get(j).getDvalue());
                }

            }
            list.add(model);
        }

        return list;
    }

    /// <summary>
    /// 溶剂脱沥青装置
    /// </summary>
    /// <param name="devicenamelist"></param>
    /// <param name="Devicelist"></param>
    public Object GetSolventDeasphaltingList(List<DevicePropertiesVO> devicenamelist, List<DevicePropertiesVO> Devicelist)
    {
        List<SolventDeasphaltingReportVO> list = new ArrayList<>();
        for (int i = 0; i < devicenamelist.size(); i++)
        {
            SolventDeasphaltingReportVO model = new SolventDeasphaltingReportVO();
            final String devicename=devicenamelist.get(i).getDevicename();
            List<DevicePropertiesVO> Devicelistlist = Devicelist.stream().filter(dev->dev.getDevicename().equals(devicename)).collect(Collectors.toList());
            for (int j = 0; j < Devicelistlist.size(); j++)
            {
                model.setDeviceName(Devicelistlist.get(j).getDevicename());
                model.setShortForInstallation(Devicelistlist.get(j).getDevicename());
                if (Devicelistlist.get(j).getFieldName().equals("加工能力(万吨/年)"))
                {
                    model.setProcessCapacitySolvent(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("加工量(万吨)"))
                {
                    model.setProcessQuantitySolvent(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("负荷率(%)"))
                {
                    model.setLoadRateSolvent(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("运行天数(天)"))
                {
                    model.setDaysOfOperationSolvent(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("累计运行天数(天)"))
                {
                    model.setDaysOfOperationTotalSolvent(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("非计划停工天数(天)"))
                {
                    model.setUnpDowntimeDaysSolvent(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("非计划停工次数(次)"))
                {
                    model.setUnpDowntimenumberSolvent(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("残炭差"))
                {
                    model.setResidualCarbonSolvent(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("硫含量差"))
                {
                    model.setSulfurContentPoSolvent(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("原料硫含量(%)"))
                {
                    model.setSulfurContentSolvent(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("密度(kg/m³)"))
                {
                    model.setDensitySolvent(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("原料残碳%"))
                {
                    model.setRawMaterialsSolvent(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("100℃黏度"))
                {
                    model.setViscosity100Solvent(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("80℃或120℃黏度"))
                {
                    model.setViscosity80Solvent(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("Ni质量分数/%"))
                {
                    model.setMassFractionNiSolvent(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("V质量分数/%"))
                {
                    model.setMassFractionVSolvent(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("原料氮含量"))
                {
                    model.setNitrogenContentSolvent(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("减压渣油(%)"))
                {
                    model.setVacuumResidueSolvent(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("催化油浆(%)"))
                {
                    model.setCatalyticSlurrySolvent(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("丙烷(%)"))
                {
                    model.setPropaneSolvent(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("正丁烷(%)"))
                {
                    model.setNbutaneSolvent(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("异丁烷(%)"))
                {
                    model.setIsobutaneSolvent(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("轻脱沥青油(%)"))
                {
                    model.setLightLeachingSolvent(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("重脱沥青油(%)"))
                {
                    model.setHeavyLeachingSolvent(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("脱沥青油(%)"))
                {
                    model.setDeaerationSolvent(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("脱油沥青(%)"))
                {
                    model.setDeoilingSolvent(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("加工损失(%)"))
                {
                    model.setProcessingLossSolvent(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("脱沥青油硫含量(%)"))
                {
                    model.setDeaerationShlSolvent(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("脱沥青油密度(kg/m3)"))
                {
                    model.setDeaerationMdSolvent(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("脱沥青油残炭(%)"))
                {
                    model.setDeaerationCtSolvent(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("脱沥青油80℃黏度"))
                {
                    model.setDeaerationNd80Solvent(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("脱沥青油100℃黏度"))
                {
                    model.setDeaerationNd100Solvent(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("脱沥青油氮质量分数"))
                {
                    model.setDeaerationNSolvent(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("脱沥青油Ni质量分数"))
                {
                    model.setDeaerationNiSolvent(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("脱沥青油V质量分数"))
                {
                    model.setDeaerationVSolvent(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("脱油沥青软化点(℃)"))
                {
                    model.setDeaerationRhdSolvent(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("脱油沥青针入度(1/10mm)"))
                {
                    model.setDeaerationRdSolvent(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("脱油沥青延度(25℃)(cm)"))
                {
                    model.setDeaerationYdSolvent(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("溶剂比"))
                {
                    model.setSolventRatioSolvent(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("抽提塔顶温度(℃)"))
                {
                    model.setExtractionTowerTWdSolvent(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("抽提塔底温度(℃)"))
                {
                    model.setExtractionTowerDWdSolvent(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("抽提塔顶压力(MPa)"))
                {
                    model.setExtractionTowerTYlSolvent(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("超临界塔底温度(℃)"))
                {
                    model.setSupercriticalWdSolvent(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("超临界塔压力(MPa)"))
                {
                    model.setSupercriticalYlSolvent(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("能耗(千克标油/吨)"))
                {
                    model.setEnergyConsumptionSolvent(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("燃料单耗(kg/t)"))
                {
                    model.setUnitFuelConsumptionSolvent(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("溶剂单耗(kg/t)"))
                {
                    model.setSolventConDhSolvent(Devicelistlist.get(j).getDvalue());
                }

            }
            list.add(model);
        }

        return list;
    }

    /// <summary>
    /// 连续重整装置
    /// </summary>
    /// <param name="devicenamelist"></param>
    /// <param name="Devicelist"></param>
    public Object GetContinuousReformingList(List<DevicePropertiesVO> devicenamelist, List<DevicePropertiesVO> Devicelist)
    {
        List<ContinuousReformingReportVO> list = new ArrayList<>();
        for (int i = 0; i < devicenamelist.size(); i++)
        {
            ContinuousReformingReportVO model = new ContinuousReformingReportVO();
            final String devicename=devicenamelist.get(i).getDevicename();
            List<DevicePropertiesVO> Devicelistlist = Devicelist.stream().filter(dev->dev.getDevicename().equals(devicename)).collect(Collectors.toList());
            for (int j = 0; j < Devicelistlist.size(); j++)
            {
                model.setDeviceName(Devicelistlist.get(j).getDevicename());
                model.setShortForInstallation(Devicelistlist.get(j).getDevicename());
                if (Devicelistlist.get(j).getFieldName().equals("加工能力(万吨/年)"))
                {
                    model.setProcessCapacityCon(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("预分馏进料量(万吨)"))
                {
                    model.setFeedQuantityYfCon(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("重整进料量(万吨)"))
                {
                    model.setFeedQuantityCzCon(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("负荷率(%)"))
                {
                    model.setLoadRateCon(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("运行天数(天)"))
                {
                    model.setDaysOfOperationCon(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("累计运行天数(天)"))
                {
                    model.setDaysOfOperationTotalCon(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("非计划停工天数(天)"))
                {
                    model.setUnpDowntimeDaysCon(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("非计划停工次数(次)"))
                {
                    model.setUnpDowntimenumberCon(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("石脑油初馏点(℃)"))
                {
                    model.setNaphthaCldCon(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("石脑油终馏点(℃)"))
                {
                    model.setNaphthaZldCon(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("石脑油硫含量(ppm)"))
                {
                    model.setNaphthaShlCon(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("石脑油氯含量(ppm)"))
                {
                    model.setNaphthaLhlCon(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("重整进料芳潜(%)"))
                {
                    model.setAromaticPotentialCon(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("芳烃差(%)"))
                {
                    model.setAromaticsDifferCon(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("直馏石脑油比例(%)"))
                {
                    model.setNaphthaZlCon(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("加氢裂化石脑油比例(%)"))
                {
                    model.setNaphthaLhCon(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("加氢焦化汽油比例(%)"))
                {
                    model.setNaphthaJhCon(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("其他(%)"))
                {
                    model.setOtherCon(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("N+2A(%)"))
                {
                    model.setN2ACon(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("干气(%)"))
                {
                    model.setDryGasCon(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("液化气(%)"))
                {
                    model.setLiquefiedGasCon(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("丁-戊烷油(%)"))
                {
                    model.setPentaneOilCon(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("重整生成油(%)"))
                {
                    model.setGeneratedOilCon(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("纯氢产率(%)"))
                {
                    model.setHydroYieldCon(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("苯收率(%)"))
                {
                    model.setBenzeneYieldCon(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("甲苯收率(%)"))
                {
                    model.setBenzeneYieldJCon(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("重芳烃收率(%)"))
                {
                    model.setYieldOfHeavyCon(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("非芳烃(%)"))
                {
                    model.setNonAromaticCon(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("生成油芳含(%)"))
                {
                    model.setGeneratedOilFhCon(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("生成油RON"))
                {
                    model.setGeneratedOilRONCon(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("辛烷值桶"))
                {
                    model.setOctaneNumberBarCon(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("芳烃产率(%)"))
                {
                    model.setAromaticHydroCon(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("能耗(千克标油/吨)"))
                {
                    model.setEnergyConsumptionCon(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("燃料单耗(千克/吨)"))
                {
                    model.setUnitFuelConsumptionCon(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("能耗因数"))
                {
                    model.setEnergyConsumCon(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("加权平均床层入口温度WAIT(℃)"))
                {
                    model.setInletTemperatureCon(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("加热炉热效率(%)"))
                {
                    model.setHeatingFurnaceCon(Devicelistlist.get(j).getDvalue());
                }

            }
            list.add(model);
        }

        return list;
    }

    /// <summary>
    /// 固定床重整装置
    /// </summary>
    /// <param name="devicenamelist"></param>
    /// <param name="Devicelist"></param>
    public Object GetFixedBedReformingList(List<DevicePropertiesVO> devicenamelist, List<DevicePropertiesVO> Devicelist)
    {
        List<FixedBedReformingReportVO> list = new ArrayList<>();
        for (int i = 0; i < devicenamelist.size(); i++)
        {
            FixedBedReformingReportVO model = new FixedBedReformingReportVO();
            final String devicename=devicenamelist.get(i).getDevicename();
            List<DevicePropertiesVO> Devicelistlist = Devicelist.stream().filter(dev->dev.getDevicename().equals(devicename)).collect(Collectors.toList());
            for (int j = 0; j < Devicelistlist.size(); j++)
            {
                model.setDeviceName(Devicelistlist.get(j).getDevicename());
                model.setShortForInstallation(Devicelistlist.get(j).getDevicename());
                if (Devicelistlist.get(j).getFieldName().equals("加工能力(万吨/年)"))
                {
                    model.setProcessCapacityFixed(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("预分馏进料量(万吨)"))
                {
                    model.setFeedQuantityYfFixed(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("重整进料量(万吨)"))
                {
                    model.setFeedQuantityCzFixed(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("负荷率(%)"))
                {
                    model.setLoadRateFixed(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("运行天数(天)"))
                {
                    model.setDaysOfOperationFixed(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("累计运行天数(天)"))
                {
                    model.setDaysOfOperationTotalFixed(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("非计划停工天数(天)"))
                {
                    model.setUnpDowntimeDaysFixed(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("非计划停工次数(次)"))
                {
                    model.setUnpDowntimenumberFixed(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("直馏石脑油比例(%)"))
                {
                    model.setNaphthaZlFixed(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("加氢裂化石脑油比例(%)"))
                {
                    model.setNaphthaLhFixed(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("加氢焦化汽油比例(%)"))
                {
                    model.setNaphthaJhFixed(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("其他(%)"))
                {
                    model.setOtherFixed(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("石脑油初馏点(℃)"))
                {
                    model.setNaphthaCldFixed(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("石脑油终馏点(℃)"))
                {
                    model.setNaphthaZldFixed(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("石脑油硫含量(ppm)"))
                {
                    model.setNaphthaShlFixed(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("石脑油氯含量(ppm)"))
                {
                    model.setNaphthaLhlFixed(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("原料芳潜(%)"))
                {
                    model.setAromaticPotentialFixed(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("干气(%)"))
                {
                    model.setDryGasFixed(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("液化气(%)"))
                {
                    model.setLiquefiedGasFixed(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("丁-戊烷油(%)"))
                {
                    model.setPentaneOilFixed(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("重整生成油(%)"))
                {
                    model.setReformedOilFixed(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("纯氢产率(%)"))
                {
                    model.setHydroYieldFixed(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("苯收率(%)"))
                {
                    model.setBenzeneYieldFixed(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("甲苯收率(%)"))
                {
                    model.setBenzeneYieldJFixed(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("重芳烃收率(%)"))
                {
                    model.setYieldOfHeavyFixed(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("非芳烃(%)"))
                {
                    model.setNonAromaticFixed(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("纯氢产率(%)"))
                {
                    model.setHydroYield2Fixed(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("加工损失(%)"))
                {
                    model.setProcessingLossFixed(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("生成油芳含(%)"))
                {
                    model.setGenerateOilContaFixed(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("芳烃差(%)"))
                {
                    model.setAromaticsDifferenceFixed(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("生成油RON"))
                {
                    model.setGeneratedOilRONFixed(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("重整生成油RON*重整生成油收率"))
                {
                    model.setReformedOilRONFixed(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("芳烃产率(%)"))
                {
                    model.setAromaticHydroFixed(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("能耗(千克标油/吨)"))
                {
                    model.setEnergyConsumptionFixed(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("能耗因数"))
                {
                    model.setEnergyConsumFixed(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("燃料单耗(千克/吨)"))
                {
                    model.setUnitFuelConsumptionFixed(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("加权平均床层入口温度WAIT(℃)"))
                {
                    model.setInletTemperatureFixed(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("加热炉热效率(%)"))
                {
                    model.setHeatingFurnaceFixed(Devicelistlist.get(j).getDvalue());
                }

            }
            list.add(model);
        }

        return list;
    }

    /// <summary>
    /// 催化汽油加氢装置
    /// </summary>
    /// <param name="devicenamelist"></param>
    /// <param name="Devicelist"></param>
    public Object GetCatalyticGasolineHydroList(List<DevicePropertiesVO> devicenamelist, List<DevicePropertiesVO> Devicelist)
    {
        List<CatalyticGasolineHydroReportVO> list = new ArrayList<>();
        for (int i = 0; i < devicenamelist.size(); i++)
        {
            CatalyticGasolineHydroReportVO model = new CatalyticGasolineHydroReportVO();
            final String devicename=devicenamelist.get(i).getDevicename();
            List<DevicePropertiesVO> Devicelistlist = Devicelist.stream().filter(dev->dev.getDevicename().equals(devicename)).collect(Collectors.toList());
            for (int j = 0; j < Devicelistlist.size(); j++)
            {
                model.setDeviceName(Devicelistlist.get(j).getDevicename());
                model.setShortForInstallation(Devicelistlist.get(j).getDevicename());
                if (Devicelistlist.get(j).getFieldName().equals("加工能力(万吨/年)"))
                {
                    model.setProcessCapacityCata(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("加工量(万吨)"))
                {
                    model.setProcessQuantityCata(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("负荷率(%)"))
                {
                    model.setLoadRateCata(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("运行天数(天)"))
                {
                    model.setDaysOfOperationCata(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("累计运行天数(天)"))
                {
                    model.setDaysOfOperationTotalCata(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("非计划停工天数(天)"))
                {
                    model.setUnpDowntimeDaysCata(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("非计划停工次数(次)"))
                {
                    model.setUnpDowntimenumberCata(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("原料硫含量(ppm)"))
                {
                    model.setSulfurContentCata(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("烯烃含量(%)"))
                {
                    model.setOlefinContentCata(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("气体(%)"))
                {
                    model.setGasCata(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("精制汽油(%)"))
                {
                    model.setRefinedGasolineCata(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("加工损失(%)"))
                {
                    model.setProcessingLossCata(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("精制汽油硫含量(ppm)"))
                {
                    model.setRefinedGasolineShlCata(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("精制汽油直链烷烃含量(%)"))
                {
                    model.setRefinedGasolineZlwthlCata(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("精制汽油芳潜(%)"))
                {
                    model.setRefinedGasolineFqCata(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("脱硫率(%)"))
                {
                    model.setDesulfurizationRateCata(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("精制汽油烯烃含量(%)"))
                {
                    model.setRefinedGasolineXthlCata(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("加热炉热效率(%)"))
                {
                    model.setHeatingFurnaceCata(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("主反应器入口温度"))
                {
                    model.setMainReactorRkwdCata(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("主反应器温升"))
                {
                    model.setMainReactorWsCata(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("新氢耗量(万吨)"))
                {
                    model.setNewHydrogenConsumCata(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("能耗(千克标油/吨)"))
                {
                    model.setEnergyConsumptionCata(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("燃料单耗(千克/吨)"))
                {
                    model.setUnitFuelConsumptionCata(Devicelistlist.get(j).getDvalue());
                }

            }
            list.add(model);
        }

        return list;
    }

    /// <summary>
    /// 催化汽油后加氢装置
    /// </summary>
    /// <param name="devicenamelist"></param>
    /// <param name="Devicelist"></param>
    public Object GetCatalyticGasolinePostList(List<DevicePropertiesVO> devicenamelist, List<DevicePropertiesVO> Devicelist)
    {
        List<CatalyticGasolinePostReportVO> list = new ArrayList<>();
        for (int i = 0; i < devicenamelist.size(); i++)
        {
            CatalyticGasolinePostReportVO model = new CatalyticGasolinePostReportVO();
            final String devicename=devicenamelist.get(i).getDevicename();
            List<DevicePropertiesVO> Devicelistlist = Devicelist.stream().filter(dev->dev.getDevicename().equals(devicename)).collect(Collectors.toList());
            for (int j = 0; j < Devicelistlist.size(); j++)
            {
                model.setDeviceName(Devicelistlist.get(j).getDevicename());
                model.setShortForInstallation(Devicelistlist.get(j).getDevicename());
                if (Devicelistlist.get(j).getFieldName().equals("加工能力(万吨/年)"))
                {
                    model.setProcessCapacityPost(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("加工量(万吨)"))
                {
                    model.setProcessQuantityPost(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("负荷率(%)"))
                {
                    model.setLoadRatePost(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("运行天数(天)"))
                {
                    model.setDaysOfOperationPost(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("累计运行天数(天)"))
                {
                    model.setDaysOfOperationTotalPost(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("非计划停工天数(天)"))
                {
                    model.setUnpDowntimeDaysPost(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("非计划停工次数(次)"))
                {
                    model.setUnpDowntimenumberPost(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("RON损失×（原料烯烃含量-产品烯烃含量）/脱硫率^2"))
                {
                    model.setRONLossOrxthlPost(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("原料硫含量(ppm)"))
                {
                    model.setRawMaterialShlPost(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("原料烯烃含量(%)"))
                {
                    model.setRawMaterialXthlPost(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("原料芳烃含量"))
                {
                    model.setRawMaterialFthlPost(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("原料RON"))
                {
                    model.setRawMaterialRonPost(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("气体(%)"))
                {
                    model.setGasPost(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("石脑油(%)"))
                {
                    model.setNaphthaPost(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("精制汽油(%)"))
                {
                    model.setRefinedGasoline2Post(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("加工损失(%)"))
                {
                    model.setProcessingLossPost(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("脱后轻汽油硫含量"))
                {
                    model.setGasolineAfterRemovalPost(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("精制汽油硫含量(ppm)"))
                {
                    model.setRefinedGasolineLhlPost(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("精制汽油RON"))
                {
                    model.setRefinedGasolineRonPost(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("精制汽油抗爆指数"))
                {
                    model.setRefinedGasolineKbzsPost(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("精制汽油烯烃含量(%)"))
                {
                    model.setRefinedGasolinethlPost(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("烯烃差(%)"))
                {
                    model.setOlefinDifferencePost(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("脱硫率(%)"))
                {
                    model.setDesulfurizationRatePost(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("硫差(ppm)"))
                {
                    model.setSulfurDifferencePost(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("RON损失"))
                {
                    model.setRONLossPost(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("主反应器入口温度"))
                {
                    model.setInletTemperaturePost(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("主反应器温升"))
                {
                    model.setTemperatureRisePost(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("能耗(千克标油/吨)"))
                {
                    model.setEnergyConsumptionPost(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("燃料单耗(千克/吨)"))
                {
                    model.setUnitFuelConsumptionPost(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("加热炉热效率(%)"))
                {
                    model.setHeatingFurnacePost(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("新氢耗量(万吨)"))
                {
                    model.setNewHydroHlPost(Devicelistlist.get(j).getDvalue());
                }

            }
            list.add(model);
        }

        return list;
    }

    /// <summary>
    /// 航煤加氢装置
    /// </summary>
    /// <param name="devicenamelist"></param>
    /// <param name="Devicelist"></param>
    public Object GetAviationKeroseneHydroList(List<DevicePropertiesVO> devicenamelist, List<DevicePropertiesVO> Devicelist)
    {
        List<AviationKeroseneHydroReportVO> list = new ArrayList<>();
        for (int i = 0; i < devicenamelist.size(); i++)
        {
            AviationKeroseneHydroReportVO model = new AviationKeroseneHydroReportVO();
            final String devicename=devicenamelist.get(i).getDevicename();
            List<DevicePropertiesVO> Devicelistlist = Devicelist.stream().filter(dev->dev.getDevicename().equals(devicename)).collect(Collectors.toList());
            for (int j = 0; j < Devicelistlist.size(); j++)
            {
                model.setDeviceName(Devicelistlist.get(j).getDevicename());
                model.setShortForInstallation(Devicelistlist.get(j).getDevicename());
                if (Devicelistlist.get(j).getFieldName().equals("加工能力(万吨/年)"))
                {
                    model.setProcessCapacityAvia(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("加工量(万吨)"))
                {
                    model.setProcessQuantityAvia(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("负荷率(%)"))
                {
                    model.setLoadRateAvia(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("运行天数(天)"))
                {
                    model.setDaysOfOperationAvia(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("累计运行天数(天)"))
                {
                    model.setDaysOfOperationTotalAvia(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("非计划停工天数(天)"))
                {
                    model.setUnpDowntimeDaysAvia(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("非计划停工次数(次)"))
                {
                    model.setUnpDowntimenumberAvia(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("原料硫含量(ppm)"))
                {
                    model.setRawMaterialShlAvia(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("原料氮含量"))
                {
                    model.setNitrogenContentAvia(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("气体(%)"))
                {
                    model.setGasAvia(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("石脑油(%)"))
                {
                    model.setNaphthaAvia(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("精制航煤(%)"))
                {
                    model.setRefinedAviationCoalAvia(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("加工损失(%)"))
                {
                    model.setProcessingLossAvia(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("精制煤油硫醇性硫(ppm)"))
                {
                    model.setRefinedAviationCoalCAvia(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("精制煤油硫含量(%)"))
                {
                    model.setRefinedAviationCoalSAvia(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("脱硫率(%)"))
                {
                    model.setDesulfurizationRateAvia(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("反应温度(℃)"))
                {
                    model.setReactionTemperAvia(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("能耗(千克标油/吨)"))
                {
                    model.setEnergyConsumptionAvia(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("燃料单耗(千克/吨)"))
                {
                    model.setUnitFuelConsumptionAvia(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("加热炉热效率(%)"))
                {
                    model.setHeatingFurnaceAvia(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("新氢耗量(万吨)"))
                {
                    model.setNewHydroHlAvia(Devicelistlist.get(j).getDvalue());
                }

            }
            list.add(model);
        }

        return list;
    }

    /// <summary>
    /// SZorb装置
    /// </summary>
    /// <param name="devicenamelist"></param>
    /// <param name="Devicelist"></param>
    public Object GetSzorbList(List<DevicePropertiesVO> devicenamelist, List<DevicePropertiesVO> Devicelist)
    {
        List<SzorbReportVO> list = new ArrayList<>();
        for (int i = 0; i < devicenamelist.size(); i++)
        {
            SzorbReportVO model = new SzorbReportVO();
            final String devicename=devicenamelist.get(i).getDevicename();
            List<DevicePropertiesVO> Devicelistlist = Devicelist.stream().filter(dev->dev.getDevicename().equals(devicename)).collect(Collectors.toList());

            for (int j = 0; j < Devicelistlist.size(); j++)
            {
                model.setDeviceName(Devicelistlist.get(j).getDevicename());
                model.setShortForInstallation(Devicelistlist.get(j).getDevicename());
                if (Devicelistlist.get(j).getFieldName().equals("加工能力(万吨/年)"))
                {
                    model.setProcessCapacitySzorb(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("加工量(万吨)"))
                {
                    model.setProcessQuantitySzorb(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("负荷率(%)"))
                {
                    model.setLoadRateSzorb(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("运行天数(天)"))
                {
                    model.setDaysOfOperationSzorb(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("累计运行天数(天)"))
                {
                    model.setDaysOfOperationTotalSzorb(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("非计划停工天数(天)"))
                {
                    model.setUnpDowntimeDaysSzorb(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("非计划停工次数(次)"))
                {
                    model.setUnpDowntimenumberSzorb(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("原料硫含量(ppm)"))
                {
                    model.setRawMaterialShlSzorb(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("原料烯烃含量(%)"))
                {
                    model.setRawMaterialXthlSzorb(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("原料RON"))
                {
                    model.setRawMaterialRonSzorb(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("气体(%)"))
                {
                    model.setGasSzorb(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("精制汽油(%)"))
                {
                    model.setRefinedGasolineSzorb(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("加工损失(%)"))
                {
                    model.setProcessingLossSzorb(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("脱后轻汽油硫含量"))
                {
                    model.setGasolineAfterRemovalSzorb(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("精制汽油RON"))
                {
                    model.setRefinedGasolineRONSzorb(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("精制汽油硫含量(μg/g)"))
                {
                    model.setRefinedGasolineShlSzorb(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("精制汽油烯烃含量(%)"))
                {
                    model.setRefinedGasolineXthlSzorb(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("烯烃差(%)"))
                {
                    model.setOlefinDifferenceSzorb(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("待生吸附剂硫含量(%)"))
                {
                    model.setSpentAdsorSulfurSzorb(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("再生吸附剂硫含量(%)"))
                {
                    model.setRegdAdsorSulfurSzorb(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("空速(1/h)"))
                {
                    model.setAirspeedSzorb(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("RON损失"))
                {
                    model.setLossRONSzorb(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("硫差(ppm)"))
                {
                    model.setSulfurDifferenceSzorb(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("反应温度(℃)"))
                {
                    model.setReactionTempSzorb(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("反应压力(kPa)"))
                {
                    model.setReactionPressureSzorb(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("吸附剂单耗(kg/t)"))
                {
                    model.setConsumpOfAdsorbSzorb(Devicelistlist.get(j).getDvalue());
                }

            }
            list.add(model);
        }

        return list;
    }

    /// <summary>
    /// 柴油加氢装置
    /// </summary>
    /// <param name="devicenamelist"></param>
    /// <param name="Devicelist"></param>
    /// <returns></returns>
    public Object GetDieselHydroList(List<DevicePropertiesVO> devicenamelist, List<DevicePropertiesVO> Devicelist)
    {
        List<DieselHydroReportVO> list = new ArrayList<>();
        for (int i = 0; i < devicenamelist.size(); i++)
        {
            DieselHydroReportVO model = new DieselHydroReportVO();
            final String devicename=devicenamelist.get(i).getDevicename();
            List<DevicePropertiesVO> Devicelistlist = Devicelist.stream().filter(dev->dev.getDevicename().equals(devicename)).collect(Collectors.toList());
            for (int j = 0; j < Devicelistlist.size(); j++)
            {
                model.setDeviceName(Devicelistlist.get(j).getDevicename());
                model.setShortForInstallation(Devicelistlist.get(j).getDevicename());
                if (Devicelistlist.get(j).getFieldName().equals("加工能力(万吨/年)"))
                {
                    model.setProcessCapacityDiese(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("加工量(万吨)"))
                {
                    model.setProcessQuantityDiese(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("负荷率(%)"))
                {
                    model.setLoadRateDiese(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("运行天数(天)"))
                {
                    model.setDaysOfOperationDiese(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("累计运行天数(天)"))
                {
                    model.setDaysOfOperationTotalDiese(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("非计划停工天数(天)"))
                {
                    model.setUnpDowntimeDaysDiese(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("非计划停工次数(次)"))
                {
                    model.setUnpDowntimenumberDiese(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("原料硫含量(%)"))
                {
                    model.setRawMaterialShlDiese(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("直馏柴油比例(%)"))
                {
                    model.setDieselOilZlDiese(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("催化柴油比例(%)"))
                {
                    model.setDieselOilChDiese(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("焦化柴油比例(%)"))
                {
                    model.setDieselOilJhDiese(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("焦化汽油比例"))
                {
                    model.setGasolineJhDiese(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("蜡油或渣油加氢柴油比例"))
                {
                    model.setGasolineLyOrZyDiese(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("气体(%)"))
                {
                    model.setGasDiese(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("石脑油(%)"))
                {
                    model.setNaphthaDiese(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("精制柴油(%)"))
                {
                    model.setRefinedDieselDiese(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("加工损失(%)"))
                {
                    model.setProcessingLossDiese(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("十六烷值指数"))
                {
                    model.setIndexNumberDiese(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("精制柴油多环芳烃含量(%)"))
                {
                    model.setRefinedDieselDhftDiese(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("精制柴油硫含量(ppm)"))
                {
                    model.setRefinedDieselShlDiese(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("脱硫率(%)"))
                {
                    model.setDesulfuriRateDiese(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("多环芳烃饱和率(%)"))
                {
                    model.setSaturationRateDiese(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("加权平均反应温度(℃)"))
                {
                    model.setInletTemperatureDiese(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("反应器入口压力"))
                {
                    model.setInletPressureDiese(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("加热炉热效率(%)"))
                {
                    model.setHeatingFurnaceDiese(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("新氢耗量(万吨)"))
                {
                    model.setNewHydroHlDiese(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("能耗(千克标油/吨)"))
                {
                    model.setEnergyConsumptionDiese(Devicelistlist.get(j).getDvalue());
                }

            }
            list.add(model);
        }

        return list;
    }

    /// <summary>
    /// 蜡油加氢装置
    /// </summary>
    /// <param name="devicenamelist"></param>
    /// <param name="Devicelist"></param>
    public Object GetWaxOilHydroList(List<DevicePropertiesVO> devicenamelist, List<DevicePropertiesVO> Devicelist)
    {
        List<WaxOilHydroReportVO> list = new ArrayList<>();
        for (int i = 0; i < devicenamelist.size(); i++)
        {
            WaxOilHydroReportVO model = new WaxOilHydroReportVO();
            final String devicename=devicenamelist.get(i).getDevicename();
            List<DevicePropertiesVO> Devicelistlist = Devicelist.stream().filter(dev->dev.getDevicename().equals(devicename)).collect(Collectors.toList());
            for (int j = 0; j < Devicelistlist.size(); j++)
            {
                model.setDeviceName(Devicelistlist.get(j).getDevicename());
                model.setShortForInstallation(Devicelistlist.get(j).getDevicename());
                if (Devicelistlist.get(j).getFieldName().equals("加工能力(万吨/年)"))
                {
                    model.setProcessCapacityWaxOil(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("加工量(万吨)"))
                {
                    model.setProcessQuantityWaxOil(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("负荷率(%)"))
                {
                    model.setLoadRateWaxOil(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("运行天数(天)"))
                {
                    model.setDaysOfOperationWaxOil(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("累计运行天数(天)"))
                {
                    model.setDaysOfOperationTotalWaxOil(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("非计划停工天数(天)"))
                {
                    model.setUnpDowntimeDaysWaxOil(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("非计划停工次数(次)"))
                {
                    model.setUnpDowntimenumberWaxOil(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("直馏蜡油比例(%)"))
                {
                    model.setScaleZllyWaxOil(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("脱沥青油比例(%)"))
                {
                    model.setScaleTlqyWaxOil(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("焦化蜡油比例(%)"))
                {
                    model.setScaleJhlyWaxOil(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("原料中柴油比例(%)"))
                {
                    model.setScaleZcyWaxOil(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("直柴比例"))
                {
                    model.setScaleZcWaxOil(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("焦柴比例"))
                {
                    model.setScaleJcWaxOil(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("催柴比例"))
                {
                    model.setScaleCcWaxOil(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("硫含量(%)"))
                {
                    model.setSulfurContentWaxOil(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("气体(%)"))
                {
                    model.setGasWaxOil(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("石脑油收率(%)"))
                {
                    model.setNaphthaSlWaxOil(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("柴油(%)"))
                {
                    model.setDieselOilWaxOil(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("精制蜡油(%)"))
                {
                    model.setRefinedWaxOilWaxOil(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("加工损失(%)"))
                {
                    model.setProcessingLossWaxOil(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("精制蜡油硫含量(%)"))
                {
                    model.setRefinedWaxOilShlWaxOil(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("脱硫率(%)"))
                {
                    model.setDesulfuriRateWaxOil(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("氮含量"))
                {
                    model.setNitrogenContentWaxOil(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("脱氮率(%)"))
                {
                    model.setDenitrogenRateWaxOil(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("精制反应温度(℃)"))
                {
                    model.setRefiningReactTemWaxOil(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("反应温升"))
                {
                    model.setReactionTemperWaxOil(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("反应器入口压力"))
                {
                    model.setInletPressureWaxOil(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("加热炉热效率(%)"))
                {
                    model.setHeatingFurnaceWaxOil(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("新氢耗量(万吨)"))
                {
                    model.setNewHydroHlWaxOil(Devicelistlist.get(j).getDvalue());
                }

            }
            list.add(model);
        }

        return list;
    }

    /// <summary>
    /// 渣油加氢装置
    /// </summary>
    /// <param name="devicenamelist"></param>
    /// <param name="Devicelist"></param>
    public Object GetResidueHydroList(List<DevicePropertiesVO> devicenamelist, List<DevicePropertiesVO> Devicelist)
    {
        List<ResidueHydroReportVO> list = new ArrayList<>();
        for (int i = 0; i < devicenamelist.size(); i++)
        {
            ResidueHydroReportVO model = new ResidueHydroReportVO();
            final String devicename=devicenamelist.get(i).getDevicename();
            List<DevicePropertiesVO> Devicelistlist = Devicelist.stream().filter(dev->dev.getDevicename().equals(devicename)).collect(Collectors.toList());
            for (int j = 0; j < Devicelistlist.size(); j++)
            {
                model.setDeviceName(Devicelistlist.get(j).getDevicename());
                model.setShortForInstallation(Devicelistlist.get(j).getDevicename());
                if (Devicelistlist.get(j).getFieldName().equals("加工能力(万吨/年)"))
                {
                    model.setProcessCapacityResid(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("加工量(万吨)"))
                {
                    model.setProcessQuantityResid(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("负荷率(%)"))
                {
                    model.setLoadRateResid(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("运行天数(天)"))
                {
                    model.setDaysOfOperationResid(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("累计运行天数(天)"))
                {
                    model.setDaysOfOperationTotalResid(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("非计划停工天数(天)"))
                {
                    model.setUnpDowntimeDaysResid(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("非计划停工次数(次)"))
                {
                    model.setUnpDowntimenumberResid(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("脱硫率(%)"))
                {
                    model.setDesulfurizationRateResid(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("脱钙率(%)"))
                {
                    model.setDecalcificationRateResid(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("脱铁率(%)"))
                {
                    model.setIronRemovalRateResid(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("脱钒率(%)"))
                {
                    model.setVanadiumRemoRateResid(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("脱镍率(%)"))
                {
                    model.setNickelRemovalRateResid(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("残炭脱除率(%)"))
                {
                    model.setRemovalRateResid(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("脱氮率(%)"))
                {
                    model.setDenitrogenationRateResid(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("综合脱除率(%)"))
                {
                    model.setRemovalRateZHResid(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("加氢渣油量/（减渣量+新氢耗量）"))
                {
                    model.setHydroResidualOilResid(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("原料含硫量(%)"))
                {
                    model.setRawMaterialHllResid(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("原料含氮量(%)"))
                {
                    model.setRawMaterialHdlResid(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("原料含镍量(ppm)"))
                {
                    model.setRawMaterialHnlResid(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("原料含钙量(ppm)"))
                {
                    model.setRawMaterialHglResid(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("原料含钒量(ppm)"))
                {
                    model.setRawMaterialHflResid(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("原料含铁量(ppm)"))
                {
                    model.setRawMaterialHtlResid(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("原料Ni+Fe+V+Ca含量(ppm)"))
                {
                    model.setRawMaterialNiResid(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("原料残炭(%)"))
                {
                    model.setRawMaterialCarbonResid(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("减渣比例(%)"))
                {
                    model.setScaleJzResid(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("常渣比例(%)"))
                {
                    model.setScaleCzResid(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("蜡油比例(%)"))
                {
                    model.setScaleLyResid(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("原料中柴油比例(%)"))
                {
                    model.setRawMaterialScaleResid(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("气体(%)"))
                {
                    model.setGasResid(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("石脑油(%)"))
                {
                    model.setNaphthaSlResid(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("柴油(%)"))
                {
                    model.setDieselOilResid(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("加氢渣油(%)"))
                {
                    model.setHydroResidueResid(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("产品含镍量(ppm)"))
                {
                    model.setProductHnlResid(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("产品含钙量(ppm)"))
                {
                    model.setProductHglResid(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("产品含钒量(ppm)"))
                {
                    model.setProductHflResid(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("产品含铁量(ppm)"))
                {
                    model.setProductHtlResid(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("产品含氮量(%)"))
                {
                    model.setProductHdlResid(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("产品含硫量(%)"))
                {
                    model.setProductHslResid(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("残炭(%)"))
                {
                    model.setCarbonResidueResid(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("加工损失(%)"))
                {
                    model.setProcessingLossResid(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("高换效率"))
                {
                    model.setHighExchangeEfficResid(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("管程换热前温度"))
                {
                    model.setHeatExchangeGcqwdResid(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("管程换热后温度"))
                {
                    model.setHeatExchangeGchwdResid(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("壳程换热前温度"))
                {
                    model.setHeatExchangeKcqwdResid(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("壳程换热后温度"))
                {
                    model.setHeatExchangeKchwdResid(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("管程换热前压力"))
                {
                    model.setHeatExchangeGcqylResid(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("管程换热后压力"))
                {
                    model.setHeatExchangeGchylResid(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("壳程换热前压力"))
                {
                    model.setHeatExchangeKcqylResid(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("壳程换热后压力"))
                {
                    model.setHeatExchangeKchylResid(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("新氢耗量(万吨)"))
                {
                    model.setNewHydroConsumpResid(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("循环氢纯度(%)"))
                {
                    model.setHydroPurityResid(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("循环氢流量(万标立/小时)"))
                {
                    model.setHydrogenFlowResid(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("化学耗氢(ripp)"))
                {
                    model.setHydroConsumpripResid(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("化学耗氢(fripp)"))
                {
                    model.setHydroConsumpfripResid(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("氢气利用效率(ripp)"))
                {
                    model.setUtilizEfficripResid(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("氢气利用效率(fripp)"))
                {
                    model.setUtilizEfficfripResid(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("加热炉热效率(%)"))
                {
                    model.setHeatingFurnaceResid(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("能耗(千克标油/吨)"))
                {
                    model.setEnergyConsumptionResid(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("燃料单耗(千克/吨)"))
                {
                    model.setUnitFuelConsumptionResid(Devicelistlist.get(j).getDvalue());
                }

            }
            list.add(model);
        }

        return list;
    }

    /// <summary>
    /// 制氢装置
    /// </summary>
    /// <param name="devicenamelist"></param>
    /// <param name="Devicelist"></param>
    public Object GetHydrogenProductionList(List<DevicePropertiesVO> devicenamelist, List<DevicePropertiesVO> Devicelist)
    {
        List<HydrogenProductionReportVO> list = new ArrayList<>();
        for (int i = 0; i < devicenamelist.size(); i++)
        {
            HydrogenProductionReportVO model = new HydrogenProductionReportVO();
            final String devicename=devicenamelist.get(i).getDevicename();
            List<DevicePropertiesVO> Devicelistlist = Devicelist.stream().filter(dev->dev.getDevicename().equals(devicename)).collect(Collectors.toList());
            for (int j = 0; j < Devicelistlist.size(); j++)
            {
                model.setDeviceName(Devicelistlist.get(j).getDevicename());
                model.setShortForInstallation(Devicelistlist.get(j).getDevicename());
                if (Devicelistlist.get(j).getFieldName().equals("产纯氢能力(万吨/年)"))
                {
                    model.setProcessCapacityHydrogen(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("产纯氢量(万吨)"))
                {
                    model.setProcessQuantityHydrogen(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("负荷率(%)"))
                {
                    model.setLoadRateHydrogen(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("运行天数(天)"))
                {
                    model.setDaysOfOperationHydrogen(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("累计运行天数(天)"))
                {
                    model.setDaysOfOperationTotalHydrogen(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("石脑油比例(%)"))
                {
                    model.setNaphthaHydrogen(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("干气比例(%)"))
                {
                    model.setDryGasHydrogen(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("然气比例(%)"))
                {
                    model.setNaturalGasHydrogen(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("甲醇量(吨)"))
                {
                    model.setMethanolContentHydrogen(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("纯氢产率(%)"))
                {
                    model.setPureDydroYieldHydrogen(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("CO+CO2含量(ppm)"))
                {
                    model.setCOCO2ContHydrogen(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("尾气中氢含量(%)"))
                {
                    model.setHydroContentHydrogen(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("水碳比"))
                {
                    model.setWaterCarbonRatioHydrogen(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("转化温度(℃)"))
                {
                    model.setConverTemperHydrogen(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("转化炉热效率(%)"))
                {
                    model.setThermalEfficiencyHydrogen(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("能耗(千克标油/吨纯氢气)"))
                {
                    model.setEnergyConsumptionHydrogen(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("燃料单耗(千克/吨)"))
                {
                    model.setUnitFuelConsumptionHydrogen(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("非计划停工天数(天)"))
                {
                    model.setUnpDowntimeDaysHydrogen(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("非计划停工次数(次)"))
                {
                    model.setUnpDowntimenumberHydrogen(Devicelistlist.get(j).getDvalue());
                }

            }
            list.add(model);
        }

        return list;
    }

    /// <summary>
    /// 硫回收装置
    /// </summary>
    /// <param name="devicenamelist"></param>
    /// <param name="Devicelist"></param>
    public Object GetSulfurRecoveryList(List<DevicePropertiesVO> devicenamelist, List<DevicePropertiesVO> Devicelist)
    {
        List<SulfurRecoveryReportVO> list = new ArrayList<>();
        for (int i = 0; i < devicenamelist.size(); i++)
        {
            SulfurRecoveryReportVO model = new SulfurRecoveryReportVO();
            final String devicename=devicenamelist.get(i).getDevicename();
            List<DevicePropertiesVO> Devicelistlist = Devicelist.stream().filter(dev->dev.getDevicename().equals(devicename)).collect(Collectors.toList());
            for (int j = 0; j < Devicelistlist.size(); j++)
            {
                model.setDeviceName(Devicelistlist.get(j).getDevicename());
                model.setShortForInstallation(Devicelistlist.get(j).getDevicename());
                if (Devicelistlist.get(j).getFieldName().equals("加工能力(万吨/年)"))
                {
                    model.setProcessCapacitySulf(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("酸性气处理量(万吨)"))
                {
                    model.setAcidGasProcesSulf(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("负荷率(%)"))
                {
                    model.setLoadRateSulf(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("运行天数(天)"))
                {
                    model.setDaysOfOperationSulf(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("累计运行天数(天)"))
                {
                    model.setDaysOfOperationTotalSulf(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("原料硫化氢含量(%)"))
                {
                    model.setRawMaterialHsSulf(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("原料硫含量(%)"))
                {
                    model.setRawMaterialShlSulf(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("原料氨含量(%)"))
                {
                    model.setRawMaterialAhlSulf(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("原料烃含量(%)"))
                {
                    model.setRawMaterialThlSulf(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("原料CO2含量(%)"))
                {
                    model.setRawMaterialCO2Sulf(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("硫磺/硫酸产量(万吨)"))
                {
                    model.setBrimStoneSulf(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("硫回收率(计算)(%)"))
                {
                    model.setSulfurRecRateJSSulf(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("硫回收率(填报)(%)"))
                {
                    model.setSulfurRecRateTBSulf(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("吸收塔净化后尾气硫化氢含量(ppm)"))
                {
                    model.setHydroSulfideContSulf(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("尾气排放二氧化硫含量(mg/m3)"))
                {
                    model.setSulfurDioxideSulf(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("尾气排放合格率(%)"))
                {
                    model.setPassRateSulf(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("能耗千克标油/吨硫磺（吨硫酸）"))
                {
                    model.setEnergyConsumptionSulf(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("燃料单耗(千克/吨)"))
                {
                    model.setUnitFuelConsumptionSulf(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("非计划停工天数(天)"))
                {
                    model.setUnpDowntimeDaysSulf(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("非计划停工次数(次)"))
                {
                    model.setUnpDowntimenumberSulf(Devicelistlist.get(j).getDvalue());
                }

            }
            list.add(model);
        }

        return list;
    }

    /// <summary>
    /// 污水汽提装置
    /// </summary>
    /// <param name="devicenamelist"></param>
    /// <param name="Devicelist"></param>
    public Object GetSewageStrippingList(List<DevicePropertiesVO> devicenamelist, List<DevicePropertiesVO> Devicelist)
    {
        List<SewageStrippingReportVO> list = new ArrayList<>();
        for (int i = 0; i < devicenamelist.size(); i++)
        {
            SewageStrippingReportVO model = new SewageStrippingReportVO();
            final String devicename=devicenamelist.get(i).getDevicename();
            List<DevicePropertiesVO> Devicelistlist = Devicelist.stream().filter(dev->dev.getDevicename().equals(devicename)).collect(Collectors.toList());
            for (int j = 0; j < Devicelistlist.size(); j++)
            {
                model.setDeviceName(Devicelistlist.get(j).getDevicename());
                model.setShortForInstallation(Devicelistlist.get(j).getDevicename());
                if (Devicelistlist.get(j).getFieldName().equals("加工能力(万吨/年)"))
                {
                    model.setProcessCapacitySewage(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("加工量(万吨)"))
                {
                    model.setProcessQuantitySewage(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("负荷率(%)"))
                {
                    model.setLoadRateSewage(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("运行天数(天)"))
                {
                    model.setDaysOfOperationSewage(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("累计运行天数(天)"))
                {
                    model.setDaysOfOperationTotalSewage(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("原料中氨含量(ppm)"))
                {
                    model.setRawMaterialAhlSewage(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("原料中H2S含量(ppm)"))
                {
                    model.setRawMaterialH2SSewage(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("原料中烃含量(ppm)"))
                {
                    model.setRawMaterialThlSewage(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("净化水产率(%)"))
                {
                    model.setPurifyingAquaticSewage(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("氨产率(%)"))
                {
                    model.setAmmoniaYieldSewage(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("酸性气率(%)"))
                {
                    model.setAcidGasRateSewage(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("加工损失(%)"))
                {
                    model.setProcessingLossSewage(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("净化水氨含量(ppm)"))
                {
                    model.setAmmoniaYieldJhsSewage(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("净化水H2S含量(ppm)"))
                {
                    model.setAmmoniaYieldH2SSewage(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("酸性气H2S含量(%)"))
                {
                    model.setAcidGasH2SSewage(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("酸性气烃含量(%)"))
                {
                    model.setAcidGasThlSewage(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("能耗(千克标油/吨)"))
                {
                    model.setEnergyConsumptionSewage(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("蒸汽耗量(吨/吨原料)"))
                {
                    model.setSteamConsumptionSewage(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("耗蒸汽压力等级(MPa)"))
                {
                    model.setPressureRatingSewage(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("非计划停工天数(天)"))
                {
                    model.setUnpDowntimeDaysSewage(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("非计划停工次数(次)"))
                {
                    model.setUnpDowntimenumberSewage(Devicelistlist.get(j).getDvalue());
                }

            }
            list.add(model);
        }

        return list;
    }

    /// <summary>
    /// 溶剂精制装置
    /// </summary>
    /// <param name="devicenamelist"></param>
    /// <param name="Devicelist"></param>
    public Object GetSolventRefiningList(List<DevicePropertiesVO> devicenamelist, List<DevicePropertiesVO> Devicelist)
    {
        List<SolventRefiningReportVO> list = new ArrayList<>();
        for (int i = 0; i < devicenamelist.size(); i++)
        {
            SolventRefiningReportVO model = new SolventRefiningReportVO();
            final String devicename=devicenamelist.get(i).getDevicename();
            List<DevicePropertiesVO> Devicelistlist = Devicelist.stream().filter(dev->dev.getDevicename().equals(devicename)).collect(Collectors.toList());
            for (int j = 0; j < Devicelistlist.size(); j++)
            {
                model.setDeviceName(Devicelistlist.get(j).getDevicename());
                model.setShortForInstallation(Devicelistlist.get(j).getDevicename());
                if (Devicelistlist.get(j).getFieldName().equals("加工能力(万吨/年)"))
                {
                    model.setProcessCapacitySolven(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("加工量(万吨)"))
                {
                    model.setProcessQuantitySolven(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("负荷率(%)"))
                {
                    model.setLoadRateSolven(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("运行天数(天)"))
                {
                    model.setDaysOfOperationSolven(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("累计运行天数(天)"))
                {
                    model.setDaysOfOperationTotalSolven(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("平均酸值(%)"))
                {
                    model.setAcidValueSolven(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("粘度指数"))
                {
                    model.setViscosityIndexSolven(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("精制油收率(%)"))
                {
                    model.setOilYieldJzSolven(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("抽出油收率(%)"))
                {
                    model.setOilYieldCcSolven(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("倾点(℃)"))
                {
                    model.setPourPointSolven(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("精制油粘度指数"))
                {
                    model.setViscosityIndexJzySolven(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("粘指差"))
                {
                    model.setStickinessDiffSolven(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("溶剂单耗(kg/t)"))
                {
                    model.setSolventConPerUnitSolven(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("精制液加热炉热效率(%)"))
                {
                    model.setHeatingFurnaceJzySolven(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("抽出液加热炉热效率(%)"))
                {
                    model.setHeatingFurnaceCcySolven(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("馏出口合格率(%)"))
                {
                    model.setPassRateSolven(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("能耗(千克标油/吨)"))
                {
                    model.setEnergyConsumptionSolven(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("燃料单耗(千克/吨)"))
                {
                    model.setUnitFuelConsumptionSolven(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("非计划停工天数(天)"))
                {
                    model.setUnpDowntimeDaysSolven(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("非计划停工次数(次)"))
                {
                    model.setUnpDowntimenumberSolven(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("产品粘指/原料粘指"))
                {
                    model.setProductAdhesiveSolven(Devicelistlist.get(j).getDvalue());
                }

            }
            list.add(model);
        }

        return list;
    }

    /// <summary>
    /// 溶剂脱蜡装置
    /// </summary>
    /// <param name="devicenamelist"></param>
    /// <param name="Devicelist"></param>
    public Object GetSolventDewaxingList(List<DevicePropertiesVO> devicenamelist, List<DevicePropertiesVO> Devicelist)
    {
        List<SolventDewaxingReportVO> list = new ArrayList<>();
        for (int i = 0; i < devicenamelist.size(); i++)
        {
            SolventDewaxingReportVO model = new SolventDewaxingReportVO();
            final String devicename=devicenamelist.get(i).getDevicename();
            List<DevicePropertiesVO> Devicelistlist = Devicelist.stream().filter(dev->dev.getDevicename().equals(devicename)).collect(Collectors.toList());
            for (int j = 0; j < Devicelistlist.size(); j++)
            {
                model.setDeviceName(Devicelistlist.get(j).getDevicename());
                model.setShortForInstallation(Devicelistlist.get(j).getDevicename());
                if (Devicelistlist.get(j).getFieldName().equals("加工能力(万吨/年)"))
                {
                    model.setProcessCapacityDewax(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("加工量(万吨)"))
                {
                    model.setProcessQuantityDewax(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("负荷率(%)"))
                {
                    model.setLoadRateDewax(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("运行天数(天)"))
                {
                    model.setDaysOfOperationDewax(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("累计运行天数(天)"))
                {
                    model.setDaysOfOperationTotalDewax(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("脱蜡油收率(%)"))
                {
                    model.setOilYieldDewax(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("脱油蜡收率(%)"))
                {
                    model.setWaxYieldDewax(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("蜡下油(%)"))
                {
                    model.setOilUnderWaxDewax(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("蜡膏(%)"))
                {
                    model.setCerateDewax(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("脱蜡油+脱油蜡总收率(%)"))
                {
                    model.setDewaxingOilDewax(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("溶剂单耗"))
                {
                    model.setSolventConPerUnitDewax(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("能耗(千克标油/吨)"))
                {
                    model.setEnergyConsumptionDewax(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("燃料单耗(千克/吨)"))
                {
                    model.setUnitFuelConsumptionDewax(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("非计划停工天数(天)"))
                {
                    model.setUnpDowntimeDaysDewax(Devicelistlist.get(j).getDvalue());
                }
                else if (Devicelistlist.get(j).getFieldName().equals("非计划停工次数(次)"))
                {
                    model.setUnpDowntimenumberDewax(Devicelistlist.get(j).getDvalue());
                }

            }
            list.add(model);
        }

        return list;
    }
}
