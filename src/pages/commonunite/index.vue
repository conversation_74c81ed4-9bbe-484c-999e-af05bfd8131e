<script lang="ts" setup>
document.title="首页 - 中国石化炼油技术分析及远程诊断系统"
import { ref } from "vue";
const currentNav = ref<string[]>(["nav_1"]);
const urlIframe = ref("/yczd-auth-web/commonunite/list-show?nodeid=96");
const currentNavName = ref("污水汽提");

function gotoNav(url: string,key:string,title:string) {
  urlIframe.value =url;
  currentNav.value=[key];
  currentNavName.value = title;
}

</script>
<template>
  <a-layout class="page home">
    <a-layout-sider class="sider" collapsible>
      <h3 class="nav_title">辅助装置</h3>
      <a-menu v-model:selectedKeys="currentNav" mode="inline">
        <a-menu-item key="nav_1">
          <div @click.stop="gotoNav('/yczd-auth-web/commonunite/list-show?nodeid=96','nav_1','污水汽提')">污水汽提</div>
        </a-menu-item>
        <a-menu-item key="nav_2">
          <div @click.stop="gotoNav('/yczd-auth-web/commonunite/list-show?nodeid=97','nav_2','烷基化')">烷基化</div>
        </a-menu-item>
        <a-menu-item key="nav_3">
          <div @click.stop="gotoNav('/yczd-auth-web/commonunite/list-show?nodeid=98','nav_3','溶剂再生')">溶剂再生</div>
        </a-menu-item>
        <a-menu-item key="nav_4">
          <div @click.stop="gotoNav('/yczd-auth-web/commonunite/list-show?nodeid=99','nav_4','溶剂脱沥青')">溶剂脱沥青</div>
        </a-menu-item>
        <a-menu-item key="nav_5">
          <div @click.stop="gotoNav('/yczd-auth-web/commonunite/list-show?nodeid=100','nav_5','石蜡加氢')">石蜡加氢</div>
        </a-menu-item>
        <a-menu-item key="nav_6">
          <div @click.stop="gotoNav('/yczd-auth-web/commonunite/list-show?nodeid=101','nav_6','润滑油加氢')">润滑油加氢</div>
        </a-menu-item>
        <a-menu-item key="nav_7">
          <div @click.stop="gotoNav('/yczd-auth-web/commonunite/list-show?nodeid=9001522','nav_7','储运罐区')">储运罐区</div>
        </a-menu-item>
        <a-menu-item key="nav_8">
          <div @click.stop="gotoNav('/yczd-auth-web/commonunite/solvent-deasphalting','nav_8','溶剂脱沥青装置报表')">溶剂脱沥青装置报表</div>
        </a-menu-item>
        <a-menu-item key="nav_9">
          <div @click.stop="gotoNav('/yczd-auth-web/commonunite/sewage-stripping','nav_9','污水汽提装置报表')">污水汽提装置报表</div>
        </a-menu-item>
        <a-menu-item key="nav_10">
          <div @click.stop="gotoNav('/yczd-auth-web/commonunite/solvent-refining','nav_10','溶剂精制装置报表')">溶剂精制装置报表</div>
        </a-menu-item>
        <a-menu-item key="nav_11">
          <div @click.stop="gotoNav('/yczd-auth-web/commonunite/solvent-dewaxing','nav_11','溶剂脱蜡装置报表')">溶剂脱蜡装置报表</div>
        </a-menu-item>
      </a-menu>
    </a-layout-sider>
    <a-layout-content>
      <h1 class="title">{{ currentNavName }}</h1>
      <iframe class="bd" :src="urlIframe" />
    </a-layout-content>
  </a-layout>
</template>
<style lang="less" scoped>
.page.home {
  width: 100%;
  height: 100vh;
  background: #f7f7fc !important;

  >.sider {
    .title {
      color: #fff;
    }
  }

  .title,
  .nav_title {
    color: #fff;
    background-color: #001529;
    height: 60px;
    line-height: 60px;
    padding: 0;
    margin: 0;
  }
  .bd{
    padding: 10px;
    width: 100%;
    // height: 100%;
    border: none;
    height: calc(100% - 60px);
  }
}
</style>