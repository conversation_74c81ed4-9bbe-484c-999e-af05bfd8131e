/**
 * 网络巡检接口映射表
 * 后端十个装置接口独立
 * by menglj at 20250508
 */
export default {
    // 催化裂化
    cata: {
        /**
         * GET-专家点评查询
         */
        url_comment_list: "/yczd/remoteDiagnosis/getQuery",
        /**
         * GET-专家点评查询-相关信息
         * query
         * adviceId
         */
        url_comment_list_byid: "/yczd/remoteDiagnosis/getQueryById",
        /**
         * GET-论坛公告
         * query
         * sectionId
         */
        url_forum_announce: "/yczd/remoteDiagnosis/getForumAnnouncement",
        /**
         * POST-发表-话题
         * body
         * size,current,adviceId(帖子id),
         * sectionId(所属板块id),type,title,content,
         * sender,sendTime,replyer,replyTime,visible,
         * lastUpdateTime,reserved2(附件数目),reserved
         * replyNum,scanNum,step,padviceId,top(0不置顶,1置顶,2板块公告)
         * image,device,company,senderen,peplyeren,BBSType,inPutBBs,
         * titelLabel,order,timeRange
         */
        url_topic_save: "/yczd/remoteDiagnosis/saveNewTopic",
        /**
         * POST-回复话题
         */
        url_topic_replay: "/yczd/remoteDiagnosis/saveHFTopic",
        /**
         * GET-删除-话题
         * query
         * adviceId
         */
        url_topic_delete: "/yczd/remoteDiagnosis/DelNewTopic",
        /**
         * GET-置顶-话题
         * query
         * adviceId
         */
        url_topic_totop: "/yczd/remoteDiagnosis/isTop",
        /**
         * POST-修改-话题
         */
        url_topic_update: "/yczd/remoteDiagnosis/updateNewTopic",
        /**
         * GET-添加资料类型
         * query
         * typeName
         */
        url_doc_type_save: "/yczd/remoteDiagnosis/addDocumentType",
        /**
         * GET-获取资料类型
         */
        url_doc_type_list: "/yczd/remoteDiagnosis/getDocumentType",
        /**
         * POST-保存资料
         */
        url_doc_save: "/yczd/remoteDiagnosis/saveDocument",
        /**
         * GET-获取资料
         */
        url_doc_list: "/yczd/remoteDiagnosis/getDocument",
        /**
         * GET-查询资料详情
         * query
         * documentId
         */
        url_doc_info: "/yczd/remoteDiagnosis/getDocumentDetails",
        /**
         * GET-技术资料明细
         */
        url_doc_tech_info: "/yczd/remoteDiagnosis/getTechDocumentDetails",
        /**
         * GET-装置介绍
         */
        url_doc_device_info: "/yczd/remoteDiagnosis/getDeviceIntroduction",

        // GET-装置详情
        url_doc_device_detail: "/yczd/remoteDiagnosis/pageDeviceDesc",
        /**
         * GET-发件列表-短信息交流
         */
        url_email_send_list: "/yczd/remoteDiagnosis/getInForExchange",
        /**
         * GET-根据主题查询某人各个主题的未读消息、根据主题查询各个主题的未读消息
         * query
         * id,userId,subject
         */
        url_email_unread_count: "/yczd/remoteDiagnosis/getNoticeCountBySubject",
        /**
         * GET-删除-短信息交流
         * query
         * id
         */
        url_email_send_delete: "/yczd/remoteDiagnosis/getDelInForExchange",
        /**
         * POST-删除-短信息交流
         * query
         * id
         */
        url_email_send_save: "/yczd/remoteDiagnosis/SaveInForExchange",
        /**
         * GET-收件列表-短信息交流
         */
        url_email_receive_list: "/yczd/remoteDiagnosis/getReceiverList",
        /**
         * GET-收件列表-短信息交流
         * ids
         */
        url_email_users_by_ids: "/yczd/remoteDiagnosis/getUsersByIds",
        /**
         * GET-获取主题
         */
        url_theme_list: "/yczd/remoteDiagnosis/getTheme",
        /**
         * GET-时间范围
         */
        url_time_range: "/yczd/remoteDiagnosis/getTimeRange",
        /**
         * GET-获取装置
         */
        url_device_list: "/yczd/remoteDiagnosis/getDevice",
        /**
         * GET-获取企业
         */
        url_enterprise_list: "/yczd/remoteDiagnosis/getEnterprise",
        /**
         * GET-获取内部资料
         */
        url_file_info: "/yczd/remoteDiagnosis/getFileInfoVo",
        /**
         * GET-新增内部资料
         * query
         * fileIndex
         */
        url_file_save: "/yczd/remoteDiagnosis/saveFileInfo",
        /**
         * GET-获取内部资料类型
         */
        url_file_type_list: "/yczd/remoteDiagnosis/getFileInfoType",
        /**
         * GET-获取内部资料类型-表
         */
        url_file_type_table: "/yczd/remoteDiagnosis/getOutSideFileType",
        /**
         * GET-获取内部资料-表
         */
        url_file_table: "/yczd/remoteDiagnosis/getOutSideFileInfo",
        /**
         * GET-保存内部资料-表
         */
        url_file_table_save: "/yczd/remoteDiagnosis/saveOutSideFileInfo",
        /**
         * GET-删除内部资料-表
         */
        url_file_table_delete: "/yczd/remoteDiagnosis/delOutSideFileInfo",
        /**
         * POST-删除内部资料
         * body
         * fileName,fileType,conferenceName,conferenceDate,
         * filePath,uploadUser,conferenceBeginDate,conferenceEndDate
         */
        url_file_delete: "/yczd/remoteDiagnosis/delFileInfo",
        /**
         * GET-会议表-在线交流
         */
        url_chat_room_list: "/yczd/remoteDiagnosis/getChatroom",
        /**
         * POST-保存会议表-在线交流
         */
        url_chat_room_save: "/yczd/remoteDiagnosis/saveChatroom",
        /**
         * GET-获取会议交流记录表-在线交流
         */
        url_chat_room_records: "/yczd/remoteDiagnosis/getMeetingExchange",
        /**
         * POST-保存会议交流记录表-在线交流
         */
        url_chat_room_record_save: "/yczd/remoteDiagnosis/saveMeetingExchange",
        /**
         * GET-退出会议表-在线交流
         */
        url_chat_room_exit: "/yczd/remoteDiagnosis/exitChatroom",
        /**
         * POST-附件上传
         */
        url_upload: "/yczd/remoteDiagnosis/file",
        /**
         * GET-获取附件
         * query
         * fileName
         */
        url_file_detail: "/yczd/remoteDiagnosis/getFileSteam",

        /**
         * GET-查询股份日报
         */
        // url_share_daily_info:"/yczd/remoteDiagnosis/getGFrequestmain",
        url_share_daily_info: "/WordTempelet/股份公司技术日报.docx",

        /**
         * GET-查询企业日报
         */
        // url_company_daily_info:"/yczd/remoteDiagnosis/getQYrequestmain",
        url_company_daily_info: "/WordTempelet/安庆企业技术日报.docx",
    },
    // 焦化
    coke: {
        /**
         * GET-专家点评查询
         */
        url_comment_list: "/yczd/coke/remoteDiagnosis/getQuery",
        /**
         * GET-专家点评查询-相关信息
         * query
         * adviceId
         */
        url_comment_list_byid: "/yczd/coke/remoteDiagnosis/getQueryById",
        /**
         * GET-论坛公告
         * query
         * sectionId
         */
        url_forum_announce: "/yczd/coke/remoteDiagnosis/getForumAnnouncement",
        /**
         * POST-发表-话题
         * body
         * size,current,adviceId(帖子id),
         * sectionId(所属板块id),type,title,content,
         * sender,sendTime,replyer,replyTime,visible,
         * lastUpdateTime,reserved2(附件数目),reserved
         * replyNum,scanNum,step,padviceId,top(0不置顶,1置顶,2板块公告)
         * image,device,company,senderen,peplyeren,BBSType,inPutBBs,
         * titelLabel,order,timeRange
         */
        url_topic_save: "/yczd/coke/remoteDiagnosis/saveNewTopic",
        /**
         * POST-回复话题
         */
        url_topic_replay: "/yczd/coke/remoteDiagnosis/saveHFTopic",
        /**
         * GET-删除-话题
         * query
         * adviceId
         */
        url_topic_delete: "/yczd/coke/remoteDiagnosis/DelNewTopic",
        /**
         * GET-置顶-话题
         * query
         * adviceId
         */
        url_topic_totop: "/yczd/coke/remoteDiagnosis/isTop",
        /**
         * POST-修改-话题
         */
        url_topic_update: "/yczd/coke/remoteDiagnosis/updateNewTopic",
        /**
         * GET-添加资料类型
         * query
         * typeName
         */
        url_doc_type_save: "/yczd/coke/remoteDiagnosis/addDocumentType",
        /**
         * GET-获取资料类型
         */
        url_doc_type_list: "/yczd/coke/remoteDiagnosis/getDocumentType",
        /**
         * POST-保存资料
         */
        url_doc_save: "/yczd/coke/remoteDiagnosis/saveDocument",
        /**
         * GET-获取资料
         */
        url_doc_list: "/yczd/coke/remoteDiagnosis/getDocument",
        /**
         * GET-查询资料详情
         * query
         * documentId
         */
        url_doc_info: "/yczd/coke/remoteDiagnosis/getDocumentDetails",
        /**
         * GET-技术资料明细
         */
        url_doc_tech_info: "/yczd/coke/remoteDiagnosis/getTechDocumentDetails",
        /**
         * GET-装置介绍
         */
        url_doc_device_info: "/yczd/coke/remoteDiagnosis/getDeviceIntroduction",
        // GET-装置详情
        url_doc_device_detail: "/yczd/coke/remoteDiagnosis/pageDeviceDesc",
        /**
         * GET-发件列表-短信息交流
         */
        url_email_send_list: "/yczd/coke/remoteDiagnosis/getInForExchange",
        /**
         * GET-根据主题查询某人各个主题的未读消息、根据主题查询各个主题的未读消息
         * query
         * id,userId,subject
         */
        url_email_unread_count: "/yczd/coke/remoteDiagnosis/getNoticeCountBySubject",
        /**
         * GET-删除-短信息交流
         * query
         * id
         */
        url_email_send_delete: "/yczd/coke/remoteDiagnosis/getDelInForExchange",
        /**
         * POST-删除-短信息交流
         * query
         * id
         */
        url_email_send_save: "/yczd/coke/remoteDiagnosis/SaveInForExchange",
        /**
         * GET-收件列表-短信息交流
         */
        url_email_receive_list: "/yczd/coke/remoteDiagnosis/getReceiverList",
        /**
         * GET-收件列表-短信息交流
         * ids
         */
        url_email_users_by_ids: "/yczd/coke/remoteDiagnosis/getUsersByIds",
        /**
         * GET-获取主题
         */
        url_theme_list: "/yczd/coke/remoteDiagnosis/getTheme",
        /**
         * GET-时间范围
         */
        url_time_range: "/yczd/coke/remoteDiagnosis/getTimeRange",
        /**
         * GET-获取装置
         */
        url_device_list: "/yczd/coke/remoteDiagnosis/getDevice",
        /**
         * GET-获取企业
         */
        url_enterprise_list: "/yczd/coke/remoteDiagnosis/getEnterprise",
        /**
         * GET-获取内部资料
         */
        url_file_info: "/yczd/coke/remoteDiagnosis/getFileInfoVo",
        /**
         * GET-新增内部资料
         * query
         * fileIndex
         */
        url_file_save: "/yczd/coke/remoteDiagnosis/saveFileInfo",
        /**
         * GET-获取内部资料类型
         */
        url_file_type_list: "/yczd/coke/remoteDiagnosis/getFileInfoType",
        /**
         * GET-获取内部资料类型-表
         */
        url_file_type_table: "/yczd/coke/remoteDiagnosis/getOutSideFileType",
        /**
         * GET-获取内部资料-表
         */
        url_file_table: "/yczd/coke/remoteDiagnosis/getOutSideFileInfo",
        /**
         * GET-保存内部资料-表
         */
        url_file_table_save: "/yczd/coke/remoteDiagnosis/saveOutSideFileInfo",
        /**
         * GET-删除内部资料-表
         */
        url_file_table_delete: "/yczd/coke/remoteDiagnosis/delOutSideFileInfo",
        /**
         * POST-删除内部资料
         * body
         * fileName,fileType,conferenceName,conferenceDate,
         * filePath,uploadUser,conferenceBeginDate,conferenceEndDate
         */
        url_file_delete: "/yczd/coke/remoteDiagnosis/delFileInfo",
        /**
         * GET-会议表-在线交流
         */
        url_chat_room_list: "/yczd/coke/remoteDiagnosis/getChatroom",
        /**
         * POST-保存会议表-在线交流
         */
        url_chat_room_save: "/yczd/coke/remoteDiagnosis/saveChatroom",
        /**
         * GET-获取会议交流记录表-在线交流
         */
        url_chat_room_records: "/yczd/coke/remoteDiagnosis/getMeetingExchange",
        /**
         * POST-保存会议交流记录表-在线交流
         */
        url_chat_room_record_save: "/yczd/coke/remoteDiagnosis/saveMeetingExchange",
        /**
         * GET-退出会议表-在线交流
         */
        url_chat_room_exit: "/yczd/coke/remoteDiagnosis/exitChatroom",
        /**
         * POST-附件上传
         */
        url_upload: "/yczd/coke/remoteDiagnosis/file",
        /**
         * GET-获取附件
         * query
         * fileName
         */
        url_file_detail: "/yczd/coke/remoteDiagnosis/getFileSteam",

        /**
         * GET-查询股份日报
         */
        url_share_daily_info: "/yczd/coke/remoteDiagnosis/getGFrequestmain",
        /**
         * GET-查询企业日报
         */
        url_company_daily_info: "/yczd/coke/remoteDiagnosis/getQYrequestmain",
    },
    // 气分
    gfu: {
        /**
         * GET-专家点评查询
         */
        url_comment_list: "/yczd/gfu/remoteDiagnosis/getQuery",
        /**
         * GET-专家点评查询-相关信息
         * query
         * adviceId
         */
        url_comment_list_byid: "/yczd/gfu/remoteDiagnosis/getQueryById",
        /**
         * GET-论坛公告
         * query
         * sectionId
         */
        url_forum_announce: "/yczd/gfu/remoteDiagnosis/getForumAnnouncement",
        /**
         * POST-发表-话题
         * body
         * size,current,adviceId(帖子id),
         * sectionId(所属板块id),type,title,content,
         * sender,sendTime,replyer,replyTime,visible,
         * lastUpdateTime,reserved2(附件数目),reserved
         * replyNum,scanNum,step,padviceId,top(0不置顶,1置顶,2板块公告)
         * image,device,company,senderen,peplyeren,BBSType,inPutBBs,
         * titelLabel,order,timeRange
         */
        url_topic_save: "/yczd/gfu/remoteDiagnosis/saveNewTopic",
        /**
         * POST-回复话题
         */
        url_topic_replay: "/yczd/gfu/remoteDiagnosis/saveHFTopic",
        /**
         * GET-删除-话题
         * query
         * adviceId
         */
        url_topic_delete: "/yczd/gfu/remoteDiagnosis/DelNewTopic",
        /**
         * GET-置顶-话题
         * query
         * adviceId
         */
        url_topic_totop: "/yczd/gfu/remoteDiagnosis/isTop",
        /**
         * POST-修改-话题
         */
        url_topic_update: "/yczd/gfu/remoteDiagnosis/updateNewTopic",
        /**
         * GET-添加资料类型
         * query
         * typeName
         */
        url_doc_type_save: "/yczd/gfu/remoteDiagnosis/addDocumentType",
        /**
         * GET-获取资料类型
         */
        url_doc_type_list: "/yczd/gfu/remoteDiagnosis/getDocumentType",
        /**
         * POST-保存资料
         */
        url_doc_save: "/yczd/gfu/remoteDiagnosis/saveDocument",
        /**
         * GET-获取资料
         */
        url_doc_list: "/yczd/gfu/remoteDiagnosis/getDocument",
        /**
         * GET-查询资料详情
         * query
         * documentId
         */
        url_doc_info: "/yczd/gfu/remoteDiagnosis/getDocumentDetails",
        /**
         * GET-技术资料明细
         */
        url_doc_tech_info: "/yczd/gfu/remoteDiagnosis/getTechDocumentDetails",
        /**
         * GET-装置介绍
         */
        url_doc_device_info: "/yczd/gfu/remoteDiagnosis/getDeviceIntroduction",
        // GET-装置详情
        url_doc_device_detail: "/yczd/gfu/remoteDiagnosis/pageDeviceDesc",
        /**
         * GET-发件列表-短信息交流
         */
        url_email_send_list: "/yczd/gfu/remoteDiagnosis/getInForExchange",
        /**
         * GET-根据主题查询某人各个主题的未读消息、根据主题查询各个主题的未读消息
         * query
         * id,userId,subject
         */
        url_email_unread_count: "/yczd/gfu/remoteDiagnosis/getNoticeCountBySubject",
        /**
         * GET-删除-短信息交流
         * query
         * id
         */
        url_email_send_delete: "/yczd/gfu/remoteDiagnosis/getDelInForExchange",
        /**
         * POST-删除-短信息交流
         * query
         * id
         */
        url_email_send_save: "/yczd/gfu/remoteDiagnosis/SaveInForExchange",
        /**
         * GET-收件列表-短信息交流
         */
        url_email_receive_list: "/yczd/gfu/remoteDiagnosis/getReceiverList",
        /**
         * GET-收件列表-短信息交流
         * ids
         */
        url_email_users_by_ids: "/yczd/gfu/remoteDiagnosis/getUsersByIds",
        /**
         * GET-获取主题
         */
        url_theme_list: "/yczd/gfu/remoteDiagnosis/getTheme",
        /**
         * GET-时间范围
         */
        url_time_range: "/yczd/gfu/remoteDiagnosis/getTimeRange",
        /**
         * GET-获取装置
         */
        url_device_list: "/yczd/gfu/remoteDiagnosis/getDevice",
        /**
         * GET-获取企业
         */
        url_enterprise_list: "/yczd/gfu/remoteDiagnosis/getEnterprise",
        /**
         * GET-获取内部资料
         */
        url_file_info: "/yczd/gfu/remoteDiagnosis/getFileInfoVo",
        /**
         * GET-新增内部资料
         * query
         * fileIndex
         */
        url_file_save: "/yczd/gfu/remoteDiagnosis/saveFileInfo",
        /**
         * GET-获取内部资料类型
         */
        url_file_type_list: "/yczd/gfu/remoteDiagnosis/getFileInfoType",
        /**
         * GET-获取内部资料类型-表
         */
        url_file_type_table: "/yczd/gfu/remoteDiagnosis/getOutSideFileType",
        /**
         * GET-获取内部资料-表
         */
        url_file_table: "/yczd/gfu/remoteDiagnosis/getOutSideFileInfo",
        /**
         * GET-保存内部资料-表
         */
        url_file_table_save: "/yczd/gfu/remoteDiagnosis/saveOutSideFileInfo",
        /**
         * GET-删除内部资料-表
         */
        url_file_table_delete: "/yczd/gfu/remoteDiagnosis/delOutSideFileInfo",
        /**
         * POST-删除内部资料
         * body
         * fileName,fileType,conferenceName,conferenceDate,
         * filePath,uploadUser,conferenceBeginDate,conferenceEndDate
         */
        url_file_delete: "/yczd/gfu/remoteDiagnosis/delFileInfo",
        /**
         * GET-会议表-在线交流
         */
        url_chat_room_list: "/yczd/gfu/remoteDiagnosis/getChatroom",
        /**
         * POST-保存会议表-在线交流
         */
        url_chat_room_save: "/yczd/gfu/remoteDiagnosis/saveChatroom",
        /**
         * GET-获取会议交流记录表-在线交流
         */
        url_chat_room_records: "/yczd/gfu/remoteDiagnosis/getMeetingExchange",
        /**
         * POST-保存会议交流记录表-在线交流
         */
        url_chat_room_record_save: "/yczd/gfu/remoteDiagnosis/saveMeetingExchange",
        /**
         * GET-退出会议表-在线交流
         */
        url_chat_room_exit: "/yczd/gfu/remoteDiagnosis/exitChatroom",
        /**
         * POST-附件上传
         */
        url_upload: "/yczd/gfu/remoteDiagnosis/file",
        /**
         * GET-获取附件
         * query
         * fileName
         */
        url_file_detail: "/yczd/gfu/remoteDiagnosis/getFileSteam",

        /**
         * GET-查询股份日报
         */
        url_share_daily_info: "/yczd/gfu/remoteDiagnosis/getGFrequestmain",
        /**
         * GET-查询企业日报
         */
        url_company_daily_info: "/yczd/gfu/remoteDiagnosis/getQYrequestmain",
    },
    // 加氢
    hydro: {
        /**
         * GET-专家点评查询
         */
        url_comment_list: "/yczd/hgu/hydro/remoteDiagnosis/getQuery",
        /**
         * GET-专家点评查询-相关信息
         * query
         * adviceId
         */
        url_comment_list_byid: "/yczd/hydro/remoteDiagnosis/getQueryById",
        /**
         * GET-论坛公告
         * query
         * sectionId
         */
        url_forum_announce: "/yczd/hydro/remoteDiagnosis/getForumAnnouncement",
        /**
         * POST-发表-话题
         * body
         * size,current,adviceId(帖子id),
         * sectionId(所属板块id),type,title,content,
         * sender,sendTime,replyer,replyTime,visible,
         * lastUpdateTime,reserved2(附件数目),reserved
         * replyNum,scanNum,step,padviceId,top(0不置顶,1置顶,2板块公告)
         * image,device,company,senderen,peplyeren,BBSType,inPutBBs,
         * titelLabel,order,timeRange
         */
        url_topic_save: "/yczd/hydro/remoteDiagnosis/saveNewTopic",
        /**
         * POST-回复话题
         */
        url_topic_replay: "/yczd/hydro/remoteDiagnosis/saveHFTopic",
        /**
         * GET-删除-话题
         * query
         * adviceId
         */
        url_topic_delete: "/yczd/hydro/remoteDiagnosis/DelNewTopic",
        /**
         * GET-置顶-话题
         * query
         * adviceId
         */
        url_topic_totop: "/yczd/hydro/remoteDiagnosis/isTop",
        /**
         * POST-修改-话题
         */
        url_topic_update: "/yczd/hydro/remoteDiagnosis/updateNewTopic",
        /**
         * GET-添加资料类型
         * query
         * typeName
         */
        url_doc_type_save: "/yczd/hydro/remoteDiagnosis/addDocumentType",
        /**
         * GET-获取资料类型
         */
        url_doc_type_list: "/yczd/hydro/remoteDiagnosis/getDocumentType",
        /**
         * POST-保存资料
         */
        url_doc_save: "/yczd/hydro/remoteDiagnosis/saveDocument",
        /**
         * GET-获取资料
         */
        url_doc_list: "/yczd/hydro/remoteDiagnosis/getDocument",
        /**
         * GET-查询资料详情
         * query
         * documentId
         */
        url_doc_info: "/yczd/hydro/remoteDiagnosis/getDocumentDetails",
        /**
         * GET-技术资料明细
         */
        url_doc_tech_info: "/yczd/hydro/remoteDiagnosis/getTechDocumentDetails",
        /**
         * GET-装置介绍
         */
        url_doc_device_info: "/yczd/hydro/remoteDiagnosis/getDeviceIntroduction",
        // GET-装置详情
        url_doc_device_detail: "/yczd/hydro/remoteDiagnosis/pageDeviceDesc",
        /**
         * GET-发件列表-短信息交流
         */
        url_email_send_list: "/yczd/hydro/remoteDiagnosis/getInForExchange",
        /**
         * GET-根据主题查询某人各个主题的未读消息、根据主题查询各个主题的未读消息
         * query
         * id,userId,subject
         */
        url_email_unread_count: "/yczd/hydro/remoteDiagnosis/getNoticeCountBySubject",
        /**
         * GET-删除-短信息交流
         * query
         * id
         */
        url_email_send_delete: "/yczd/hydro/remoteDiagnosis/getDelInForExchange",
        /**
         * POST-删除-短信息交流
         * query
         * id
         */
        url_email_send_save: "/yczd/hydro/remoteDiagnosis/SaveInForExchange",
        /**
         * GET-收件列表-短信息交流
         */
        url_email_receive_list: "/yczd/hydro/remoteDiagnosis/getReceiverList",
        /**
         * GET-收件列表-短信息交流
         * ids
         */
        url_email_users_by_ids: "/yczd/hydro/remoteDiagnosis/getUsersByIds",
        /**
         * GET-获取主题
         */
        url_theme_list: "/yczd/hydro/remoteDiagnosis/getTheme",
        /**
         * GET-时间范围
         */
        url_time_range: "/yczd/hydro/remoteDiagnosis/getTimeRange",
        /**
         * GET-获取装置
         */
        url_device_list: "/yczd/hydro/remoteDiagnosis/getDevice",
        /**
         * GET-获取企业
         */
        url_enterprise_list: "/yczd/hydro/remoteDiagnosis/getEnterprise",
        /**
         * GET-获取内部资料
         */
        url_file_info: "/yczd/hydro/remoteDiagnosis/getFileInfoVo",
        /**
         * GET-新增内部资料
         * query
         * fileIndex
         */
        url_file_save: "/yczd/hydro/remoteDiagnosis/saveFileInfo",
        /**
         * GET-获取内部资料类型
         */
        url_file_type_list: "/yczd/hydro/remoteDiagnosis/getFileInfoType",
        /**
         * GET-获取内部资料类型-表
         */
        url_file_type_table: "/yczd/hydro/remoteDiagnosis/getOutSideFileType",
        /**
         * GET-获取内部资料-表
         */
        url_file_table: "/yczd/hydro/remoteDiagnosis/getOutSideFileInfo",
        /**
         * GET-保存内部资料-表
         */
        url_file_table_save: "/yczd/hydro/remoteDiagnosis/saveOutSideFileInfo",
        /**
         * GET-删除内部资料-表
         */
        url_file_table_delete: "/yczd/hydro/remoteDiagnosis/delOutSideFileInfo",
        /**
         * POST-删除内部资料
         * body
         * fileName,fileType,conferenceName,conferenceDate,
         * filePath,uploadUser,conferenceBeginDate,conferenceEndDate
         */
        url_file_delete: "/yczd/hydro/remoteDiagnosis/delFileInfo",
        /**
         * GET-会议表-在线交流
         */
        url_chat_room_list: "/yczd/hydro/remoteDiagnosis/getChatroom",
        /**
         * POST-保存会议表-在线交流
         */
        url_chat_room_save: "/yczd/hydro/remoteDiagnosis/saveChatroom",
        /**
         * GET-获取会议交流记录表-在线交流
         */
        url_chat_room_records: "/yczd/hydro/remoteDiagnosis/getMeetingExchange",
        /**
         * POST-保存会议交流记录表-在线交流
         */
        url_chat_room_record_save: "/yczd/hydro/remoteDiagnosis/saveMeetingExchange",
        /**
         * GET-退出会议表-在线交流
         */
        url_chat_room_exit: "/yczd/hydro/remoteDiagnosis/exitChatroom",
        /**
         * POST-附件上传
         */
        url_upload: "/yczd/hydro/remoteDiagnosis/file",
        /**
         * GET-获取附件
         * query
         * fileName
         */
        url_file_detail: "/yczd/hydro/remoteDiagnosis/getFileSteam",

        /**
         * GET-查询股份日报
         */
        url_share_daily_info: "/yczd/hydro/remoteDiagnosis/getGFrequestmain",
        /**
         * GET-查询企业日报
         */
        url_company_daily_info: "/yczd/hydro/remoteDiagnosis/getQYrequestmain",
    },
    // 制氢
    hgu: {
        /**
         * GET-专家点评查询
         */
        url_comment_list: "/yczd/hgu/remoteDiagnosis/getQuery",
        /**
         * GET-专家点评查询-相关信息
         * query
         * adviceId
         */
        url_comment_list_byid: "/yczd/hgu/remoteDiagnosis/getQueryById",
        /**
         * GET-论坛公告
         * query
         * sectionId
         */
        url_forum_announce: "/yczd/hgu/remoteDiagnosis/getForumAnnouncement",
        /**
         * POST-发表-话题
         * body
         * size,current,adviceId(帖子id),
         * sectionId(所属板块id),type,title,content,
         * sender,sendTime,replyer,replyTime,visible,
         * lastUpdateTime,reserved2(附件数目),reserved
         * replyNum,scanNum,step,padviceId,top(0不置顶,1置顶,2板块公告)
         * image,device,company,senderen,peplyeren,BBSType,inPutBBs,
         * titelLabel,order,timeRange
         */
        url_topic_save: "/yczd/hgu/remoteDiagnosis/saveNewTopic",
        /**
         * POST-回复话题
         */
        url_topic_replay: "/yczd/hgu/remoteDiagnosis/saveHFTopic",
        /**
         * GET-删除-话题
         * query
         * adviceId
         */
        url_topic_delete: "/yczd/hgu/remoteDiagnosis/DelNewTopic",
        /**
         * GET-置顶-话题
         * query
         * adviceId
         */
        url_topic_totop: "/yczd/hgu/remoteDiagnosis/isTop",
        /**
         * POST-修改-话题
         */
        url_topic_update: "/yczd/hgu/remoteDiagnosis/updateNewTopic",
        /**
         * GET-添加资料类型
         * query
         * typeName
         */
        url_doc_type_save: "/yczd/hgu/remoteDiagnosis/addDocumentType",
        /**
         * GET-获取资料类型
         */
        url_doc_type_list: "/yczd/hgu/remoteDiagnosis/getDocumentType",
        /**
         * POST-保存资料
         */
        url_doc_save: "/yczd/hgu/remoteDiagnosis/saveDocument",
        /**
         * GET-获取资料
         */
        url_doc_list: "/yczd/hgu/remoteDiagnosis/getDocument",
        /**
         * GET-查询资料详情
         * query
         * documentId
         */
        url_doc_info: "/yczd/hgu/remoteDiagnosis/getDocumentDetails",
        /**
         * GET-技术资料明细
         */
        url_doc_tech_info: "/yczd/hgu/remoteDiagnosis/getTechDocumentDetails",
        /**
         * GET-装置介绍
         */
        url_doc_device_info: "/yczd/hgu/remoteDiagnosis/getDeviceIntroduction",
        // GET-装置详情
        url_doc_device_detail: "/yczd/hgu/remoteDiagnosis/pageDeviceDesc",
        /**
         * GET-发件列表-短信息交流
         */
        url_email_send_list: "/yczd/hgu/remoteDiagnosis/getInForExchange",
        /**
         * GET-根据主题查询某人各个主题的未读消息、根据主题查询各个主题的未读消息
         * query
         * id,userId,subject
         */
        url_email_unread_count: "/yczd/hgu/remoteDiagnosis/getNoticeCountBySubject",
        /**
         * GET-删除-短信息交流
         * query
         * id
         */
        url_email_send_delete: "/yczd/hgu/remoteDiagnosis/getDelInForExchange",
        /**
         * POST-删除-短信息交流
         * query
         * id
         */
        url_email_send_save: "/yczd/hgu/remoteDiagnosis/SaveInForExchange",
        /**
         * GET-收件列表-短信息交流
         */
        url_email_receive_list: "/yczd/hgu/remoteDiagnosis/getReceiverList",
        /**
         * GET-收件列表-短信息交流
         * ids
         */
        url_email_users_by_ids: "/yczd/hgu/remoteDiagnosis/getUsersByIds",
        /**
         * GET-获取主题
         */
        url_theme_list: "/yczd/hgu/remoteDiagnosis/getTheme",
        /**
         * GET-时间范围
         */
        url_time_range: "/yczd/hgu/remoteDiagnosis/getTimeRange",
        /**
         * GET-获取装置
         */
        url_device_list: "/yczd/hgu/remoteDiagnosis/getDevice",
        /**
         * GET-获取企业
         */
        url_enterprise_list: "/yczd/hgu/remoteDiagnosis/getEnterprise",
        /**
         * GET-获取内部资料
         */
        url_file_info: "/yczd/hgu/remoteDiagnosis/getFileInfoVo",
        /**
         * GET-新增内部资料
         * query
         * fileIndex
         */
        url_file_save: "/yczd/hgu/remoteDiagnosis/saveFileInfo",
        /**
         * GET-获取内部资料类型
         */
        url_file_type_list: "/yczd/hgu/remoteDiagnosis/getFileInfoType",
        /**
         * GET-获取内部资料类型-表
         */
        url_file_type_table: "/yczd/hgu/remoteDiagnosis/getOutSideFileType",
        /**
         * GET-获取内部资料-表
         */
        url_file_table: "/yczd/hgu/remoteDiagnosis/getOutSideFileInfo",
        /**
         * GET-保存内部资料-表
         */
        url_file_table_save: "/yczd/hgu/remoteDiagnosis/saveOutSideFileInfo",
        /**
         * GET-删除内部资料-表
         */
        url_file_table_delete: "/yczd/hgu/remoteDiagnosis/delOutSideFileInfo",
        /**
         * POST-删除内部资料
         * body
         * fileName,fileType,conferenceName,conferenceDate,
         * filePath,uploadUser,conferenceBeginDate,conferenceEndDate
         */
        url_file_delete: "/yczd/hgu/remoteDiagnosis/delFileInfo",
        /**
         * GET-会议表-在线交流
         */
        url_chat_room_list: "/yczd/hgu/remoteDiagnosis/getChatroom",
        /**
         * POST-保存会议表-在线交流
         */
        url_chat_room_save: "/yczd/hgu/remoteDiagnosis/saveChatroom",
        /**
         * GET-获取会议交流记录表-在线交流
         */
        url_chat_room_records: "/yczd/hgu/remoteDiagnosis/getMeetingExchange",
        /**
         * POST-保存会议交流记录表-在线交流
         */
        url_chat_room_record_save: "/yczd/hgu/remoteDiagnosis/saveMeetingExchange",
        /**
         * GET-退出会议表-在线交流
         */
        url_chat_room_exit: "/yczd/hgu/remoteDiagnosis/exitChatroom",
        /**
         * POST-附件上传
         */
        url_upload: "/yczd/hgu/remoteDiagnosis/file",
        /**
         * GET-获取附件
         * query
         * fileName
         */
        url_file_detail: "/yczd/hgu/remoteDiagnosis/getFileSteam",

        /**
         * GET-查询股份日报
         */
        url_share_daily_info: "/yczd/hgu/remoteDiagnosis/getGFrequestmain",
        /**
         * GET-查询企业日报
         */
        url_company_daily_info: "/yczd/hgu/remoteDiagnosis/getQYrequestmain",
    },
    // MTBE
    mtbe: {
        /**
         * GET-专家点评查询
         */
        url_comment_list: "/yczd/mtbe/remoteDiagnosis/getQuery",
        /**
         * GET-专家点评查询-相关信息
         * query
         * adviceId
         */
        url_comment_list_byid: "/yczd/mtbe/remoteDiagnosis/getQueryById",
        /**
         * GET-论坛公告
         * query
         * sectionId
         */
        url_forum_announce: "/yczd/mtbe/remoteDiagnosis/getForumAnnouncement",
        /**
         * POST-发表-话题
         * body
         * size,current,adviceId(帖子id),
         * sectionId(所属板块id),type,title,content,
         * sender,sendTime,replyer,replyTime,visible,
         * lastUpdateTime,reserved2(附件数目),reserved
         * replyNum,scanNum,step,padviceId,top(0不置顶,1置顶,2板块公告)
         * image,device,company,senderen,peplyeren,BBSType,inPutBBs,
         * titelLabel,order,timeRange
         */
        url_topic_save: "/yczd/mtbe/remoteDiagnosis/saveNewTopic",
        /**
         * POST-回复话题
         */
        url_topic_replay: "/yczd/mtbe/remoteDiagnosis/saveHFTopic",
        /**
         * GET-删除-话题
         * query
         * adviceId
         */
        url_topic_delete: "/yczd/mtbe/remoteDiagnosis/DelNewTopic",
        /**
         * GET-置顶-话题
         * query
         * adviceId
         */
        url_topic_totop: "/yczd/mtbe/remoteDiagnosis/isTop",
        /**
         * POST-修改-话题
         */
        url_topic_update: "/yczd/mtbe/remoteDiagnosis/updateNewTopic",
        /**
         * GET-添加资料类型
         * query
         * typeName
         */
        url_doc_type_save: "/yczd/mtbe/remoteDiagnosis/addDocumentType",
        /**
         * GET-获取资料类型
         */
        url_doc_type_list: "/yczd/mtbe/remoteDiagnosis/getDocumentType",
        /**
         * POST-保存资料
         */
        url_doc_save: "/yczd/mtbe/remoteDiagnosis/saveDocument",
        /**
         * GET-获取资料
         */
        url_doc_list: "/yczd/mtbe/remoteDiagnosis/getDocument",
        /**
         * GET-查询资料详情
         * query
         * documentId
         */
        url_doc_info: "/yczd/mtbe/remoteDiagnosis/getDocumentDetails",
        /**
         * GET-技术资料明细
         */
        url_doc_tech_info: "/yczd/mtbe/remoteDiagnosis/getTechDocumentDetails",
        /**
         * GET-装置介绍
         */
        url_doc_device_info: "/yczd/mtbe/remoteDiagnosis/getDeviceIntroduction",
        // GET-装置详情
        url_doc_device_detail: "/yczd/mtbe/remoteDiagnosis/pageDeviceDesc",
        /**
         * GET-发件列表-短信息交流
         */
        url_email_send_list: "/yczd/mtbe/remoteDiagnosis/getInForExchange",
        /**
         * GET-根据主题查询某人各个主题的未读消息、根据主题查询各个主题的未读消息
         * query
         * id,userId,subject
         */
        url_email_unread_count: "/yczd/mtbe/remoteDiagnosis/getNoticeCountBySubject",
        /**
         * GET-删除-短信息交流
         * query
         * id
         */
        url_email_send_delete: "/yczd/mtbe/remoteDiagnosis/getDelInForExchange",
        /**
         * POST-删除-短信息交流
         * query
         * id
         */
        url_email_send_save: "/yczd/mtbe/remoteDiagnosis/SaveInForExchange",
        /**
         * GET-收件列表-短信息交流
         */
        url_email_receive_list: "/yczd/mtbe/remoteDiagnosis/getReceiverList",
        /**
         * GET-收件列表-短信息交流
         * ids
         */
        url_email_users_by_ids: "/yczd/mtbe/remoteDiagnosis/getUsersByIds",
        /**
         * GET-获取主题
         */
        url_theme_list: "/yczd/mtbe/remoteDiagnosis/getTheme",
        /**
         * GET-时间范围
         */
        url_time_range: "/yczd/mtbe/remoteDiagnosis/getTimeRange",
        /**
         * GET-获取装置
         */
        url_device_list: "/yczd/mtbe/remoteDiagnosis/getDevice",
        /**
         * GET-获取企业
         */
        url_enterprise_list: "/yczd/mtbe/remoteDiagnosis/getEnterprise",
        /**
         * GET-获取内部资料
         */
        url_file_info: "/yczd/mtbe/remoteDiagnosis/getFileInfoVo",
        /**
         * GET-新增内部资料
         * query
         * fileIndex
         */
        url_file_save: "/yczd/mtbe/remoteDiagnosis/saveFileInfo",
        /**
         * GET-获取内部资料类型
         */
        url_file_type_list: "/yczd/mtbe/remoteDiagnosis/getFileInfoType",
        /**
         * GET-获取内部资料类型-表
         */
        url_file_type_table: "/yczd/mtbe/remoteDiagnosis/getOutSideFileType",
        /**
         * GET-获取内部资料-表
         */
        url_file_table: "/yczd/mtbe/remoteDiagnosis/getOutSideFileInfo",
        /**
         * GET-保存内部资料-表
         */
        url_file_table_save: "/yczd/mtbe/remoteDiagnosis/saveOutSideFileInfo",
        /**
         * GET-删除内部资料-表
         */
        url_file_table_delete: "/yczd/mtbe/remoteDiagnosis/delOutSideFileInfo",
        /**
         * POST-删除内部资料
         * body
         * fileName,fileType,conferenceName,conferenceDate,
         * filePath,uploadUser,conferenceBeginDate,conferenceEndDate
         */
        url_file_delete: "/yczd/mtbe/remoteDiagnosis/delFileInfo",
        /**
         * GET-会议表-在线交流
         */
        url_chat_room_list: "/yczd/mtbe/remoteDiagnosis/getChatroom",
        /**
         * POST-保存会议表-在线交流
         */
        url_chat_room_save: "/yczd/mtbe/remoteDiagnosis/saveChatroom",
        /**
         * GET-获取会议交流记录表-在线交流
         */
        url_chat_room_records: "/yczd/mtbe/remoteDiagnosis/getMeetingExchange",
        /**
         * POST-保存会议交流记录表-在线交流
         */
        url_chat_room_record_save: "/yczd/mtbe/remoteDiagnosis/saveMeetingExchange",
        /**
         * GET-退出会议表-在线交流
         */
        url_chat_room_exit: "/yczd/mtbe/remoteDiagnosis/exitChatroom",
        /**
         * POST-附件上传
         */
        url_upload: "/yczd/mtbe/remoteDiagnosis/file",
        /**
         * GET-获取附件
         * query
         * fileName
         */
        url_file_detail: "/yczd/mtbe/remoteDiagnosis/getFileSteam",

        /**
         * GET-查询股份日报
         */
        url_share_daily_info: "/yczd/mtbe/remoteDiagnosis/getGFrequestmain",
        /**
         * GET-查询企业日报
         */
        url_company_daily_info: "/yczd/mtbe/remoteDiagnosis/getQYrequestmain",
    },
    // 常减压
    press: {
        /**
         * GET-专家点评查询
         */
        url_comment_list: "/yczd/press/remoteDiagnosis/getQuery",
        /**
         * GET-专家点评查询-相关信息
         * query
         * adviceId
         */
        url_comment_list_byid: "/yczd/press/remoteDiagnosis/getQueryById",
        /**
         * GET-论坛公告
         * query
         * sectionId
         */
        url_forum_announce: "/yczd/press/remoteDiagnosis/getForumAnnouncement",
        /**
         * POST-发表-话题
         * body
         * size,current,adviceId(帖子id),
         * sectionId(所属板块id),type,title,content,
         * sender,sendTime,replyer,replyTime,visible,
         * lastUpdateTime,reserved2(附件数目),reserved
         * replyNum,scanNum,step,padviceId,top(0不置顶,1置顶,2板块公告)
         * image,device,company,senderen,peplyeren,BBSType,inPutBBs,
         * titelLabel,order,timeRange
         */
        url_topic_save: "/yczd/press/remoteDiagnosis/saveNewTopic",
        /**
         * POST-回复话题
         */
        url_topic_replay: "/yczd/press/remoteDiagnosis/saveHFTopic",
        /**
         * GET-删除-话题
         * query
         * adviceId
         */
        url_topic_delete: "/yczd/press/remoteDiagnosis/DelNewTopic",
        /**
         * GET-置顶-话题
         * query
         * adviceId
         */
        url_topic_totop: "/yczd/press/remoteDiagnosis/isTop",
        /**
         * POST-修改-话题
         */
        url_topic_update: "/yczd/press/remoteDiagnosis/updateNewTopic",
        /**
         * GET-添加资料类型
         * query
         * typeName
         */
        url_doc_type_save: "/yczd/press/remoteDiagnosis/addDocumentType",
        /**
         * GET-获取资料类型
         */
        url_doc_type_list: "/yczd/press/remoteDiagnosis/getDocumentType",
        /**
         * POST-保存资料
         */
        url_doc_save: "/yczd/press/remoteDiagnosis/saveDocument",
        /**
         * GET-获取资料
         */
        url_doc_list: "/yczd/press/remoteDiagnosis/getDocument",
        /**
         * GET-查询资料详情
         * query
         * documentId
         */
        url_doc_info: "/yczd/press/remoteDiagnosis/getDocumentDetails",
        /**
         * GET-技术资料明细
         */
        url_doc_tech_info: "/yczd/press/remoteDiagnosis/getTechDocumentDetails",
        /**
         * GET-装置介绍
         */
        url_doc_device_info: "/yczd/press/remoteDiagnosis/getDeviceIntroduction",
        // GET-装置详情
        url_doc_device_detail: "/yczd/press/remoteDiagnosis/pageDeviceDesc",
        /**
         * GET-发件列表-短信息交流
         */
        url_email_send_list: "/yczd/press/remoteDiagnosis/getInForExchange",
        /**
         * GET-根据主题查询某人各个主题的未读消息、根据主题查询各个主题的未读消息
         * query
         * id,userId,subject
         */
        url_email_unread_count: "/yczd/press/remoteDiagnosis/getNoticeCountBySubject",
        /**
         * GET-删除-短信息交流
         * query
         * id
         */
        url_email_send_delete: "/yczd/press/remoteDiagnosis/getDelInForExchange",
        /**
         * POST-删除-短信息交流
         * query
         * id
         */
        url_email_send_save: "/yczd/press/remoteDiagnosis/SaveInForExchange",
        /**
         * GET-收件列表-短信息交流
         */
        url_email_receive_list: "/yczd/press/remoteDiagnosis/getReceiverList",
        /**
         * GET-收件列表-短信息交流
         * ids
         */
        url_email_users_by_ids: "/yczd/press/remoteDiagnosis/getUsersByIds",
        /**
         * GET-获取主题
         */
        url_theme_list: "/yczd/press/remoteDiagnosis/getTheme",
        /**
         * GET-时间范围
         */
        url_time_range: "/yczd/press/remoteDiagnosis/getTimeRange",
        /**
         * GET-获取装置
         */
        url_device_list: "/yczd/press/remoteDiagnosis/getDevice",
        /**
         * GET-获取企业
         */
        url_enterprise_list: "/yczd/press/remoteDiagnosis/getEnterprise",
        /**
         * GET-获取内部资料
         */
        url_file_info: "/yczd/press/remoteDiagnosis/getFileInfoVo",
        /**
         * GET-新增内部资料
         * query
         * fileIndex
         */
        url_file_save: "/yczd/press/remoteDiagnosis/saveFileInfo",
        /**
         * GET-获取内部资料类型
         */
        url_file_type_list: "/yczd/press/remoteDiagnosis/getFileInfoType",
        /**
         * GET-获取内部资料类型-表
         */
        url_file_type_table: "/yczd/press/remoteDiagnosis/getOutSideFileType",
        /**
         * GET-获取内部资料-表
         */
        url_file_table: "/yczd/press/remoteDiagnosis/getOutSideFileInfo",
        /**
         * GET-保存内部资料-表
         */
        url_file_table_save: "/yczd/press/remoteDiagnosis/saveOutSideFileInfo",
        /**
         * GET-删除内部资料-表
         */
        url_file_table_delete: "/yczd/press/remoteDiagnosis/delOutSideFileInfo",
        /**
         * POST-删除内部资料
         * body
         * fileName,fileType,conferenceName,conferenceDate,
         * filePath,uploadUser,conferenceBeginDate,conferenceEndDate
         */
        url_file_delete: "/yczd/press/remoteDiagnosis/delFileInfo",
        /**
         * GET-会议表-在线交流
         */
        url_chat_room_list: "/yczd/press/remoteDiagnosis/getChatroom",
        /**
         * POST-保存会议表-在线交流
         */
        url_chat_room_save: "/yczd/press/remoteDiagnosis/saveChatroom",
        /**
         * GET-获取会议交流记录表-在线交流
         */
        url_chat_room_records: "/yczd/press/remoteDiagnosis/getMeetingExchange",
        /**
         * POST-保存会议交流记录表-在线交流
         */
        url_chat_room_record_save: "/yczd/press/remoteDiagnosis/saveMeetingExchange",
        /**
         * GET-退出会议表-在线交流
         */
        url_chat_room_exit: "/yczd/press/remoteDiagnosis/exitChatroom",
        /**
         * POST-附件上传
         */
        url_upload: "/yczd/press/remoteDiagnosis/file",
        /**
         * GET-获取附件
         * query
         * fileName
         */
        url_file_detail: "/yczd/press/remoteDiagnosis/getFileSteam",

        /**
         * GET-查询股份日报
         */
        url_share_daily_info: "/yczd/press/remoteDiagnosis/getGFrequestmain",
        /**
         * GET-查询企业日报
         */
        url_company_daily_info: "/yczd/press/remoteDiagnosis/getQYrequestmain",
    },
    // 重整
    regn: {
        /**
         * GET-专家点评查询
         */
        url_comment_list: "/yczd/regn/remoteDiagnosis/getQuery",
        /**
         * GET-专家点评查询-相关信息
         * query
         * adviceId
         */
        url_comment_list_byid: "/yczd/regn/remoteDiagnosis/getQueryById",
        /**
         * GET-论坛公告
         * query
         * sectionId
         */
        url_forum_announce: "/yczd/regn/remoteDiagnosis/getForumAnnouncement",
        /**
         * POST-发表-话题
         * body
         * size,current,adviceId(帖子id),
         * sectionId(所属板块id),type,title,content,
         * sender,sendTime,replyer,replyTime,visible,
         * lastUpdateTime,reserved2(附件数目),reserved
         * replyNum,scanNum,step,padviceId,top(0不置顶,1置顶,2板块公告)
         * image,device,company,senderen,peplyeren,BBSType,inPutBBs,
         * titelLabel,order,timeRange
         */
        url_topic_save: "/yczd/regn/remoteDiagnosis/saveNewTopic",
        /**
         * POST-回复话题
         */
        url_topic_replay: "/yczd/regn/remoteDiagnosis/saveHFTopic",
        /**
         * GET-删除-话题
         * query
         * adviceId
         */
        url_topic_delete: "/yczd/regn/remoteDiagnosis/DelNewTopic",
        /**
         * GET-置顶-话题
         * query
         * adviceId
         */
        url_topic_totop: "/yczd/regn/remoteDiagnosis/isTop",
        /**
         * POST-修改-话题
         */
        url_topic_update: "/yczd/regn/remoteDiagnosis/updateNewTopic",
        /**
         * GET-添加资料类型
         * query
         * typeName
         */
        url_doc_type_save: "/yczd/regn/remoteDiagnosis/addDocumentType",
        /**
         * GET-获取资料类型
         */
        url_doc_type_list: "/yczd/regn/remoteDiagnosis/getDocumentType",
        /**
         * POST-保存资料
         */
        url_doc_save: "/yczd/regn/remoteDiagnosis/saveDocument",
        /**
         * GET-获取资料
         */
        url_doc_list: "/yczd/regn/remoteDiagnosis/getDocument",
        /**
         * GET-查询资料详情
         * query
         * documentId
         */
        url_doc_info: "/yczd/regn/remoteDiagnosis/getDocumentDetails",
        /**
         * GET-技术资料明细
         */
        url_doc_tech_info: "/yczd/regn/remoteDiagnosis/getTechDocumentDetails",
        /**
         * GET-装置介绍
         */
        url_doc_device_info: "/yczd/regn/remoteDiagnosis/getDeviceIntroduction",
        // GET-装置详情
        url_doc_device_detail: "/yczd/regn/remoteDiagnosis/pageDeviceDesc",
        /**
         * GET-发件列表-短信息交流
         */
        url_email_send_list: "/yczd/regn/remoteDiagnosis/getInForExchange",
        /**
         * GET-根据主题查询某人各个主题的未读消息、根据主题查询各个主题的未读消息
         * query
         * id,userId,subject
         */
        url_email_unread_count: "/yczd/regn/remoteDiagnosis/getNoticeCountBySubject",
        /**
         * GET-删除-短信息交流
         * query
         * id
         */
        url_email_send_delete: "/yczd/regn/remoteDiagnosis/getDelInForExchange",
        /**
         * POST-删除-短信息交流
         * query
         * id
         */
        url_email_send_save: "/yczd/regn/remoteDiagnosis/SaveInForExchange",
        /**
         * GET-收件列表-短信息交流
         */
        url_email_receive_list: "/yczd/regn/remoteDiagnosis/getReceiverList",
        /**
         * GET-收件列表-短信息交流
         * ids
         */
        url_email_users_by_ids: "/yczd/regn/remoteDiagnosis/getUsersByIds",
        /**
         * GET-获取主题
         */
        url_theme_list: "/yczd/regn/remoteDiagnosis/getTheme",
        /**
         * GET-时间范围
         */
        url_time_range: "/yczd/regn/remoteDiagnosis/getTimeRange",
        /**
         * GET-获取装置
         */
        url_device_list: "/yczd/regn/remoteDiagnosis/getDevice",
        /**
         * GET-获取企业
         */
        url_enterprise_list: "/yczd/regn/remoteDiagnosis/getEnterprise",
        /**
         * GET-获取内部资料
         */
        url_file_info: "/yczd/regn/remoteDiagnosis/getFileInfoVo",
        /**
         * GET-新增内部资料
         * query
         * fileIndex
         */
        url_file_save: "/yczd/regn/remoteDiagnosis/saveFileInfo",
        /**
         * GET-获取内部资料类型
         */
        url_file_type_list: "/yczd/regn/remoteDiagnosis/getFileInfoType",
        /**
         * GET-获取内部资料类型-表
         */
        url_file_type_table: "/yczd/regn/remoteDiagnosis/getOutSideFileType",
        /**
         * GET-获取内部资料-表
         */
        url_file_table: "/yczd/regn/remoteDiagnosis/getOutSideFileInfo",
        /**
         * GET-保存内部资料-表
         */
        url_file_table_save: "/yczd/regn/remoteDiagnosis/saveOutSideFileInfo",
        /**
         * GET-删除内部资料-表
         */
        url_file_table_delete: "/yczd/regn/remoteDiagnosis/delOutSideFileInfo",
        /**
         * POST-删除内部资料
         * body
         * fileName,fileType,conferenceName,conferenceDate,
         * filePath,uploadUser,conferenceBeginDate,conferenceEndDate
         */
        url_file_delete: "/yczd/regn/remoteDiagnosis/delFileInfo",
        /**
         * GET-会议表-在线交流
         */
        url_chat_room_list: "/yczd/regn/remoteDiagnosis/getChatroom",
        /**
         * POST-保存会议表-在线交流
         */
        url_chat_room_save: "/yczd/regn/remoteDiagnosis/saveChatroom",
        /**
         * GET-获取会议交流记录表-在线交流
         */
        url_chat_room_records: "/yczd/regn/remoteDiagnosis/getMeetingExchange",
        /**
         * POST-保存会议交流记录表-在线交流
         */
        url_chat_room_record_save: "/yczd/regn/remoteDiagnosis/saveMeetingExchange",
        /**
         * GET-退出会议表-在线交流
         */
        url_chat_room_exit: "/yczd/regn/remoteDiagnosis/exitChatroom",
        /**
         * POST-附件上传
         */
        url_upload: "/yczd/regn/remoteDiagnosis/file",
        /**
         * GET-获取附件
         * query
         * fileName
         */
        url_file_detail: "/yczd/regn/remoteDiagnosis/getFileSteam",

        /**
         * GET-查询股份日报
         */
        url_share_daily_info: "/yczd/regn/remoteDiagnosis/getGFrequestmain",
        /**
         * GET-查询企业日报
         */
        url_company_daily_info: "/yczd/regn/remoteDiagnosis/getQYrequestmain",
    },
    // 硫磺
    sru: {
        /**
         * GET-专家点评查询
         */
        url_comment_list: "/yczd/sru/remoteDiagnosis/getQuery",
        /**
         * GET-专家点评查询-相关信息
         * query
         * adviceId
         */
        url_comment_list_byid: "/yczd/sru/remoteDiagnosis/getQueryById",
        /**
         * GET-论坛公告
         * query
         * sectionId
         */
        url_forum_announce: "/yczd/sru/remoteDiagnosis/getForumAnnouncement",
        /**
         * POST-发表-话题
         * body
         * size,current,adviceId(帖子id),
         * sectionId(所属板块id),type,title,content,
         * sender,sendTime,replyer,replyTime,visible,
         * lastUpdateTime,reserved2(附件数目),reserved
         * replyNum,scanNum,step,padviceId,top(0不置顶,1置顶,2板块公告)
         * image,device,company,senderen,peplyeren,BBSType,inPutBBs,
         * titelLabel,order,timeRange
         */
        url_topic_save: "/yczd/sru/remoteDiagnosis/saveNewTopic",
        /**
         * POST-回复话题
         */
        url_topic_replay: "/yczd/sru/remoteDiagnosis/saveHFTopic",
        /**
         * GET-删除-话题
         * query
         * adviceId
         */
        url_topic_delete: "/yczd/sru/remoteDiagnosis/DelNewTopic",
        /**
         * GET-置顶-话题
         * query
         * adviceId
         */
        url_topic_totop: "/yczd/sru/remoteDiagnosis/isTop",
        /**
         * POST-修改-话题
         */
        url_topic_update: "/yczd/sru/remoteDiagnosis/updateNewTopic",
        /**
         * GET-添加资料类型
         * query
         * typeName
         */
        url_doc_type_save: "/yczd/sru/remoteDiagnosis/addDocumentType",
        /**
         * GET-获取资料类型
         */
        url_doc_type_list: "/yczd/sru/remoteDiagnosis/getDocumentType",
        /**
         * POST-保存资料
         */
        url_doc_save: "/yczd/sru/remoteDiagnosis/saveDocument",
        /**
         * GET-获取资料
         */
        url_doc_list: "/yczd/sru/remoteDiagnosis/getDocument",
        /**
         * GET-查询资料详情
         * query
         * documentId
         */
        url_doc_info: "/yczd/sru/remoteDiagnosis/getDocumentDetails",
        /**
         * GET-技术资料明细
         */
        url_doc_tech_info: "/yczd/sru/remoteDiagnosis/getTechDocumentDetails",
        /**
         * GET-装置介绍
         */
        url_doc_device_info: "/yczd/sru/remoteDiagnosis/getDeviceIntroduction",
        // GET-装置详情
        url_doc_device_detail: "/yczd/sru/remoteDiagnosis/pageDeviceDesc",
        /**
         * GET-发件列表-短信息交流
         */
        url_email_send_list: "/yczd/sru/remoteDiagnosis/getInForExchange",
        /**
         * GET-根据主题查询某人各个主题的未读消息、根据主题查询各个主题的未读消息
         * query
         * id,userId,subject
         */
        url_email_unread_count: "/yczd/sru/remoteDiagnosis/getNoticeCountBySubject",
        /**
         * GET-删除-短信息交流
         * query
         * id
         */
        url_email_send_delete: "/yczd/sru/remoteDiagnosis/getDelInForExchange",
        /**
         * POST-删除-短信息交流
         * query
         * id
         */
        url_email_send_save: "/yczd/sru/remoteDiagnosis/SaveInForExchange",
        /**
         * GET-收件列表-短信息交流
         */
        url_email_receive_list: "/yczd/sru/remoteDiagnosis/getReceiverList",
        /**
         * GET-收件列表-短信息交流
         * ids
         */
        url_email_users_by_ids: "/yczd/sru/remoteDiagnosis/getUsersByIds",
        /**
         * GET-获取主题
         */
        url_theme_list: "/yczd/sru/remoteDiagnosis/getTheme",
        /**
         * GET-时间范围
         */
        url_time_range: "/yczd/sru/remoteDiagnosis/getTimeRange",
        /**
         * GET-获取装置
         */
        url_device_list: "/yczd/sru/remoteDiagnosis/getDevice",
        /**
         * GET-获取企业
         */
        url_enterprise_list: "/yczd/sru/remoteDiagnosis/getEnterprise",
        /**
         * GET-获取内部资料
         */
        url_file_info: "/yczd/sru/remoteDiagnosis/getFileInfoVo",
        /**
         * GET-新增内部资料
         * query
         * fileIndex
         */
        url_file_save: "/yczd/sru/remoteDiagnosis/saveFileInfo",
        /**
         * GET-获取内部资料类型
         */
        url_file_type_list: "/yczd/sru/remoteDiagnosis/getFileInfoType",
        /**
         * GET-获取内部资料类型-表
         */
        url_file_type_table: "/yczd/sru/remoteDiagnosis/getOutSideFileType",
        /**
         * GET-获取内部资料-表
         */
        url_file_table: "/yczd/sru/remoteDiagnosis/getOutSideFileInfo",
        /**
         * GET-保存内部资料-表
         */
        url_file_table_save: "/yczd/sru/remoteDiagnosis/saveOutSideFileInfo",
        /**
         * GET-删除内部资料-表
         */
        url_file_table_delete: "/yczd/sru/remoteDiagnosis/delOutSideFileInfo",
        /**
         * POST-删除内部资料
         * body
         * fileName,fileType,conferenceName,conferenceDate,
         * filePath,uploadUser,conferenceBeginDate,conferenceEndDate
         */
        url_file_delete: "/yczd/sru/remoteDiagnosis/delFileInfo",
        /**
         * GET-会议表-在线交流
         */
        url_chat_room_list: "/yczd/sru/remoteDiagnosis/getChatroom",
        /**
         * POST-保存会议表-在线交流
         */
        url_chat_room_save: "/yczd/sru/remoteDiagnosis/saveChatroom",
        /**
         * GET-获取会议交流记录表-在线交流
         */
        url_chat_room_records: "/yczd/sru/remoteDiagnosis/getMeetingExchange",
        /**
         * POST-保存会议交流记录表-在线交流
         */
        url_chat_room_record_save: "/yczd/sru/remoteDiagnosis/saveMeetingExchange",
        /**
         * GET-退出会议表-在线交流
         */
        url_chat_room_exit: "/yczd/sru/remoteDiagnosis/exitChatroom",
        /**
         * POST-附件上传
         */
        url_upload: "/yczd/sru/remoteDiagnosis/file",
        /**
         * GET-获取附件
         * query
         * fileName
         */
        url_file_detail: "/yczd/sru/remoteDiagnosis/getFileSteam",

        /**
         * GET-查询股份日报
         */
        url_share_daily_info: "/yczd/sru/remoteDiagnosis/getGFrequestmain",
        /**
         * GET-查询企业日报
         */
        url_company_daily_info: "/yczd/sru/remoteDiagnosis/getQYrequestmain",
    },
    // SZORB
    szorb: {
        /**
         * GET-专家点评查询
         */
        url_comment_list: "/yczd/szorb/remoteDiagnosis/getQuery",
        /**
         * GET-专家点评查询-相关信息
         * query
         * adviceId
         */
        url_comment_list_byid: "/yczd/szorb/remoteDiagnosis/getQueryById",
        /**
         * GET-论坛公告
         * query
         * sectionId
         */
        url_forum_announce: "/yczd/szorb/remoteDiagnosis/getForumAnnouncement",
        /**
         * POST-发表-话题
         * body
         * size,current,adviceId(帖子id),
         * sectionId(所属板块id),type,title,content,
         * sender,sendTime,replyer,replyTime,visible,
         * lastUpdateTime,reserved2(附件数目),reserved
         * replyNum,scanNum,step,padviceId,top(0不置顶,1置顶,2板块公告)
         * image,device,company,senderen,peplyeren,BBSType,inPutBBs,
         * titelLabel,order,timeRange
         */
        url_topic_save: "/yczd/szorb/remoteDiagnosis/saveNewTopic",
        /**
         * POST-回复话题
         */
        url_topic_replay: "/yczd/szorb/remoteDiagnosis/saveHFTopic",
        /**
         * GET-删除-话题
         * query
         * adviceId
         */
        url_topic_delete: "/yczd/szorb/remoteDiagnosis/DelNewTopic",
        /**
         * GET-置顶-话题
         * query
         * adviceId
         */
        url_topic_totop: "/yczd/szorb/remoteDiagnosis/isTop",
        /**
         * POST-修改-话题
         */
        url_topic_update: "/yczd/szorb/remoteDiagnosis/updateNewTopic",
        /**
         * GET-添加资料类型
         * query
         * typeName
         */
        url_doc_type_save: "/yczd/szorb/remoteDiagnosis/addDocumentType",
        /**
         * GET-获取资料类型
         */
        url_doc_type_list: "/yczd/szorb/remoteDiagnosis/getDocumentType",
        /**
         * POST-保存资料
         */
        url_doc_save: "/yczd/szorb/remoteDiagnosis/saveDocument",
        /**
         * GET-获取资料
         */
        url_doc_list: "/yczd/szorb/remoteDiagnosis/getDocument",
        /**
         * GET-查询资料详情
         * query
         * documentId
         */
        url_doc_info: "/yczd/szorb/remoteDiagnosis/getDocumentDetails",
        /**
         * GET-技术资料明细
         */
        url_doc_tech_info: "/yczd/szorb/remoteDiagnosis/getTechDocumentDetails",
        /**
         * GET-装置介绍
         */
        url_doc_device_info: "/yczd/szorb/remoteDiagnosis/getDeviceIntroduction",
        // GET-装置详情
        url_doc_device_detail: "/yczd/szorb/remoteDiagnosis/pageDeviceDesc",
        /**
         * GET-发件列表-短信息交流
         */
        url_email_send_list: "/yczd/szorb/remoteDiagnosis/getInForExchange",
        /**
         * GET-根据主题查询某人各个主题的未读消息、根据主题查询各个主题的未读消息
         * query
         * id,userId,subject
         */
        url_email_unread_count: "/yczd/szorb/remoteDiagnosis/getNoticeCountBySubject",
        /**
         * GET-删除-短信息交流
         * query
         * id
         */
        url_email_send_delete: "/yczd/szorb/remoteDiagnosis/getDelInForExchange",
        /**
         * POST-删除-短信息交流
         * query
         * id
         */
        url_email_send_save: "/yczd/szorb/remoteDiagnosis/SaveInForExchange",
        /**
         * GET-收件列表-短信息交流
         */
        url_email_receive_list: "/yczd/szorb/remoteDiagnosis/getReceiverList",
        /**
         * GET-收件列表-短信息交流
         * ids
         */
        url_email_users_by_ids: "/yczd/szorb/remoteDiagnosis/getUsersByIds",
        /**
         * GET-获取主题
         */
        url_theme_list: "/yczd/szorb/remoteDiagnosis/getTheme",
        /**
         * GET-时间范围
         */
        url_time_range: "/yczd/szorb/remoteDiagnosis/getTimeRange",
        /**
         * GET-获取装置
         */
        url_device_list: "/yczd/szorb/remoteDiagnosis/getDevice",
        /**
         * GET-获取企业
         */
        url_enterprise_list: "/yczd/szorb/remoteDiagnosis/getEnterprise",
        /**
         * GET-获取内部资料
         */
        url_file_info: "/yczd/szorb/remoteDiagnosis/getFileInfoVo",
        /**
         * GET-新增内部资料
         * query
         * fileIndex
         */
        url_file_save: "/yczd/szorb/remoteDiagnosis/saveFileInfo",
        /**
         * GET-获取内部资料类型
         */
        url_file_type_list: "/yczd/szorb/remoteDiagnosis/getFileInfoType",
        /**
         * GET-获取内部资料类型-表
         */
        url_file_type_table: "/yczd/szorb/remoteDiagnosis/getOutSideFileType",
        /**
         * GET-获取内部资料-表
         */
        url_file_table: "/yczd/szorb/remoteDiagnosis/getOutSideFileInfo",
        /**
         * GET-保存内部资料-表
         */
        url_file_table_save: "/yczd/szorb/remoteDiagnosis/saveOutSideFileInfo",
        /**
         * GET-删除内部资料-表
         */
        url_file_table_delete: "/yczd/szorb/remoteDiagnosis/delOutSideFileInfo",
        /**
         * POST-删除内部资料
         * body
         * fileName,fileType,conferenceName,conferenceDate,
         * filePath,uploadUser,conferenceBeginDate,conferenceEndDate
         */
        url_file_delete: "/yczd/szorb/remoteDiagnosis/delFileInfo",
        /**
         * GET-会议表-在线交流
         */
        url_chat_room_list: "/yczd/szorb/remoteDiagnosis/getChatroom",
        /**
         * POST-保存会议表-在线交流
         */
        url_chat_room_save: "/yczd/szorb/remoteDiagnosis/saveChatroom",
        /**
         * GET-获取会议交流记录表-在线交流
         */
        url_chat_room_records: "/yczd/szorb/remoteDiagnosis/getMeetingExchange",
        /**
         * POST-保存会议交流记录表-在线交流
         */
        url_chat_room_record_save: "/yczd/szorb/remoteDiagnosis/saveMeetingExchange",
        /**
         * GET-退出会议表-在线交流
         */
        url_chat_room_exit: "/yczd/szorb/remoteDiagnosis/exitChatroom",
        /**
         * POST-附件上传
         */
        url_upload: "/yczd/szorb/remoteDiagnosis/file",
        /**
         * GET-获取附件
         * query
         * fileName
         */
        url_file_detail: "/yczd/szorb/remoteDiagnosis/getFileSteam",

        /**
         * GET-查询股份日报
         */
        url_share_daily_info: "/yczd/szorb/remoteDiagnosis/getGFrequestmain",
        /**
         * GET-查询企业日报
         */
        url_company_daily_info: "/yczd/szorb/remoteDiagnosis/getQYrequestmain",


    }
}