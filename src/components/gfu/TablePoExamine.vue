<script lang="ts" setup>
/**
 * 装置竞赛排名table
 * Author: menglj
 * Date: 20240612
 */
import { ref,reactive} from "vue"
import { message } from "ant-design-vue";
import { iTableColumn,DataItem } from "@/interface/index";
import type { TableProps, TableColumnType } from "ant-design-vue";
import {formatExcelData,exportDataToExcel} from "@/utils/index";
import {forEach,assign,toNumber,orderBy} from "lodash";
import {iPagination} from "@/interface/index";
import useGfuStore from "@/store/gfu";
const storeGfu = useGfuStore();
const columns: TableColumnType<iTableColumn>[] = [
  {
    title: "装置名称",
    dataIndex: "devicename",
    key: "devicename",
  },
  {
    title: "总分\n(满分100分)",
    children:[
      {
        title:"排名",
        dataIndex: "rank",
        key: "rank",
      },
      {
        title:"总分",
        dataIndex: "total",
        key: "total",
      },
    ]
  },
  {
    title: "非计划停工次数\n(满分20分)",
    children:[
      {
        title:"得分",
        dataIndex: "fjhtgdf",
        key: "fjhtgdf",
      },
      {
        title:"天数",
        dataIndex: "fjhtgts",
        key: "fjhtgts",
      },
      {
        title:"次数",
        dataIndex: "fjhtgcs",
        key: "fjhtgcs",
      }
    ]
  },
  {
    title: "能耗/10+损失\n(满分25分)",
    children:[
      {
        title:"得分",
        dataIndex: "nhdf",
        key: "nhdf",
      },
      {
        title:"值",
        dataIndex: "nhz",
        key: "nhz",
      }
    ]
  },
  {
    title: "长周期运行天数\n(满分5分)",
    children:[
      {
        title:"得分",
        dataIndex: "czqyxdf",
        key: "czqyxdf",
      },
      {
        title:"天数",
        dataIndex: "czqyxts",
        key: "czqyxts",
      },
      {
        title:"利用率",
        dataIndex: "czqyxlyl",
        key: "czqyxlyl",
      },
    ]
  },
  {
    title: "[汽油收率×1.2+柴油收率+液化气收率]×(原料密度/1000)×{100/(100-原料残炭×0.8)}+掺渣比例/20\n(满分10分)",
    children:[
      {
        title:"得分",
        dataIndex: "qysldf",
        key: "qysldf",
        width:120,
      },
      {
        title:"值",
        dataIndex: "qyslz",
        key: "qyslz",
        width:120,
      },
    ]
  },
  {
    title: "烟机累计回收功率/主风机累计功耗\n(满分5分)",
    children:[
      {
        title:"得分",
        dataIndex: "yjljhsdf",
        key: "yjljhsdf",
        width:100,
      },
      {
        title:"值",
        dataIndex: "yjljhsz",
        key: "yjljhsz",
        width:100,
      },
    ]
  },
  {
    title: "（干气C3+含量）/转化率\n(满分5分)",
    children:[
      {
        title:"得分",
        dataIndex: "gqdf",
        key: "gqdf",
        width:100,
      },
      {
        title:"值",
        dataIndex: "gqz",
        key: "gqz",
        width:100,
      }
    ]
  },
  {
    title: "（干气+生焦）/转化率\n(满分10分)",
    children:[
      {
        title:"得分",
        dataIndex: "sjdf",
        key: "sjdf",
        width:100,
      },
      {
        title:"值",
        dataIndex: "sjz",
        key: "sjz",
        width:100,
      }
    ]
  },
  {
    title: "汽油收率xRon\n(满分5分)",
    children:[
      {
        title:"得分",
        dataIndex: "qyrondf",
        key: "qyrondf",
        width:100,
      },
      {
        title:"值",
        dataIndex: "qyronz",
        key: "qyronz",
        width:100,
      }
    ]
  },
  {
    title: "汽油和柴油重叠度\n(满分5分)",
    children:[
      {
        title:"得分",
        dataIndex: "cycdddf",
        key: "cycdddf",
        width:100,
      },
      {
        title:"值",
        dataIndex: "cycddz",
        key: "cycddz",
        width:100,
      },
    ]
  }
];
const isFetching = ref<boolean>(false);
const rowSelection: TableProps["rowSelection"] = {
  onSelect: (record: DataItem, selected: boolean, selectedRows: DataItem[]) => {
    let arr: any = [];
    selectedRows.forEach((item, index) => {
      if (item != undefined) {
        arr.push(item);
      }
    });
  },
  onSelectAll: (
    selected: boolean,
    selectedRows: DataItem[],
    changeRows: DataItem[]
  ) => {
    let arr: any = [];
    selectedRows.forEach((item, index) => {
      if (item != undefined) {
        arr.push(item);
      }
    });
  },
};
const scrollTableOption = reactive({ x: "max-content"});
// const scrollTableOption = reactive({ x: 1200, y: 0 });
// function updateScrollY() {
//   scrollTableOption.y = getTableScrollHeight(".table");
// }
// // 列表
// onMounted(() => {
//   nextTick(() => {
//     updateScrollY()
//   })
// })

function displayText(text:string){
  let _obj = text&&text.split(",")||"",
    _text = _obj&&_obj[0]!="null"&&_obj[0]!="NULL"?_obj[0]:"-";
  // console.log("===text==",text,"===",_text);
  return _text;
}
// 计算总分和排名
function caclTotal(list:any[]){
  let newlist=[];
  // 计算总分
  forEach(list,(v)=>{
    const _total = toNumber(v.fjhtgdf)+toNumber(v.nhdf)+toNumber(v.czqyxdf)+toNumber(v.qysldf)+toNumber(v.yjljhsdf)+toNumber(v.gqdf)+toNumber(v.sjdf)+toNumber(v.qyrondf)+toNumber(v.cycdddf);
    assign(v,{total:_total});
    newlist.push(v);
  })
  // 根据总分排名
  newlist = orderBy(newlist,"total","desc");
  // 排序
  // forEach(newlist,(v,idx)=>{
  //   assign(v,{rank:idx+1})
  // })
  return newlist;
}
const listTableData = ref([]);
function fetchTableData(){
  storeGfu.fetchPOTableInfo({
    pageCode:"po_examine"
  }).then((resp:any)=>{
    listTableData.value = caclTotal(resp);
  }).catch((err)=>{
    // listTableData.value = caclTotal(TableData);
    // console.log("==err==",err)
    message.error(err.statusText||err.message||err.msg||err||"数据错误!");
  })
}
fetchTableData();

function doExport(){
  const excelData = formatExcelData(columns,listTableData.value);
  // console.log("===do export===",columns,"====",listTableData.value);
  // console.log("====",excelData);

  exportDataToExcel(excelData,"table_device_stability_rate_gfu","装置平稳率");
}
defineExpose({
  doExport
});
const currentPage = ref<number>(0);
function onTableChange(pagination:iPagination){
  currentPage.value = (pagination.current)*pagination.pageSize;
}
</script>
<template>
<div class="cmp table_po_examine">
  <a-table 
    :title="()=>{ return '得分排名表'}"
    class="table" 
    bordered 
    :columns="columns" 
    :data-source="listTableData" 
    :rowKey="(record) => { return 'po-examine-' + record.devicename; }" 
    :footer="null"
    :scroll="scrollTableOption"
    :loading="isFetching"
    @change="onTableChange">
    <template #bodyCell="{ column, record, index }">
      <template v-if="column.key==='rank'">{{ index+1+currentPage }}</template>
    </template>
  </a-table>
</div>
</template>
<style lang="less" scoped>
.cmp.table_po_examine{
  &:deep(.table){
    .ant-table-title{text-align:center;}
    .warn{
      color:red;
      font-weight:bold;
    }
  }
}
</style>