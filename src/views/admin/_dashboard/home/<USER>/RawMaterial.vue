<script>
import dashboard from "@/store/dashboard";
const storeDashboard = dashboard();
export default {
  data(){
    return {
      isFetching: false,
      currentTab: "0",
      tableKey: 0,
      tableColumns: [],
      columnsConfig: {
        0: [
          {prop: 'project', label: '项目'},
          {prop: 'cell1', label: '加裂'},
          {prop: 'cell2', label: '渣油'},
          {prop: 'cell3', label: '蜡油'},
          {prop: 'cell4', label: '柴油'},
          {prop: 'cell5', label: '航煤'},
          {prop: 'cell6', label: '催汽'},
          {prop: 'cell7', label: '焦汽'},
          {prop: 'cell8', label: '硫磺'},
          {prop: 'cell9', label: '制氢'}
        ],
        1: [
          {prop: 'project', label: '项目'},
          {prop: 'cell1', label: '常减压'},
          {prop: 'cell2', label: '催化裂化'},
          {prop: 'cell3', label: '延迟焦化'},
          {prop: 'cell4', label: '连续重整'},
          {prop: 'cell5', label: '半再生重整'},
          {prop: 'cell6', label: 'S Zorb'},
          {prop: 'cell7', label: '气分'},
          {prop: 'cell8', label: 'MTBE'}
        ]
      },
      listData: []
    }
  },
  created(){
    this.fetchRawMatrial();
  },
  methods:{
    doChangeTab(tab) {
      this.currentTab = tab;
      this.fetchRawMatrial();
    },
    fetchRawMatrial(){
      if(this.isFetching) return;
      this.isFetching = true;
      this.tableColumns = this.columnsConfig[this.currentTab];
      this.listData = [];
      storeDashboard.fetchRawMatrial(this.currentTab).then((resp)=>{
        // const respData =cd
        // // console.log("===resp==",respData);
        // for(let i=0;i<10;i++) {
        //   let record = respData[i],
        //     sourceData = this.listData[i]||{};
        //   for(let j=0;j<9;j++){
        //     const d = record&&record[j]||'-';
        //     // console.log("====",record,"===",d);
        //     set(sourceData,`t1c${j+1}`,d);
        //   }
        //   this.listData[i] = sourceData;
        // }
        this.listData = resp.retResult||[];
        this.tableKey += 1;
        // console.log("==data==",this.listData);
      }).finally(()=>{
        this.isFetching = false;
      });
    }
  }
}
</script>
<template>
<div class="panel overview">
  <div class="head">
    <h4 class="title">装置原料性质</h4>
  </div>
  <div class="content">
    <ul class="tabs">
      <li class="tab" :class="{'current':currentTab==='0'}" @click.stop="doChangeTab('0')">临氢装置</li>
      <li class="tab" :class="{'current':currentTab==='1'}" @click.stop="doChangeTab('1')">非临氢装置</li>
    </ul>
    <el-table :key="tableKey" :loading="isFetching" :data="listData" style="width: 100%">
      <template v-for="column in tableColumns">
        <el-table-column :key="column.prop" :prop="column.prop" :label="column.label"
                         :width="column.width"></el-table-column>
      </template>
    </el-table>
  </div>
</div>
</template>
<style lang="less" scoped>
@import "../style.less";
.panel{
  >.content{
    padding-right:.1rem;
  }
}
</style>