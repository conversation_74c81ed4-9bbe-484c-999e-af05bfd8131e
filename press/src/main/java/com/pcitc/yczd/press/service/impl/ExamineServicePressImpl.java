package com.pcitc.yczd.press.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.pcitc.yczd.press.entity.PageConfigEntity;
import com.pcitc.yczd.press.mapper.PageConfigPressMapper;
import com.pcitc.yczd.press.service.*;
import com.pcitc.yczd.press.utils.ExamineUtil;
import com.pcitc.yczd.press.utils.YczdCataUtils;
import com.pcitc.yczd.press.vo.ExamineSocraparamPressVo;
import com.pcitc.yczd.press.vo.TablePageConfigVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: yczd-springboot
 * @description: ExamineServiceIpml
 * @author: pengchong.li
 * @createTime: 2024-05-24 14:38
 **/
@Service
@Slf4j
public class ExamineServicePressImpl implements IExamineServicePress {


    @Autowired
    private IExaminePressStandDownService iExaminePressStandDownService;
    @Autowired
    private IExaminePressConsumptionService iExaminePressConsumptionService;
    @Autowired
    private IExaminePressTotalCataService iExaminePressTotalCataService;
    @Autowired
    private IExaminePressService iExaminePressService;
    @Autowired
    private IExamineLiquidService iExamineLiquidService;
    @Autowired
    private IExamineHydrogenationService iExamineHydrogenationService;
    @Autowired
    private IExamineHotService iExamineHotService;
    @Autowired
    private IExamineAftersaltoffPressService iExamineAftersaltoffPressService;
    @Autowired
    private IExamineAfterwateroffPressService iExamineAfterwateroffPressService;
    @Autowired
    private IExamineDesalinationratePressService iExamineDesalinationratePressService;
    @Autowired
    private IExamineDrainoilPressService iExamineDrainoilPressService;
    @Autowired
    private IExamineOilDensityPressService iExamineOilDensityPressService;
    @Autowired
    private IExamineFurnacePressService iExamineFurnacePressService;
    @Autowired
    private IExamineDesalinationPressService iExamineDesalinationPressService;
    @Autowired
    private IExamineSaltcontentPressService iExamineSaltcontentPressService;
    @Autowired
    private IExamineOverlapPressService iExamineOverlapPressService;
    @Autowired
    private IExamineDeeppullPressService iExamineDeeppullPressService;
    @Autowired
    private PageConfigPressMapper pageConfigPressMapper;

    public Map<String, List<TablePageConfigVO>> getExaminelist(String[] group, String pageencode, List<TablePageConfigVO> list, String starttime,
                                                               String endtime) {
        List dataList = new ArrayList();
        Map<String, List<TablePageConfigVO>> newmapDevice = new Hashtable();
        try {
            List<String> dwlist =
                    list.stream().map(TablePageConfigVO::getDwcode).distinct().collect(Collectors.toList());
            Map<String, Map<String, List<String>>> idc_examine_value = new Hashtable<>();


            String IdKey = "";
            String device_id, items_id;
            Map<String, List<TablePageConfigVO>> mapDevice = new Hashtable();

            Map<String, List<String>> IDC_examineItem = new Hashtable<>();
            for (int i = 0; i < dwlist.size(); i++) {
                //if(i==1) {
                final String dwcode = dwlist.get(i);
                List<TablePageConfigVO> listdwcode =
                        list.stream().filter(ivo -> ivo.getDwcode().equals(dwcode)).collect(Collectors.toList());
                IdKey = dwlist.get(i);
                for (int j = 0; j < listdwcode.size(); j++) {
                    TablePageConfigVO vo = new TablePageConfigVO();
                    vo.setMax(listdwcode.get(j).getMax());
                    vo.setMin(listdwcode.get(j).getMin());
                    vo.setEtc(listdwcode.get(j).getEtc());
                    if (listdwcode.get(j).getClasstype().indexOf("ExamineStandDown") != -1) {
                        List<String> douList =iExaminePressStandDownService.GetCalculateValue(listdwcode.get(j).getEtc()
                                , starttime, endtime);
                        IDC_examineItem.put(listdwcode.get(j).getDesc(), douList);
                    }
                    if (listdwcode.get(j).getClasstype().indexOf("ExamineConsumption") != -1) {
                        List<String> douList =
                              iExaminePressConsumptionService.GetCalculateValue(listdwcode.get(j).getEtc()
                                        , starttime, endtime);
                        IDC_examineItem.put(listdwcode.get(j).getDesc(), douList);
                    }
                    if (listdwcode.get(j).getClasstype().indexOf("ExamineTotal") != -1) {
                        List<String> douList =iExaminePressTotalCataService.getCalculateValue(listdwcode.get(j).getEtc()
                                , starttime, endtime);
                        IDC_examineItem.put(listdwcode.get(j).getDesc(), douList);
                    }
                    if (listdwcode.get(j).getClasstype().indexOf("ExamineLiquid") != -1) {
                        List<String> douList =iExamineLiquidService.GetCalculateValue(listdwcode.get(j).getEtc()
                                , starttime, endtime);
                        IDC_examineItem.put(listdwcode.get(j).getDesc(), douList);
                    }
                    if (listdwcode.get(j).getClasstype().indexOf("ExamineHydrogenation") != -1) {
                        List<String> douList =iExamineHydrogenationService.GetCalculateValue(listdwcode.get(j).getEtc()
                                , starttime, endtime);
                        IDC_examineItem.put(listdwcode.get(j).getDesc(), douList);
                    }
                    if (listdwcode.get(j).getClasstype().indexOf("ExamineHot") != -1) {
                        List<String> douList =iExamineHotService.GetCalculateValue(listdwcode.get(j).getEtc()
                                , starttime, endtime);
                        IDC_examineItem.put(listdwcode.get(j).getDesc(), douList);
                    }
                }
                idc_examine_value.put(dwlist.get(i), IDC_examineItem);
                //}

            }
            // 从已存在的数据中找出各个装置的停工时常，判断出哪些企业不参与考核

            List<String> stopname = new ArrayList();//存放不显示的要过滤掉的企业
            List<TablePageConfigVO> fjhtglist =
                    list.stream().filter(ivo -> ivo.getDesc().equals("非计划停工次数")).collect(Collectors.toList());
            for (int i = 0; i < idc_examine_value.size(); i++) {
                for (int j = 0; j < fjhtglist.size(); j++) {
                    if (idc_examine_value.get(fjhtglist.get(j).getDwcode()) != null) {
                        List<String> stopL = idc_examine_value.get(fjhtglist.get(j).getDwcode()).get(group[0]);
                        if (Double.parseDouble(stopL.get(2).split("\\,")[2]) > 90)
                            stopname.add(idc_examine_value.get(fjhtglist.get(j).getDwcode()).get(group[0]).toString());
                    }
                }
            }
            Map<String, Double> IDC1 = new HashMap<>();
            Map<String, Double> IDC2 = new HashMap<>();
            Map<String, Double> IDC3 = new HashMap<>();
            Map<String, Double> IDC4 = new HashMap<>();
            for (int i = 0; i < idc_examine_value.size(); i++) {
                for (int m = 0; m < dwlist.size(); m++) {

                    if (idc_examine_value.get(dwlist.get(m)) != null) {
                        for (Map.Entry<String, Map<String, List<String>>> item : idc_examine_value.entrySet()
                        ) {
                            double LI1 = 0;
                            double LI2 = 0;
                            double LI3 = 0;
                            double LI4 = 0;
                            for (Map.Entry<String, List<String>> ite : idc_examine_value.get(dwlist.get(m)).entrySet()) {
                                String keys = ite.getKey();
                                switch (keys) {
                                    case "能耗/10+损失":
                                        LI1 = Double.parseDouble(ite.getValue().get(0).split("\\,")[2]);
                                        break;
                                    case "总液收+原料硫含量+原料氮含量×5":
                                        LI2 = Double.parseDouble(ite.getValue().get(0).split("\\,")[2]);
                                        break;
                                    case "氢气利用率":
                                        LI3 = Double.parseDouble(ite.getValue().get(0).split("\\,")[2]);
                                        break;
                                    case "加热炉热效率":
                                        LI4 = Double.parseDouble(ite.getValue().get(0).split("\\,")[2]);
                                        break;
                                    default:
                                        break;
                                }
                            }
                            IDC1.put(item.getKey(), getXsValue(LI1, 2));
                            IDC2.put(item.getKey(), getXsValue(LI2, 2));
                            IDC3.put(item.getKey(), getXsValue(LI3, 2));
                            IDC4.put(item.getKey(), getXsValue(LI4, 2));
                        }
                    }
                }
                iExaminePressConsumptionService.GetScore(IDC1);
                iExamineLiquidService.GetScore(IDC2);
                iExamineHydrogenationService.GetScore(IDC3);
                iExamineHotService.GetScore(IDC4);


                List<TablePageConfigVO> listtablepage =
                        getTablePageConfig("CMMain","0","0");
                List<String> dwlistdesc =
                        list.stream().map(TablePageConfigVO::getDesc).distinct().collect(Collectors.toList());
                for (int n = 0; n < dwlistdesc.size(); n++) {
                    List<TablePageConfigVO> tablelistdate = new ArrayList<>();
                    final String dwcode = dwlistdesc.get(n);
                    List<TablePageConfigVO> listdwcode =
                            list.stream().filter(ivo -> ivo.getDesc().equals(dwcode)).collect(Collectors.toList());
                    IdKey = dwlist.get(n);
                    if (!stopname.contains(IdKey)) {
                        for (int m = 0; m < listdwcode.size(); m++) {
                            TablePageConfigVO vo = new TablePageConfigVO();
                            vo.setDevicename(DeviceNameTrans(listtablepage, listdwcode.get(m).getDwcode()));
                            vo.setMax(listdwcode.get(m).getMax());
                            vo.setMin(listdwcode.get(m).getMin());
                            vo.setEtc(listdwcode.get(m).getEtc());
                        /*if(listdwcode.get(m).getDesc().equals(group[0])){
                            List<Double> douList =examinestanddownservice.GetCalculateValue(listdwcode.get(m).getEtc()
                                    , starttime, endtime);
                            IDC_examineItem.put(listdwcode.get(m).getDesc(), douList);
                        }*/
                            if (listdwcode.get(m).getDesc().equals(group[0])) {
                                vo.setScore(iExaminePressStandDownService.GetScore(idc_examine_value, IdKey,
                                        listdwcode.get(m).getDesc(), stopname,
                                        Double.parseDouble(listdwcode.get(m).getMax())));
                            }
                            if (listdwcode.get(m).getDesc().equals(group[1])) {
                                vo.setScore(iExaminePressConsumptionService.GetSocre(IDC1, idc_examine_value, IdKey,
                                        listdwcode.get(m).getDesc()));
                            }
                            if (listdwcode.get(m).getDesc().equals(group[2])) {
                                vo.setScore(iExaminePressTotalCataService.getSocre(IDC1, idc_examine_value, IdKey,
                                        listdwcode.get(m).getDesc()));
                            }
                            if (listdwcode.get(m).getDesc().equals(group[3])) {
                                vo.setScore(iExamineLiquidService.GetSocre(IDC1, idc_examine_value, IdKey,
                                        listdwcode.get(m).getDesc()));
                            }
                            if (listdwcode.get(m).getDesc().equals(group[4])) {
                                vo.setScore(iExamineHydrogenationService.GetSocre(IDC1, idc_examine_value, IdKey,
                                        listdwcode.get(m).getDesc()));
                            }
                            if (listdwcode.get(m).getDesc().equals(group[5])) {
                                vo.setScore(iExamineHotService.GetSocre(IDC1, idc_examine_value, IdKey,
                                        listdwcode.get(m).getDesc()));
                            }
                            /*ExamineUtil.RegisterExam(listdwcode.get(m).getDesc(),vo);*/
                            tablelistdate.add(vo);

                        }

                        mapDevice.put(dwlistdesc.get(n), tablelistdate);
                    } else {
                        log.debug("企业" + IdKey + "由于停工时常超过90天，不计入考核。");
                    }

                }
                //为提高第一张图的显示速度，将数据处理工作放在了buffer刷新中实现
                //其余的数据处理工作放在叶面中实现
                List value = new ArrayList();
                List key = new ArrayList();
                for (Map.Entry<String, List<TablePageConfigVO>> item : mapDevice.entrySet()
                ) {

                    key.add(item.getKey());
                    value.add(item.getValue());
                }

            /*dataList.clear();
            dataList.add(key);
            dataList.add(value);
            dataList.add(mapDevice);*/
                newmapDevice = mapDevice;
                log.debug("===========================装置考核相关内容成功缓存=======================");
                //第三列存放原始的未加工的类
                return newmapDevice;
            }
        } catch (Exception e) {
            log.error("=====================刷新装置考核得分时报错==============原因：");
            log.error(e.getMessage());
            // //return false;
            /* throw new Exception(e.getMessage() + "堆栈信息：" + e.getStackTrace());*/
        }
        return newmapDevice;
    }

    public Map<String, List<TablePageConfigVO>> getPressExaminelist(String[] group, String pageencode,
                                                                    List<TablePageConfigVO> list, String starttime,
                                                                    String endtime) {
        List dataList = new ArrayList();
        Map<String, List<TablePageConfigVO>> newmapDevice = new Hashtable();
        try {
            List<String> dwlist =
                    list.stream().map(TablePageConfigVO::getDwcode).distinct().collect(Collectors.toList());
            Map<String, Map<String, List<String>>> idc_examine_value = new Hashtable<>();


            String IdKey = "";
            String device_id, items_id;
            Map<String, List<TablePageConfigVO>> mapDevice = new Hashtable();

            Map<String, List<String>> IDC_examineItem = new Hashtable<>();
            for (int i = 0; i < dwlist.size(); i++) {
                //if(i==1) {
                final String dwcode = dwlist.get(i);
                List<TablePageConfigVO> listdwcode =
                        list.stream().filter(ivo -> ivo.getDwcode().equals(dwcode)).collect(Collectors.toList());
                IdKey = dwlist.get(i);
                for (int j = 0; j < listdwcode.size(); j++) {
                    TablePageConfigVO vo = new TablePageConfigVO();
                    vo.setMax(listdwcode.get(j).getMax());
                    vo.setMin(listdwcode.get(j).getMin());
                    vo.setEtc(listdwcode.get(j).getEtc());
                    if (listdwcode.get(j).getClasstype().indexOf("ExamineStandDown") != -1) {
                        List<String> douList =iExaminePressStandDownService.GetCalculateValue(listdwcode.get(j).getEtc()
                                , starttime, endtime);
                        IDC_examineItem.put(listdwcode.get(j).getDesc(), douList);
                    }
                    if (listdwcode.get(j).getClasstype().indexOf("ExamineConsumption_Press") != -1) {
                        List<String> douList =
                                iExaminePressConsumptionService.GetCalculateValue(listdwcode.get(j).getEtc()
                                        , starttime, endtime);
                        IDC_examineItem.put(listdwcode.get(j).getDesc(), douList);
                    }
                    if (listdwcode.get(j).getClasstype().indexOf("ExamineTotal_Press") != -1) {
                        List<String> douList =iExaminePressTotalCataService.getCalculateValue(listdwcode.get(j).getEtc()
                                , starttime, endtime);
                        IDC_examineItem.put(listdwcode.get(j).getDesc(), douList);
                    }
                    if (listdwcode.get(j).getClasstype().indexOf("ExamineOilDensity_Press") != -1) {
                        List<String> douList =
                                iExamineOilDensityPressService.GetCalculateValue(listdwcode.get(j).getEtc()
                                , starttime, endtime);
                        IDC_examineItem.put(listdwcode.get(j).getDesc(), douList);
                    }
                    if (listdwcode.get(j).getClasstype().indexOf("ExamineFurnace_Press") != -1) {
                        List<String> douList =iExamineFurnacePressService.GetCalculateValue(listdwcode.get(j).getEtc()
                                , starttime, endtime);
                        IDC_examineItem.put(listdwcode.get(j).getDesc(), douList);
                    }
                    if (listdwcode.get(j).getClasstype().indexOf("ExamineDesalination_Press") != -1) {
                        List<String> douList =iExamineDesalinationPressService.GetCalculateValue(listdwcode.get(j).getEtc()
                                , starttime, endtime);
                        IDC_examineItem.put(listdwcode.get(j).getDesc(), douList);
                    }
                    if (listdwcode.get(j).getClasstype().indexOf("ExamineSaltcontent_Press") != -1) {
                        List<String> douList =iExamineSaltcontentPressService.GetCalculateValue(listdwcode.get(j).getEtc()
                                , starttime, endtime);
                        IDC_examineItem.put(listdwcode.get(j).getDesc(), douList);
                    }
                    if (listdwcode.get(j).getClasstype().indexOf("ExamineOverlap_Press") != -1) {
                        List<String> douList =iExamineOverlapPressService.GetCalculateValue(listdwcode.get(j).getEtc()
                                , starttime, endtime);
                        IDC_examineItem.put(listdwcode.get(j).getDesc(), douList);
                    }
                    if (listdwcode.get(j).getClasstype().indexOf("ExamineDeeppull_Press") != -1) {
                        List<String> douList =iExamineDeeppullPressService.GetCalculateValue(listdwcode.get(j).getEtc()
                                , starttime, endtime);
                        IDC_examineItem.put(listdwcode.get(j).getDesc(), douList);
                    }
                }
                idc_examine_value.put(dwlist.get(i), IDC_examineItem);
                //}

            }
            // 从已存在的数据中找出各个装置的停工时常，判断出哪些企业不参与考核

            List<String> stopname = new ArrayList();//存放不显示的要过滤掉的企业
            List<TablePageConfigVO> fjhtglist =
                    list.stream().filter(ivo -> ivo.getDesc().equals("非计划停工次数")).collect(Collectors.toList());
            for (int i = 0; i < idc_examine_value.size(); i++) {
                for (int j = 0; j < fjhtglist.size(); j++) {
                    if (idc_examine_value.get(fjhtglist.get(j).getDwcode()) != null) {
                        List<String> stopL = idc_examine_value.get(fjhtglist.get(j).getDwcode()).get(group[0]);
                        if (Double.parseDouble(stopL.get(2).split("\\,")[2]) > 90)
                            stopname.add(idc_examine_value.get(fjhtglist.get(j).getDwcode()).get(group[0]).toString());
                    }
                }
            }
            Map<String, Double> IDC1 = new HashMap<>();
            Map<String, Double> IDC2 = new HashMap<>();
            Map<String, Double> IDC3 = new HashMap<>();
            for (int i = 0; i < idc_examine_value.size(); i++) {
                for (int m = 0; m < dwlist.size(); m++) {

                    if (idc_examine_value.get(dwlist.get(m)) != null) {
                        for (Map.Entry<String, Map<String, List<String>>> item : idc_examine_value.entrySet()
                        ) {
                            double LI1 = 0;
                            double LI2 = 0;
                            double LI3 = 0;
                            for (Map.Entry<String, List<String>> ite : idc_examine_value.get(dwlist.get(m)).entrySet()) {
                                String keys = ite.getKey();
                                switch (keys) {
                                    case "能耗":
                                        LI1 = Double.parseDouble(ite.getValue().stream().filter(iov->iov.indexOf(keys)!=-1).collect(Collectors.toList()).get(0).split("\\,")[2]);
                                        break;
                                    case "原油密度²×总拔/能耗":
                                        LI2 = Double.parseDouble(ite.getValue().stream().filter(iov->iov.indexOf(keys)!=-1).collect(Collectors.toList()).get(0).split("\\,")[2]);
                                        break;
                                    case "加热炉热效率":
                                        LI3 = Double.parseDouble(ite.getValue().stream().filter(iov->iov.indexOf(keys)!=-1).collect(Collectors.toList()).get(0).split("\\,")[2]);
                                        break;
                                    default:
                                        break;
                                }
                            }
                            IDC1.put(item.getKey(), getXsValue(LI1, 2));
                            IDC2.put(item.getKey(), getXsValue(LI2, 2));
                            IDC3.put(item.getKey(), getXsValue(LI3, 2));
                        }
                    }
                }
                iExaminePressConsumptionService.GetScore(IDC1);
                iExamineOilDensityPressService.GetScore(IDC2);
                iExamineFurnacePressService.GetScore(IDC3);


                List<TablePageConfigVO> listtablepage =
                        getTablePageConfig("CMMain","0","0");
                List<String> dwlistdesc =
                        list.stream().map(TablePageConfigVO::getDesc).distinct().collect(Collectors.toList());
                for (int n = 0; n < dwlistdesc.size(); n++) {
                    List<TablePageConfigVO> tablelistdate = new ArrayList<>();
                    final String dwcode = dwlistdesc.get(n);
                    List<TablePageConfigVO> listdwcode =
                            list.stream().filter(ivo -> ivo.getDesc().equals(dwcode)).collect(Collectors.toList());
                    IdKey = dwlist.get(n);
                    if (!stopname.contains(IdKey)) {
                        for (int m = 0; m < listdwcode.size(); m++) {
                            TablePageConfigVO vo = new TablePageConfigVO();
                            vo.setDevicename(DeviceNameTrans(listtablepage, listdwcode.get(m).getDwcode()));
                            vo.setMax(listdwcode.get(m).getMax());
                            vo.setMin(listdwcode.get(m).getMin());
                            vo.setEtc(listdwcode.get(m).getEtc());

                            if (listdwcode.get(m).getDesc().equals(group[1])) {
                                vo.setScore(iExaminePressConsumptionService.GetSocre(IDC1, idc_examine_value, IdKey,
                                        listdwcode.get(m).getDesc()));
                            }

                            if (listdwcode.get(m).getDesc().equals(group[3])) {
                                vo.setScore(iExamineOilDensityPressService.GetSocre(IDC2, idc_examine_value, IdKey,
                                        listdwcode.get(m).getDesc()));
                            }
                            if (listdwcode.get(m).getDesc().equals(group[4])) {
                                vo.setScore(iExamineFurnacePressService.GetSocre(IDC3, idc_examine_value, IdKey,
                                        listdwcode.get(m).getDesc()));
                            }

                            if (listdwcode.get(m).getDesc().equals(group[8])) {
                                vo.setScore(iExamineDeeppullPressService.GetSocre(IDC3, idc_examine_value, IdKey,
                                        listdwcode.get(m).getDesc()));
                            }
                            if (listdwcode.get(m).getDesc().equals(group[2])) {
                                vo.setScore(iExaminePressTotalCataService.getSocre(IDC3, idc_examine_value, IdKey,
                                        listdwcode.get(m).getDesc()));
                            }
                            /*ExamineUtil.RegisterExam(listdwcode.get(m).getDesc(),vo);*/
                            tablelistdate.add(vo);

                        }

                        mapDevice.put(dwlistdesc.get(n), tablelistdate);
                    } else {
                        log.debug("企业" + IdKey + "由于停工时常超过90天，不计入考核。");
                    }

                }
                //为提高第一张图的显示速度，将数据处理工作放在了buffer刷新中实现
                //其余的数据处理工作放在叶面中实现
                List value = new ArrayList();
                List key = new ArrayList();
                for (Map.Entry<String, List<TablePageConfigVO>> item : mapDevice.entrySet()
                ) {

                    key.add(item.getKey());
                    value.add(item.getValue());
                }

            /*dataList.clear();
            dataList.add(key);
            dataList.add(value);
            dataList.add(mapDevice);*/
                newmapDevice = mapDevice;
                log.debug("===========================装置考核相关内容成功缓存=======================");
                //第三列存放原始的未加工的类
                return newmapDevice;
            }
        } catch (Exception e) {
            log.error("=====================刷新装置考核得分时报错==============原因：");
            log.error(e.getMessage());
            // //return false;
            /* throw new Exception(e.getMessage() + "堆栈信息：" + e.getStackTrace());*/
        }
        return newmapDevice;
    }

    public List getExaminelistnew() {

        List<TablePageConfigVO> list =
                getTablePageConfig("ExamineConfig","2","0").stream().filter(ivo ->ivo.getMax()!=null &&  !ivo.getMax().equals("")).collect(Collectors.toList());
        List dataList = new ArrayList();
        String starttime = "";
        String endtime = "";
        Map<String, List<TablePageConfigVO>> newmapDevice = new Hashtable();
        try {
            List<String> dwlist =
                    list.stream().map(TablePageConfigVO::getDwcode).distinct().collect(Collectors.toList());
            Map<String, Map<String, List<String>>> idc_examine_value = new Hashtable<>();


            String IdKey = "";
            String device_id, items_id;
            Map<String, List<TablePageConfigVO>> mapDevice = new Hashtable();

            Map<String, List<String>> IDC_examineItem = new Hashtable<>();
            for (int i = 0; i < dwlist.size(); i++) {
                //if(i==41||i==42||i==43) {
                final String dwcode = dwlist.get(i);
                List<TablePageConfigVO> listdwcode =
                        list.stream().filter(ivo -> ivo.getDwcode().equals(dwcode)).collect(Collectors.toList());
                IdKey = dwlist.get(i);
                for (int j = 0; j < listdwcode.size(); j++) {
                    TablePageConfigVO vo = new TablePageConfigVO();
                    vo.setMax(listdwcode.get(j).getMax());
                    vo.setMin(listdwcode.get(j).getMin());
                    vo.setEtc(listdwcode.get(j).getEtc());
                    if (listdwcode.get(j).getClasstype().indexOf("ExamineStandDown") != -1) {
                        List<String> douList =iExaminePressStandDownService.GetCalculateValue(listdwcode.get(j).getEtc()
                                , starttime, endtime);
                        IDC_examineItem.put(listdwcode.get(j).getDesc(), douList);
                    }
                    if (listdwcode.get(j).getClasstype().indexOf("ExamineConsumption") != -1) {
                        List<String> douList =
                               iExaminePressConsumptionService.GetCalculateValue(listdwcode.get(j).getEtc()
                                        , starttime, endtime);
                        IDC_examineItem.put(listdwcode.get(j).getDesc(), douList);
                    }
                    if (listdwcode.get(j).getClasstype().indexOf("ExamineTotal") != -1) {
                        List<String> douList = iExaminePressTotalCataService.getCalculateValue(listdwcode.get(j).getEtc()
                                , starttime, endtime);
                        IDC_examineItem.put(listdwcode.get(j).getDesc(), douList);
                    }
                    if (listdwcode.get(j).getClasstype().indexOf("ExamineLiquid") != -1) {
                        List<String> douList = iExamineLiquidService.GetCalculateValue(listdwcode.get(j).getEtc()
                                , starttime, endtime);
                        IDC_examineItem.put(listdwcode.get(j).getDesc(), douList);
                    }
                    if (listdwcode.get(j).getClasstype().indexOf("ExamineHydrogenation") != -1) {
                        List<String> douList = iExamineHydrogenationService.GetCalculateValue(listdwcode.get(j).getEtc()
                                , starttime, endtime);
                        IDC_examineItem.put(listdwcode.get(j).getDesc(), douList);
                    }
                    if (listdwcode.get(j).getClasstype().indexOf("ExamineHot") != -1) {
                        List<String> douList = iExamineHotService.GetCalculateValue(listdwcode.get(j).getEtc()
                                , starttime, endtime);
                        IDC_examineItem.put(listdwcode.get(j).getDesc(), douList);
                    }

                }
                idc_examine_value.put(dwlist.get(i), IDC_examineItem);
                //}

            }
            String[] group = new String[]{"非计划停工次数", "能耗/10+损失", "长周期运行天数", "总液收+原料硫含量+原料氮含量×5", "氢气利用率", "加热炉热效率"};
            // 从已存在的数据中找出各个装置的停工时常，判断出哪些企业不参与考核

            List<String> stopname = new ArrayList();//存放不显示的要过滤掉的企业
            List<TablePageConfigVO> fjhtglist =
                    list.stream().filter(ivo -> ivo.getDesc().equals("非计划停工次数")).collect(Collectors.toList());
            for (int i = 0; i < idc_examine_value.size(); i++) {
                for (int j = 0; j < fjhtglist.size(); j++) {
                    if (idc_examine_value.get(fjhtglist.get(j).getDwcode()) != null) {
                        List<String> stopL = idc_examine_value.get(fjhtglist.get(j).getDwcode()).get(group[0]);
                        if (Double.parseDouble(stopL.get(2).split("\\,")[2]) > 90)
                            stopname.add(idc_examine_value.get(fjhtglist.get(j).getDwcode()).get(group[0]).toString());
                    }
                }
            }
            Map<String, Double> IDC1 = new HashMap<>();
            Map<String, Double> IDC2 = new HashMap<>();
            Map<String, Double> IDC3 = new HashMap<>();
            Map<String, Double> IDC4 = new HashMap<>();
            for (int i = 0; i < idc_examine_value.size(); i++) {
                for (int m = 0; m < dwlist.size(); m++) {

                    if (idc_examine_value.get(dwlist.get(m)) != null) {
                        for (Map.Entry<String, Map<String, List<String>>> item : idc_examine_value.entrySet()
                        ) {
                            double LI1 = 0;
                            double LI2 = 0;
                            double LI3 = 0;
                            double LI4 = 0;
                            for (Map.Entry<String, List<String>> ite : idc_examine_value.get(dwlist.get(m)).entrySet()) {
                                String keys = ite.getKey();
                                switch (keys) {

                                    case "能耗/10+损失":
                                        LI1 = Double.parseDouble(ite.getValue().get(0).split("\\,")[2]);
                                        break;
                                    case "总液收+原料硫含量+原料氮含量×5":
                                        LI2 = Double.parseDouble(ite.getValue().get(0).split("\\,")[2]);
                                        break;
                                    case "氢气利用率":
                                        LI3 = Double.parseDouble(ite.getValue().get(0).split("\\,")[2]);
                                        break;
                                    case "加热炉热效率":
                                        LI4 = Double.parseDouble(ite.getValue().get(0).split("\\,")[2]);
                                        break;
                                    default:
                                        break;
                                }
                            }
                            IDC1.put(item.getKey(), getXsValue(LI1, 2));
                            IDC2.put(item.getKey(), getXsValue(LI2, 2));
                            IDC3.put(item.getKey(), getXsValue(LI3, 2));
                            IDC4.put(item.getKey(), getXsValue(LI4, 2));
                        }
                    }
                }
                iExaminePressConsumptionService.GetScore(IDC1);
                iExamineLiquidService.GetScore(IDC2);
                iExamineHydrogenationService.GetScore(IDC3);
                iExamineHotService.GetScore(IDC4);

                List<TablePageConfigVO> listtablepage =
                        getTablePageConfig("CMMain","0","0");
                List<String> dwlistdesc =
                        list.stream().map(TablePageConfigVO::getDesc).distinct().collect(Collectors.toList());
                for (int n = 0; n < dwlistdesc.size(); n++) {
                    List<TablePageConfigVO> tablelistdate = new ArrayList<>();
                    final String dwcode = dwlistdesc.get(n);
                    List<TablePageConfigVO> listdwcode =
                            list.stream().filter(ivo -> ivo.getDesc().equals(dwcode)).collect(Collectors.toList());
                    IdKey = dwlist.get(n);
                    if (!stopname.contains(IdKey)) {

                        for (int m = 0; m < listdwcode.size(); m++) {
                            TablePageConfigVO vo = new TablePageConfigVO();
                            vo.setDwcode(listdwcode.get(m).getDwcode());
                            vo.setPointidkey(IdKey);
                            vo.setDevicename(DeviceNameTrans(listtablepage, listdwcode.get(m).getDwcode()));
                            vo.setMax(listdwcode.get(m).getMax());
                            vo.setMin(listdwcode.get(m).getMin());
                            vo.setEtc(listdwcode.get(m).getEtc());
                        /*if(listdwcode.get(m).getDesc().equals(group[0])){
                            List<Double> douList =examinestanddownservice.GetCalculateValue(listdwcode.get(m).getEtc()
                                    , starttime, endtime);
                            IDC_examineItem.put(listdwcode.get(m).getDesc(), douList);
                        }*/
                            if (listdwcode.get(m).getDesc().equals(group[1])) {
                                vo.setScore(iExaminePressConsumptionService.GetSocre(IDC1, idc_examine_value, IdKey,
                                        listdwcode.get(m).getDesc()));
                            }

                            if (listdwcode.get(m).getDesc().equals(group[3])) {
                                vo.setScore(iExamineOilDensityPressService.GetSocre(IDC2, idc_examine_value, IdKey,
                                        listdwcode.get(m).getDesc()));
                            }
                            if (listdwcode.get(m).getDesc().equals(group[4])) {
                                vo.setScore(iExamineFurnacePressService.GetSocre(IDC3, idc_examine_value, IdKey,
                                        listdwcode.get(m).getDesc()));
                            }

                            if (listdwcode.get(m).getDesc().equals(group[8])) {
                                vo.setScore(iExamineDeeppullPressService.GetSocre(IDC3, idc_examine_value, IdKey,
                                        listdwcode.get(m).getDesc()));
                            }
                            if (listdwcode.get(m).getDesc().equals(group[2])) {
                                vo.setScore(iExaminePressTotalCataService.getSocre(IDC3, idc_examine_value, IdKey,
                                        listdwcode.get(m).getDesc()));
                            }
                            /*ExamineUtil.RegisterExam(listdwcode.get(m).getDesc(),vo);*/
                            tablelistdate.add(vo);

                        }

                        mapDevice.put(dwlistdesc.get(n), tablelistdate);
                    } else {
                        log.debug("企业" + IdKey + "由于停工时常超过90天，不计入考核。");
                    }

                }
                //为提高第一张图的显示速度，将数据处理工作放在了buffer刷新中实现
                //其余的数据处理工作放在叶面中实现
                List value = new ArrayList();
                List key = new ArrayList();
                for (Map.Entry<String, List<TablePageConfigVO>> item : mapDevice.entrySet()
                ) {

                    key.add(item.getKey());
                    value.add(item.getValue());
                }

                dataList.clear();
                dataList.add(key);
                dataList.add(value);
                dataList.add(mapDevice);
                /*newmapDevice=mapDevice;*/
                log.debug("===========================装置考核相关内容成功缓存=======================");
                //第三列存放原始的未加工的类
                return dataList;
            }
        } catch (Exception e) {
            log.error("=====================刷新装置考核得分时报错==============原因：");
            log.error(e.getMessage());
            // //return false;
            /* throw new Exception(e.getMessage() + "堆栈信息：" + e.getStackTrace());*/
        }
        return dataList;
    }

    public List getExaminelistPressnew() {

        List<TablePageConfigVO> list =
                getTablePageConfig("ExamineConfig","2","0").stream().filter(ivo ->ivo.getMax()!=null &&  !ivo.getMax().equals("")).collect(Collectors.toList());
        List dataList = new ArrayList();
        String starttime = "";
        String endtime = "";
        Map<String, List<TablePageConfigVO>> newmapDevice = new Hashtable();
        try {
            List<String> dwlist =
                    list.stream().map(TablePageConfigVO::getDwcode).distinct().collect(Collectors.toList());
            Map<String, Map<String, List<String>>> idc_examine_value = new Hashtable<>();


            String IdKey = "";
            String device_id, items_id;
            Map<String, List<TablePageConfigVO>> mapDevice = new Hashtable();

            Map<String, List<String>> IDC_examineItem = new Hashtable<>();
            for (int i = 0; i < dwlist.size(); i++) {
                //if(i==41||i==42||i==43) {
                final String dwcode = dwlist.get(i);
                List<TablePageConfigVO> listdwcode =
                        list.stream().filter(ivo -> ivo.getDwcode().equals(dwcode)).collect(Collectors.toList());
                IdKey = dwlist.get(i);
                for (int j = 0; j < listdwcode.size(); j++) {
                    TablePageConfigVO vo = new TablePageConfigVO();
                    vo.setMax(listdwcode.get(j).getMax());
                    vo.setMin(listdwcode.get(j).getMin());
                    vo.setEtc(listdwcode.get(j).getEtc());
                    if (listdwcode.get(j).getClasstype().indexOf("ExamineStandDown") != -1) {
                        List<String> douList =iExaminePressStandDownService.GetCalculateValue(listdwcode.get(j).getEtc()
                                , starttime, endtime);
                        IDC_examineItem.put(listdwcode.get(j).getDesc(), douList);
                    }
                    if (listdwcode.get(j).getClasstype().indexOf("ExamineConsumption_Press") != -1) {
                        List<String> douList =
                                iExaminePressConsumptionService.GetCalculateValue(listdwcode.get(j).getEtc()
                                        , starttime, endtime);
                        IDC_examineItem.put(listdwcode.get(j).getDesc(), douList);
                    }
                    if (listdwcode.get(j).getClasstype().indexOf("ExamineTotal_Press") != -1) {
                        List<String> douList =iExaminePressTotalCataService.getCalculateValue(listdwcode.get(j).getEtc()
                                , starttime, endtime);
                        IDC_examineItem.put(listdwcode.get(j).getDesc(), douList);
                    }
                    if (listdwcode.get(j).getClasstype().indexOf("ExamineOilDensity_Press") != -1) {
                        List<String> douList =
                                iExamineOilDensityPressService.GetCalculateValue(listdwcode.get(j).getEtc()
                                        , starttime, endtime);
                        IDC_examineItem.put(listdwcode.get(j).getDesc(), douList);
                    }
                    if (listdwcode.get(j).getClasstype().indexOf("ExamineFurnace_Press") != -1) {
                        List<String> douList =iExamineFurnacePressService.GetCalculateValue(listdwcode.get(j).getEtc()
                                , starttime, endtime);
                        IDC_examineItem.put(listdwcode.get(j).getDesc(), douList);
                    }
                    if (listdwcode.get(j).getClasstype().indexOf("ExamineDesalination_Press") != -1) {
                        List<String> douList =iExamineDesalinationPressService.GetCalculateValue(listdwcode.get(j).getEtc()
                                , starttime, endtime);
                        IDC_examineItem.put(listdwcode.get(j).getDesc(), douList);
                    }
                    if (listdwcode.get(j).getClasstype().indexOf("ExamineSaltcontent_Press") != -1) {
                        List<String> douList =iExamineSaltcontentPressService.GetCalculateValue(listdwcode.get(j).getEtc()
                                , starttime, endtime);
                        IDC_examineItem.put(listdwcode.get(j).getDesc(), douList);
                    }
                    if (listdwcode.get(j).getClasstype().indexOf("ExamineOverlap_Press") != -1) {
                        List<String> douList =iExamineOverlapPressService.GetCalculateValue(listdwcode.get(j).getEtc()
                                , starttime, endtime);
                        IDC_examineItem.put(listdwcode.get(j).getDesc(), douList);
                    }
                    if (listdwcode.get(j).getClasstype().indexOf("ExamineDeeppull_Press") != -1) {
                        List<String> douList =iExamineDeeppullPressService.GetCalculateValue(listdwcode.get(j).getEtc()
                                , starttime, endtime);
                        IDC_examineItem.put(listdwcode.get(j).getDesc(), douList);
                    }
                }
                idc_examine_value.put(dwlist.get(i), IDC_examineItem);
                //}

            }
            String[] group = new String[]{"非计划停工次数", "能耗", "长周期运行天数", "原油密度²×总拔/能耗", "加热炉热效率", "脱盐合格率", "脱后含盐", "常1、2重叠度", "减压深拔"};
            // 从已存在的数据中找出各个装置的停工时常，判断出哪些企业不参与考核

            List<String> stopname = new ArrayList();//存放不显示的要过滤掉的企业
            List<TablePageConfigVO> fjhtglist =
                    list.stream().filter(ivo -> ivo.getDesc().equals("非计划停工次数")).collect(Collectors.toList());
            for (int i = 0; i < idc_examine_value.size(); i++) {
                for (int j = 0; j < fjhtglist.size(); j++) {
                    if (idc_examine_value.get(fjhtglist.get(j).getDwcode()) != null) {
                        List<String> stopL = idc_examine_value.get(fjhtglist.get(j).getDwcode()).get(group[0]);
                        if (Double.parseDouble(stopL.get(2).split("\\,")[2]) > 90)
                            stopname.add(idc_examine_value.get(fjhtglist.get(j).getDwcode()).get(group[0]).toString());
                    }
                }
            }
            Map<String, Double> IDC1 = new HashMap<>();
            Map<String, Double> IDC2 = new HashMap<>();
            Map<String, Double> IDC3 = new HashMap<>();
            for (int i = 0; i < idc_examine_value.size(); i++) {
                for (int m = 0; m < dwlist.size(); m++) {

                    if (idc_examine_value.get(dwlist.get(m)) != null) {
                        for (Map.Entry<String, Map<String, List<String>>> item : idc_examine_value.entrySet()
                        ) {
                            double LI1 = 0;
                            double LI2 = 0;
                            double LI3 = 0;
                            for (Map.Entry<String, List<String>> ite : idc_examine_value.get(dwlist.get(m)).entrySet()) {
                                String keys = ite.getKey();
                                switch (keys) {

                                    case "能耗":
                                        LI1 = Double.parseDouble(ite.getValue().get(0).split("\\,")[2]);
                                        break;
                                    case "原油密度²×总拔/能耗":
                                        LI2 = Double.parseDouble(ite.getValue().get(0).split("\\,")[2]);
                                        break;
                                    case "加热炉热效率":
                                        LI3 = Double.parseDouble(ite.getValue().get(0).split("\\,")[2]);
                                        break;
                                    default:
                                        break;
                                }
                            }
                            IDC1.put(item.getKey(), getXsValue(LI1, 2));
                            IDC2.put(item.getKey(), getXsValue(LI2, 2));
                            IDC3.put(item.getKey(), getXsValue(LI3, 2));
                        }
                    }
                }
                iExaminePressConsumptionService.GetScore(IDC1);
                iExamineOilDensityPressService.GetScore(IDC2);
                iExamineFurnacePressService.GetScore(IDC3);

                List<TablePageConfigVO> listtablepage =
                        getTablePageConfig("CMMain","0","0");
                List<String> dwlistdesc =
                        list.stream().map(TablePageConfigVO::getDesc).distinct().collect(Collectors.toList());
                for (int n = 0; n < dwlistdesc.size(); n++) {
                    List<TablePageConfigVO> tablelistdate = new ArrayList<>();
                    final String dwcode = dwlistdesc.get(n);
                    List<TablePageConfigVO> listdwcode =
                            list.stream().filter(ivo -> ivo.getDesc().equals(dwcode)).collect(Collectors.toList());
                    IdKey = dwlist.get(n);
                    if (!stopname.contains(IdKey)) {
                        for (int m = 0; m < listdwcode.size(); m++) {
                            TablePageConfigVO vo = new TablePageConfigVO();
                            vo.setDwcode(listdwcode.get(m).getDwcode());
                            vo.setPointidkey(IdKey);
                            vo.setDevicename(DeviceNameTrans(listtablepage, listdwcode.get(m).getDwcode()));
                            vo.setMax(listdwcode.get(m).getMax());
                            vo.setMin(listdwcode.get(m).getMin());
                            vo.setEtc(listdwcode.get(m).getEtc());
                            if (listdwcode.get(m).getDesc().equals(group[1])) {
                                vo.setScore(iExaminePressConsumptionService.GetSocre(IDC1, idc_examine_value, IdKey,
                                        listdwcode.get(m).getDesc()));
                            }

                            if (listdwcode.get(m).getDesc().equals(group[3])) {
                                vo.setScore(iExamineOilDensityPressService.GetSocre(IDC2, idc_examine_value, IdKey,
                                        listdwcode.get(m).getDesc()));
                            }
                            if (listdwcode.get(m).getDesc().equals(group[4])) {
                                vo.setScore(iExamineFurnacePressService.GetSocre(IDC3, idc_examine_value, IdKey,
                                        listdwcode.get(m).getDesc()));
                            }

                            if (listdwcode.get(m).getDesc().equals(group[8])) {
                                vo.setScore(iExamineDeeppullPressService.GetSocre(IDC3, idc_examine_value, IdKey,
                                        listdwcode.get(m).getDesc()));
                            }
                            if (listdwcode.get(m).getDesc().equals(group[2])) {
                                vo.setScore(iExaminePressTotalCataService.getSocre(IDC3, idc_examine_value, IdKey,
                                        listdwcode.get(m).getDesc()));
                            }
                            /*ExamineUtil.RegisterExam(listdwcode.get(m).getDesc(),vo);*/
                            tablelistdate.add(vo);

                        }

                        mapDevice.put(dwlistdesc.get(n), tablelistdate);
                    } else {
                        log.debug("企业" + IdKey + "由于停工时常超过90天，不计入考核。");
                    }

                }
                //为提高第一张图的显示速度，将数据处理工作放在了buffer刷新中实现
                //其余的数据处理工作放在叶面中实现
                List value = new ArrayList();
                List key = new ArrayList();
                for (Map.Entry<String, List<TablePageConfigVO>> item : mapDevice.entrySet()
                ) {

                    key.add(item.getKey());
                    value.add(item.getValue());
                }

                dataList.clear();
                dataList.add(key);
                dataList.add(value);
                dataList.add(mapDevice);
                /*newmapDevice=mapDevice;*/
                log.debug("===========================装置考核相关内容成功缓存=======================");
                //第三列存放原始的未加工的类
                return dataList;
            }
        } catch (Exception e) {
            log.error("=====================刷新装置考核得分时报错==============原因：");
            log.error(e.getMessage());
            // //return false;
            /* throw new Exception(e.getMessage() + "堆栈信息：" + e.getStackTrace());*/
        }
        return dataList;
    }

    /// <summary>
    /// 转换中英文名称
    /// </summary>
    /// <param name="Ename"></param>
    /// <returns></returns>
    public String DeviceNameTrans(List<TablePageConfigVO> list, String Ename) {

        String labername = "";
        for (int i = 0; i < list.size(); i++) {
            if (list.get(i).getDwcode().equals(Ename))
                labername = list.get(i).getDevicename()!=null && !list.get(i).getDevicename().equals("")?list.get(i).getDevicename():list.get(i).getDwname();;
        }
        return labername;
    }

    public Double getXsValue(Double value, Integer number) {
        BigDecimal bg = new BigDecimal(value);
        Double f1 = bg.setScale(number, BigDecimal.ROUND_HALF_UP).doubleValue();
        return f1;
    }

    public List<ExamineSocraparamPressVo> getExamineExamineConfiglist() {
        List<ExamineSocraparamPressVo> list2 = new ArrayList<>();
        List list = getExaminelistPressnew();
        Map<String, List<TablePageConfigVO>> mapDevice  = (Map<String, List<TablePageConfigVO>>)list.get(2);
       /* List<TablePageConfigVO> dt=tablepageconfigservice.GetTablePageConfigList("DAMain");*/
        List<TablePageConfigVO> dt =
                getTablePageConfig("DAMain","0","0");
        List<String> bufIL0=(List<String>)list.get(0);
        List<TablePageConfigVO> bufIL1=(List<TablePageConfigVO>)list.get(1);
        //bufIL[0]为装置名称，bufIL[1]为得分，先排序，再按排序的找对应的项的细项内容
        list2= CreatTable(dt, bufIL0,bufIL1, mapDevice);
        return list2;
    }
    private List<ExamineSocraparamPressVo> CreatTable(List<TablePageConfigVO> dt, List<String> bufIL0, List<TablePageConfigVO> bufIL1,
                            Map<String, List<TablePageConfigVO>> mapDevice)
    {
        String[] group = new String[]{"非计划停工次数", "能耗", "长周期运行天数", "原油密度²×总拔/能耗", "加热炉热效率", "脱盐合格率", "脱后含盐", "常1、2重叠度", "减压深拔"};
        List<ExamineSocraparamPressVo> list=new ArrayList<>();
        /*AddTitle();
        List sortL = SortDoubleList(bufIL0, bufIL1);*/
        /*for (int i = 0; i < ((List)sortL.get(0)).size(); i++)
        {*/
        List<String> listdou= ExamineUtil.GetRealValues();
        List<String> devicelist=
                mapDevice.get("非计划停工次数").stream().map(TablePageConfigVO::getDevicename).distinct().collect(Collectors.toList());
        for (int i = 0; i < devicelist.size(); i++) {
            ExamineSocraparamPressVo vo = new ExamineSocraparamPressVo();
            final String devicname=devicelist.get(i);
            for (int j = 0; j < group.length; j++) {
                List<TablePageConfigVO> fjht=mapDevice.get(group[j]);
                List<TablePageConfigVO> tablepointvalue=
                        fjht.stream().filter(ivo -> ivo.getDevicename().equals(devicname)).collect(Collectors.toList());
                String idkeynew="";
                if("非计划停工次数".equals(group[j])) {
                    String etc =
                            fjht.stream().filter(ivo -> ivo.getDevicename().equals(devicname)).collect(Collectors.toList()).get(0).getEtc();
                    idkeynew = etc.split("\\|")[0];
                }else {
                    idkeynew=
                            fjht.stream().filter(ivo -> ivo.getDevicename().equals(devicname)).collect(Collectors.toList()).get(0).getPointidkey();
                }
                final String idkey=idkeynew;
                final  String groupstring=group[j];
                List<String> listdouble=
                        listdou.stream().filter(ivo -> ivo.indexOf(groupstring) != -1 && ivo.indexOf(idkey) != -1).collect(Collectors.toList());
                for (int k = 0; k < listdouble.size(); k++) {
                    String[] valuelist = listdouble.get(k).split("\\,");
                    vo.setDevicename(devicname);
                    Double pointvalue=tablepointvalue.get(0).getScore()==null?0:tablepointvalue.get(0).getScore();
                    if (valuelist[0].indexOf("非计划停工次数") != -1) {
                        if (valuelist[0].indexOf("次数") != -1) {
                            vo.setFjhtgcs(YczdCataUtils.getXsValue(Double.parseDouble(valuelist[2]),0));
                        }else {
                            vo.setFjhtgcs("0");
                        }
                        if (valuelist[0].indexOf("天数") != -1) {
                            vo.setFjhtgts(YczdCataUtils.getXsValue(Double.parseDouble(valuelist[2]),0));
                        }else {
                            vo.setFjhtgts("0");
                        }
                        if(tablepointvalue.size()>0){
                            vo.setFjhtgdf(YczdCataUtils.getXsValue(pointvalue,0));
                        }else {
                            vo.setFjhtgdf("0");
                        }
                    }
                    if (valuelist[0].indexOf("能耗") != -1) {
                        if (valuelist[0].indexOf("能耗") != -1) {
                            vo.setNhz(YczdCataUtils.getXsValue(Double.parseDouble(valuelist[2]),0));
                        }else {
                            vo.setNhz("0");
                        }
                        if(tablepointvalue.size()>0){
                            vo.setNhdf(YczdCataUtils.getXsValue(pointvalue,1));
                        }else {
                            vo.setNhdf("0");
                        }
                    }
                    if (valuelist[0].indexOf("长周期运行天数") != -1) {
                        if (valuelist[0].indexOf("天数") != -1) {
                            vo.setCzqyxts(YczdCataUtils.getXsValue(Double.parseDouble(valuelist[2]),0));
                        }else {
                            vo.setCzqyxts("0");
                        }
                        if (valuelist[0].indexOf("利用率") != -1) {
                            vo.setCzqyxlyl(YczdCataUtils.getXsValue(Double.parseDouble(valuelist[2]),0));
                        }else {
                            vo.setCzqyxlyl("0");
                        }
                        if(tablepointvalue.size()>0){
                            vo.setCzqyxdf(YczdCataUtils.getXsValue(pointvalue,1));
                        }else {
                            vo.setCzqyxdf("0");
                        }
                    }
                    if (valuelist[0].indexOf("原油密度²×总拔/能耗") != -1) {
                        if (valuelist[0].indexOf("原油密度²×总拔/能耗") != -1) {
                            vo.setYymdz(YczdCataUtils.getXsValue(Double.parseDouble(valuelist[2]),0));
                        }else {
                            vo.setYymdz("0");
                        }
                        if(tablepointvalue.size()>0){
                            vo.setYymddf(YczdCataUtils.getXsValue(pointvalue,0));
                        }else {
                            vo.setYymddf("0");
                        }
                    }
                    if (valuelist[0].indexOf("加热炉热效率") != -1) {
                        if (valuelist[0].indexOf("加热炉热效率") != -1) {
                            vo.setJrlrxlz(YczdCataUtils.getXsValue(Double.parseDouble(valuelist[2]),2));
                        }else {
                            vo.setJrlrxlz("0");
                        }
                        if(tablepointvalue.size()>0){
                            vo.setJrlrxldf(YczdCataUtils.getXsValue(pointvalue,0));
                        }else {
                            vo.setJrlrxldf("0");
                        }
                    }
                    if (valuelist[0].indexOf("脱盐合格率") != -1) {
                        if (valuelist[0].indexOf("脱盐合格率") != -1) {
                            vo.setTyhglz(YczdCataUtils.getXsValue(Double.parseDouble(valuelist[2]),0));
                        }else {
                            vo.setTyhglz("0");
                        }
                        if(tablepointvalue.size()>0){
                            vo.setTyhgldf(YczdCataUtils.getXsValue(pointvalue,1));
                        }else {
                            vo.setTyhgldf("0");
                        }
                    }
                    if (valuelist[0].indexOf("脱后含盐") != -1) {
                        if (valuelist[0].indexOf("脱后含盐") != -1) {
                            vo.setThhyz(YczdCataUtils.getXsValue(Double.parseDouble(valuelist[2]),0));
                        }else {
                            vo.setThhyz("0");
                        }
                        if(tablepointvalue.size()>0){
                            vo.setThhydf(YczdCataUtils.getXsValue(tablepointvalue.get(0).getScore(),0));
                        }else {
                            vo.setThhydf("0");
                        }
                    }
                    if (valuelist[0].indexOf("常1、2重叠度") != -1) {
                        if (valuelist[0].indexOf("常1、2重叠度") != -1) {
                            vo.setCcddz(YczdCataUtils.getXsValue(Double.parseDouble(valuelist[2]),0));
                        }else {
                            vo.setCcddz("0");
                        }
                        if(tablepointvalue.size()>0){
                            vo.setCcdddf(YczdCataUtils.getXsValue(tablepointvalue.get(0).getScore(),0));
                        }else {
                            vo.setCcdddf("0");
                        }
                    }
                    if (valuelist[0].indexOf("减压深拔") != -1) {
                        if (valuelist[0].indexOf("减压深拔") != -1) {
                            vo.setJysbz(YczdCataUtils.getXsValue(Double.parseDouble(valuelist[2]),0));
                        }else {
                            vo.setJysbz("0");
                        }
                        if(tablepointvalue.size()>0){
                            vo.setJysbdf(YczdCataUtils.getXsValue(pointvalue,0));
                        }else {
                            vo.setJysbdf("0");
                        }
                    }
                }
            }
            list.add(vo);
        }

        /*for (Map.Entry<String, List<TablePageConfigVO>> item : mapDevice.entrySet()
            ) {

            //for (int j = 0; j < group.length; j++) {
                for (int i = 0; i < item.getValue().size(); i++) {
                    ExamineSocraparamVo vo = new ExamineSocraparamVo();
                    *//*String devicename = item.getValue().get(0).getDevicename();
                    String score = item.getValue().get(0).getScore().toString();*//*
                    final String dwname=item.getValue().get(i).getDevicename();
                    final String dwcode=item.getValue().get(i).getDwcode();
                    final String groupstring=item.getKey();
                    final String pointidkey=item.getValue().get(i).getPointidkey();
                    List<String> listdouble=listdou.stream().filter(ivo -> ivo.indexOf(pointidkey) != -1 && ivo.indexOf(groupstring) != -1).collect(Collectors.toList());

                  *//* if (groupstring.equals("烟机累计回收功率/主风机累计耗功")) {
                       listdouble =

                   }else {
                       listdouble =
                               listdou.stream().filter(ivo -> ivo.indexOf(dwcode) != -1 && ivo.indexOf(groupstring) != -1).collect(Collectors.toList());
                   }*//*




                }


            //}

        }*/
        //region === 弃用
            /*//插入装置名称
            insertcell(((IList)sortL[0])[i].ToString(), tableRow, 6, i, "table_blue1");
            //插入排名序号
            insertcell((i + 1).ToString(), tableRow, 2.0, i, "table_blue3");
            //插入排名得分
            insertcell(((IList)sortL[1])[i].ToString(), tableRow, 2.0, i, "table_blue3");

            //循环考核项
            foreach (DictionaryEntry ExamItems in IDicDevice)
            {
                if (ExamItems.Key.ToString() == ((IList)sortL[0])[i].ToString())
                {
                    int k = 0;
                    string cssStyle = "table_blue3";
                    for (int j = 0; j < dt.Rows.Count; j++)
                    {
                        if (k % 2 == 0)
                            cssStyle = "table_yellow2";
                        else
                            cssStyle = "table_blue3";
                        insertcell(((Examine)ExamItems.Value).GetScore(dt.Rows[j][0].ToString()).ToString(), tableRow, 2.0, i, cssStyle);
                        foreach (double readValue in ((Examine)ExamItems.Value).GetRealValues(dt.Rows[j][0].ToString()))
                        {
                            string num = readValue > 1 ? Math.Round(readValue, 1).ToString() : Math.Round(readValue, 2).ToString();
                            num = readValue > 100 ? Math.Round(readValue, 0).ToString() : num;
                            insertcell(num, tableRow, 2.0, i, cssStyle);
                        }
                        k++;
                    }
                }
            }
            this.Table1.Rows.Add(tableRow);*/


        //}
        //endregion
        return list;
    }
    private void AddTitle()
    {
        List<TablePageConfigVO> datalise=new ArrayList<>();
        /*List<TablePageConfigVO> dt=tablepageconfigservice.GetTablePageConfigList("ExamineTitle");*/
        List<TablePageConfigVO> dt =
                getTablePageConfig("ExamineTitle","0","0");

        List<TablePageConfigVO> dt1 =
                dt.stream().filter(ivo -> ivo.getTitletype().equals("1")).collect(Collectors.toList());;
        List<TablePageConfigVO> dt2 =
                dt.stream().filter(ivo -> ivo.getTitletype().equals("2")).collect(Collectors.toList());;

        datalise.addAll(dt1);
        datalise.addAll(dt2);
    }
    /// <summary>
    /// 返回一个list中最大值的索引
    /// </summary>
    /// <param name="list"></param>
    /// <returns></returns>
    private List<String> SortDoubleList(List<String> name, List<TablePageConfigVO> value)
    {
        List<String> valuev=new ArrayList<>();
        List<String> resultL = new ArrayList();
        String tempscore = "0";
        String tempname = "";
        for (int i = 0; i < value.size() - 1; i++)
        {
            for (int j = i + 1; j < value.size(); j++)
            {
                if (Double.parseDouble(value.get(i).getScore().toString()) < Double.parseDouble(value.get(j).getScore().toString()))
                {
                    tempscore = value.get(i).getScore().toString();
                    valuev.set(i,value.get(j).getScore().toString());
                    valuev.set(j,tempscore);
                    /*tempname = name.get(i).toString();
                    name.set(i,name.get(j).toString());
                    name.set(j,tempname);*/
                    tempname = name.get(i).toString();
                    name.set(i,name.get(j).toString());
                    name.set(j,tempname);
                }
            }
        }
        resultL.addAll(name);
        resultL.addAll(valuev);
        return resultL;
    }

    public Map<String, List<TablePageConfigVO>> getDesaltingExaminelist(String[] group, String pageencode, List<TablePageConfigVO> list, String starttime,
                                                               String endtime) {
        List dataList = new ArrayList();
        Map<String, List<TablePageConfigVO>> newmapDevice = new Hashtable();
        try {
            List<String> dwlist =
                    list.stream().map(TablePageConfigVO::getDwcode).distinct().collect(Collectors.toList());
            Map<String, Map<String, List<String>>> idc_examine_value = new Hashtable<>();


            String IdKey = "";
            String device_id, items_id;
            Map<String, List<TablePageConfigVO>> mapDevice = new Hashtable();

            Map<String, List<String>> IDC_examineItem = new Hashtable<>();
            for (int i = 0; i < dwlist.size(); i++) {
                //if(i==1) {
                final String dwcode = dwlist.get(i);
                List<TablePageConfigVO> listdwcode =
                        list.stream().filter(ivo -> ivo.getDwcode().equals(dwcode)).collect(Collectors.toList());
                IdKey = dwlist.get(i);
                for (int j = 0; j < listdwcode.size(); j++) {
                    TablePageConfigVO vo = new TablePageConfigVO();
                    vo.setMax(listdwcode.get(j).getMax());
                    vo.setMin(listdwcode.get(j).getMin());
                    vo.setEtc(listdwcode.get(j).getEtc());
                    if (listdwcode.get(j).getClasstype().indexOf("ExamineStandDown") != -1) {
                        List<String> douList = iExaminePressStandDownService.GetCalculateValue(listdwcode.get(j).getEtc()
                                , starttime, endtime);
                        IDC_examineItem.put(listdwcode.get(j).getDesc(), douList);
                    }
                    if (listdwcode.get(j).getClasstype().indexOf("ExamineAftersaltoff_Press") != -1) {
                        List<String> douList = iExamineAftersaltoffPressService.GetCalculateValue(listdwcode.get(j).getEtc()
                                , starttime, endtime);
                        IDC_examineItem.put(listdwcode.get(j).getDesc(), douList);
                    }
                    if (listdwcode.get(j).getClasstype().indexOf("ExamineDesalinationrate_Press") != -1) {
                        List<String> douList =
                                iExamineDesalinationratePressService.GetCalculateValue(listdwcode.get(j).getEtc()
                                        , starttime, endtime);
                        IDC_examineItem.put(listdwcode.get(j).getDesc(), douList);
                    }
                    if (listdwcode.get(j).getClasstype().indexOf("ExamineAfterwateroff_Press") != -1) {
                        List<String> douList =
                                iExamineAfterwateroffPressService.GetCalculateValue(listdwcode.get(j).getEtc()
                                , starttime, endtime);
                        IDC_examineItem.put(listdwcode.get(j).getDesc(), douList);
                    }
                    if (listdwcode.get(j).getClasstype().indexOf("ExamineDrainoil_Press") != -1) {
                        List<String> douList = iExamineDrainoilPressService.GetCalculateValue(listdwcode.get(j).getEtc()
                                , starttime, endtime);
                        IDC_examineItem.put(listdwcode.get(j).getDesc(), douList);
                    }


                }
                idc_examine_value.put(dwlist.get(i), IDC_examineItem);
                //}

            }
            // 从已存在的数据中找出各个装置的停工时常，判断出哪些企业不参与考核

            List<String> stopname = new ArrayList();//存放不显示的要过滤掉的企业
            List<TablePageConfigVO> fjhtglist =
                    list.stream().filter(ivo -> ivo.getDesc().equals("非计划停工次数")).collect(Collectors.toList());
            for (int i = 0; i < idc_examine_value.size(); i++) {
                for (int j = 0; j < fjhtglist.size(); j++) {
                    if (idc_examine_value.get(fjhtglist.get(j).getDwcode()) != null) {
                        List<String> stopL = idc_examine_value.get(fjhtglist.get(j).getDwcode()).get(group[0]);
                        if (Double.parseDouble(stopL.get(2).split("\\,")[2]) > 90)
                            stopname.add(idc_examine_value.get(fjhtglist.get(j).getDwcode()).get(group[0]).toString());
                    }
                }
            }
            Map<String, Double> IDC1 = new HashMap<>();
            Map<String, Double> IDC2 = new HashMap<>();
            Map<String, Double> IDC3 = new HashMap<>();
            Map<String, Double> IDC4 = new HashMap<>();
            for (int i = 0; i < idc_examine_value.size(); i++) {

                List<TablePageConfigVO> listtablepage =
                        getTablePageConfig("CMMain","0","0");
                List<String> dwlistdesc =
                        list.stream().map(TablePageConfigVO::getDesc).distinct().collect(Collectors.toList());
                for (int n = 0; n < dwlistdesc.size(); n++) {
                    List<TablePageConfigVO> tablelistdate = new ArrayList<>();
                    final String dwcode = dwlistdesc.get(n);
                    List<TablePageConfigVO> listdwcode =
                            list.stream().filter(ivo -> ivo.getDesc().equals(dwcode)).collect(Collectors.toList());
                    IdKey = dwlist.get(n);
                    if (!stopname.contains(IdKey)) {
                        for (int m = 0; m < listdwcode.size(); m++) {
                            TablePageConfigVO vo = new TablePageConfigVO();
                            vo.setDevicename(DeviceNameTrans(listtablepage, listdwcode.get(m).getDwcode()));
                            vo.setMax(listdwcode.get(m).getMax());
                            vo.setMin(listdwcode.get(m).getMin());
                            vo.setEtc(listdwcode.get(m).getEtc());
                            vo.setDesc(listdwcode.get(m).getDesc());
                            vo.setType(listdwcode.get(m).getType());

                            tablelistdate.add(vo);

                        }

                        mapDevice.put(dwlistdesc.get(n), tablelistdate);
                    } else {
                        log.debug("企业" + IdKey + "由于停工时常超过90天，不计入考核。");
                    }

                }
                //为提高第一张图的显示速度，将数据处理工作放在了buffer刷新中实现
                //其余的数据处理工作放在叶面中实现
                List value = new ArrayList();
                List key = new ArrayList();
                for (Map.Entry<String, List<TablePageConfigVO>> item : mapDevice.entrySet()
                ) {

                    key.add(item.getKey());
                    value.add(item.getValue());
                }

            /*dataList.clear();
            dataList.add(key);
            dataList.add(value);
            dataList.add(mapDevice);*/
                newmapDevice = mapDevice;
                log.debug("===========================装置考核相关内容成功缓存=======================");
                //第三列存放原始的未加工的类
                return newmapDevice;
            }
        } catch (Exception e) {
            log.error("=====================刷新装置考核得分时报错==============原因：");
            log.error(e.getMessage());
            // //return false;
            /* throw new Exception(e.getMessage() + "堆栈信息：" + e.getStackTrace());*/
        }
        return newmapDevice;
    }
    public List<TablePageConfigVO> getTablePageConfig(String pageencode,String xml,String redlineset) {
        List<TablePageConfigVO> listnew = new ArrayList<>();
        List<PageConfigEntity> json = pageConfigPressMapper.queryByName(pageencode + ".xml");
        if(json.size()>0) {
            if (xml.equals("1")) {
                //JSONObject jsonObject = JSONObject.fromObject(json.get(0).getContent());
                JSONObject collection = json.get(0).getContentObj();
                for (Map.Entry<String, Object> point : collection.entrySet()
                ) {
                    List<TablePageConfigVO> list1 = new ArrayList<>();
                    String pointname = point.getKey();
                    if (pointname.equals("Data")) {
                        list1 =
                                ((JSONArray) point.getValue()).toJavaList(TablePageConfigVO.class);
                        list1.stream().forEach(item -> {
                            item.setGrouptype(pointname);
                            if (item.getPointvalue() != null && (item.getPointvalue().indexOf("{") != -1 || item.getPointvalue().indexOf("(") != -1)) {
                                item.setIsformula("2");
                            } else {
                                item.setIsformula("1");
                            }
                        });

                        listnew.addAll(list1);
                    }
                }
            }
            if (redlineset.equals("1")) {
                //JSONObject jsonObject = JSONObject.fromObject(json.get(0).getContent());
                JSONObject collection = json.get(0).getContentObj();
                for (Map.Entry<String, Object> point : collection.entrySet()
                ) {
                    List<TablePageConfigVO> list1 = new ArrayList<>();
                    String pointname = point.getKey();
                    if (!pointname.equals("Sort")) {
                        list1 =
                                ((JSONArray) point.getValue()).toJavaList(TablePageConfigVO.class);
                        list1.stream().forEach(item -> {
                            item.setGrouptype(pointname);
                            if (item.getPointvalue() != null && (item.getPointvalue().indexOf("{") != -1 || item.getPointvalue().indexOf("(") != -1)) {
                                item.setIsformula("2");
                            } else {
                                item.setIsformula("1");
                            }
                        });

                        listnew.addAll(list1);
                    }
                }
            }
            if (xml.equals("0") && redlineset.equals("0")) {
                //JSONObject jsonObject = JSONObject.fromObject(json.get(0).getContent());
                JSONObject collection = json.get(0).getContentObj();
                for (Map.Entry<String, Object> point : collection.entrySet()
                ) {
                    List<TablePageConfigVO> list1 = new ArrayList<>();
                    String pointname = point.getKey();
                    if (!pointname.equals("Sort")) {
                        list1 =
                                ((JSONArray) point.getValue()).toJavaList(TablePageConfigVO.class);
                        list1.stream().forEach(item -> {
                            item.setGrouptype(pointname);
                            if (item.getPointvalue() != null && (item.getPointvalue().indexOf("{") != -1 || item.getPointvalue().indexOf("(") != -1)) {
                                item.setIsformula("2");
                            } else {
                                item.setIsformula("1");
                            }
                        });

                        listnew.addAll(list1);
                    }
                }
            }
            if (xml.equals("1")) {
                //JSONObject jsonObject = JSONObject.fromObject(json.get(0).getContent());
                JSONObject collection = json.get(0).getContentObj();
                for (Map.Entry<String, Object> point : collection.entrySet()
                ) {
                    List<TablePageConfigVO> list1 = new ArrayList<>();
                    String pointname = point.getKey();
                    if (pointname.equals("Data")) {
                        list1 =
                                ((JSONArray) point.getValue()).toJavaList(TablePageConfigVO.class);
                        list1.stream().forEach(item -> {
                            item.setGrouptype(pointname);
                            if (item.getPointvalue() != null && (item.getPointvalue().indexOf("{") != -1 || item.getPointvalue().indexOf("(") != -1)) {
                                item.setIsformula("2");
                            } else {
                                item.setIsformula("1");
                            }
                        });

                        listnew.addAll(list1);
                    }
                }
            }
            if (xml.equals("2")) {
                //JSONObject jsonObject = JSONObject.fromObject(json.get(0).getContent());
                JSONObject collection = json.get(0).getContentObj();
                for (Map.Entry<String, Object> point : collection.entrySet()
                ) {
                    List<TablePageConfigVO> list1 = new ArrayList<>();
                    String pointname = point.getKey();

                    if (!pointname.equals("Sort")) {
                        if (pointname.equals("devices")) {
                            for (Map.Entry<String, Object> index : ((JSONObject) point.getValue()).entrySet()
                            ) {
                                String pointindex = index.getKey();
                                list1 =
                                        ((JSONArray) index.getValue()).toJavaList(TablePageConfigVO.class);
                                for (int i = 0; i < ((JSONArray) index.getValue()).size(); i++) {
                                    final int aa = i;
                                    JSONArray json1 = new JSONArray();
                                    json1.add(((JSONArray) index.getValue()).get(i));
                                    list1 = json1.toJavaList(TablePageConfigVO.class);
                                    for (Map.Entry<String, Object> indexnew :
                                            ((JSONObject) ((JSONArray) index.getValue()).get(i)).entrySet()
                                    ) {
                                        list1.stream().forEach(item -> {
                                            item.setDwcode(index.getKey());
                                            item.setPointname(index.getKey());
                                            item.setGrouptype(pointindex);
                                        /*item.setClasstype(((JSONObject) ((JSONArray) index.getValue()).get(aa)).get(
                                                "class").toString());*/
                                            if (indexnew.getKey().equals("class")) {
                                                item.setClasstype(indexnew.getValue().toString());
                                            }
                                            if (item.getPointvalue() != null && (item.getPointvalue().indexOf("{") != -1 || item.getPointvalue().indexOf("(") != -1)) {
                                                item.setIsformula("2");
                                            } else {
                                                item.setIsformula("1");
                                            }
                                        });
                                    }
                                    listnew.addAll(list1);
                                }

                            }
                            listnew.addAll(list1);
                        } else {
                            TablePageConfigVO vo = new TablePageConfigVO();
                            for (Map.Entry<String, Object> device : ((JSONObject) point.getValue()).entrySet()
                            ) {

                                if (device.getKey().equals("Item")) {
                                    vo.setItem(device.getValue().toString());
                                } else {
                                    vo.setIndex(device.getValue().toString());
                                }

                            }
                            list1.add(vo);
                            listnew.addAll(list1);
                        }
                    }
                }
            }
        }
        return listnew;
    }
}
