<script>
import {forEach,isString,isEmpty,set,keys,assign,map} from "lodash";
import cmpoverviewpopovertable from "./OverviewPopoverTable.vue";

import dashboard from "@/store/dashboard";
const storeDashboard = dashboard();
export default {
  components:{
    cmpoverviewpopovertable
  },
  data(){
    return {
      listData:[],// table数据
      popoverTable:{company:"",list:[]},
      dialogVisible: false, // 控制弹出窗口显示
      dialogData: {company:"",list:[]}, // 弹出窗口数据
      externalUrl: "", // 外部网站地址
      // 外部网站地址映射表
      urlMapping: {
        // 根据不同的工艺单元配置不同的外部链接
        "常减压": process.env.VUE_APP_YCZD_URL+"/yczd-auth-web/pressure/po/rdc/po-examine-table",
        "催化": process.env.VUE_APP_YCZD_URL+"/yczd-auth-web/catalytic/po/rdc/po-examine-table",
        "焦化": process.env.VUE_APP_YCZD_URL+"/yczd-auth-web/coking/po/rdc/po-examine-table",
        "连续": process.env.VUE_APP_YCZD_URL+"/yczd-auth-web/ccr/continuousregn/po/rdc/po-examine-table",
        "半再生": process.env.VUE_APP_YCZD_URL+"/yczd-auth-web/ccr/redintegration/po/rdc/po-examine-table",
        "加裂": process.env.VUE_APP_YCZD_URL+"/yczd-auth-web/hydrotreating/hydrogenation/po/rdc/po-examine-table",
        "渣油": process.env.VUE_APP_YCZD_URL+"/yczd-auth-web/hydrotreating/residualoil/po/rdc/po-examine-table",
        "蜡油": process.env.VUE_APP_YCZD_URL+"/yczd-auth-web/hydrotreating/hydrowax/po/rdc/po-examine-table",
        "柴油": process.env.VUE_APP_YCZD_URL+"/yczd-auth-web/hydrotreating/diesel/po/rdc/po-examine-table",
        "航煤": process.env.VUE_APP_YCZD_URL+"/yczd-auth-web/hydrotreating/coaloil/po/rdc/po-examine-table",
        "催汽": process.env.VUE_APP_YCZD_URL+"/yczd-auth-web/hydrotreating/petrol/po/rdc/po-examine-table",
        "焦汽": process.env.VUE_APP_YCZD_URL+"/yczd-auth-web/hydrotreating/cokergasoline/po/rdc/po-examine-table",
        "S Zorb": process.env.VUE_APP_YCZD_URL+"/yczd-auth-web/szorb/po/rdc/po-examine-table",
        "硫磺": process.env.VUE_APP_YCZD_URL+"/yczd-auth-web/sru/po/rdc/po-examine-table",
        "制氢": process.env.VUE_APP_YCZD_URL+"/yczd-auth-web/hgu/po/rdc/po-examine-table",
        "气分": process.env.VUE_APP_YCZD_URL+"/yczd-auth-web/gfu/po/rdc/po-examine-table",
        "MTBE": process.env.VUE_APP_YCZD_URL+"/yczd-auth-web/mtbe/po/rdc/po-examine-table"
      }
    }
  },
  created(){
    this.fetchData();
  },
  methods:{
    showInfo(key) {
      // console.log("===info===",key,"====",this.tableInfo,"====",this.tableInfo[key]);
      const info = this.tableInfo[key]||{};
      this.popoverTable.company = key;
      // 重置列表
      this.popoverTable.list=[];
      map(info,(v)=>{
        this.popoverTable.list.push(v);
      })
      // console.log("====showInfo===",this.popoverTable,"====",this.tableInfo)
    },
    handleCellClick(key) {
      // 处理单元格点击事件，显示弹出窗口
      this.dialogData.company = key;

      // 根据key获取对应的外部网站地址
      this.externalUrl = this.getExternalUrl(key);

      this.dialogVisible = true;
    },
    getExternalUrl(key) {
      // 根据key获取对应的外部网站地址
      // 可以根据实际需求修改这个逻辑
      return this.urlMapping[key];
    },
    closeDialog() {
      // 关闭弹出窗口
      this.dialogVisible = false;
    },
    fetchData(){
      storeDashboard.fetchOverview().then((list)=>{
        // console.log("===a===",list);
        let listTable = [],newlist=[],listInfo=[];
        forEach(list,(item)=>{
          // console.log("===",item);
          if(!isEmpty(item)&&isString(item[0])){
            listTable.push(item);
          }else{
            listInfo.push(item)
          }
        })
        // 遍历table列表数据，这是column中prop的格式
        forEach(listTable,(item,idx)=>{
          const obj = {}
          forEach(item,(v,index)=>{
            set(obj,`t1c${index+1}`,v);
          })
          newlist.push(obj);
        })
        this.listData = newlist;

        // 处理奖牌行hover数据
        const obj={};
        // console.log("=====list info=",listInfo[0]);
        forEach(listInfo[0],(val,idx)=>{
          const k = keys(val);
          // console.log("====",k,"====",val);
          this.tableInfo = assign({},this.tableInfo,val);
          set(obj,`t1c${idx+1}`,(k&&k[0]||""))
        })
        // 汇总列表加入到table数据集里
        this.listData.push(obj);
        // console.log("====table info==",this.tableInfo);
      })
    }
  },
}
</script>
<template>
<div class="panel overview">
  <div class="head">
    <h4 class="title">比学赶帮超</h4>
  </div>
  <div class="content">
    <el-table 
      :data="listData" 
      class="table">
      <el-table-column fixed width="60" prop='project' label="项目">
        <template #default="scope">
          <img v-if="scope.$index<3" src="../images/icon/icon_flag_red.png" />
          <img v-else-if="scope.$index<6" src="../images/icon/icon_flag_yellow.png" />
          <img v-else src="../images/icon/icon_gold_tuteng.gif" />
        </template>
      </el-table-column>
      <el-table-column prop='t1c1' label="常减压" width="120">
        <template #default="scope">
          <template v-if="scope.$index===6">
            <el-popover placement="top" trigger="hover">
              <div
                slot="reference"
                style="cursor:pointer;"
                @mouseover="showInfo(scope.row.t1c1)">{{ scope.row.t1c1 }}</div>
                <cmpoverviewpopovertable :list="popoverTable.list" :company="popoverTable.company" />
            </el-popover>
          </template>
          <div v-else style="cursor:pointer;" @click="handleCellClick('常减压')">{{ scope.row.t1c1 }}</div>
        </template>
      </el-table-column>
      <el-table-column prop='t1c2' label="催化" width="120">
        <template #default="scope">
          <template v-if="scope.$index===6">
            <el-popover placement="top" trigger="hover">
              <div
                slot="reference"
                style="cursor:pointer;"
                @mouseover="showInfo(scope.row.t1c2)">{{ scope.row.t1c2 }}</div>
              <cmpoverviewpopovertable :list="popoverTable.list" :company="popoverTable.company" />
            </el-popover>
          </template>
          <div v-else style="cursor:pointer;" @click="handleCellClick('催化')">{{ scope.row.t1c2 }}</div>
        </template>
      </el-table-column>
      <el-table-column prop='t1c3' label="焦化" width="120">
        <template #default="scope">
          <template v-if="scope.$index===6">
            <el-popover placement="top" trigger="hover">
              <div
                slot="reference"
                style="cursor:pointer;"
                @mouseover="showInfo(scope.row.t1c3)">{{ scope.row.t1c3 }}</div>
              <cmpoverviewpopovertable :list="popoverTable.list" :company="popoverTable.company" />
            </el-popover>
          </template>
          <div v-else style="cursor:pointer;" @click="handleCellClick('焦化')">{{ scope.row.t1c3 }}</div>
        </template>
      </el-table-column>
      <el-table-column prop='t1c4' label="连续" width="120">
        <template #default="scope">
          <template v-if="scope.$index===6">
            <el-popover placement="top" trigger="hover">
              <div
                slot="reference"
                style="cursor:pointer;"
                @mouseover="showInfo(scope.row.t1c4)">{{ scope.row.t1c4 }}</div>
              <cmpoverviewpopovertable :list="popoverTable.list" :company="popoverTable.company" />
            </el-popover>
          </template>
          <div v-else style="cursor:pointer;" @click="handleCellClick('连续')">{{ scope.row.t1c4 }}</div>
        </template>
      </el-table-column>
      <el-table-column prop='t1c5' label="半再生" width="120">
        <template #default="scope">
          <template v-if="scope.$index===6">
            <el-popover placement="top" trigger="hover">
              <div
                slot="reference"
                style="cursor:pointer;"
                @mouseover="showInfo(scope.row.t1c5)">{{ scope.row.t1c5 }}</div>
              <cmpoverviewpopovertable :list="popoverTable.list" :company="popoverTable.company" />
            </el-popover>
          </template>
          <div v-else style="cursor:pointer;" @click="handleCellClick('半再生')">{{ scope.row.t1c5 }}</div>
        </template>
      </el-table-column>
      <el-table-column prop='t1c6' label="加裂" width="120">
        <template #default="scope">
          <template v-if="scope.$index===6">
            <el-popover placement="top" trigger="hover">
              <div
                slot="reference"
                style="cursor:pointer;"
                @mouseover="showInfo(scope.row.t1c6)">{{ scope.row.t1c6 }}</div>
              <cmpoverviewpopovertable :list="popoverTable.list" :company="popoverTable.company" />
            </el-popover>
          </template>
          <div v-else style="cursor:pointer;" @click="handleCellClick('加裂')">{{ scope.row.t1c6 }}</div>
        </template>
      </el-table-column>
      <el-table-column prop='t1c7' label="渣油" width="120">
        <template #default="scope">
          <template v-if="scope.$index===6">
            <el-popover placement="top" trigger="hover">
              <div
                slot="reference"
                style="cursor:pointer;"
                @mouseover="showInfo(scope.row.t1c7)">{{ scope.row.t1c7 }}</div>
              <cmpoverviewpopovertable :list="popoverTable.list" :company="popoverTable.company" />
            </el-popover>
          </template>
          <div v-else style="cursor:pointer;" @click="handleCellClick('渣油')">{{ scope.row.t1c7 }}</div>
        </template>
      </el-table-column>
      <el-table-column prop='t1c8' label="蜡油" width="120">
        <template #default="scope">
          <template v-if="scope.$index===6">
            <el-popover placement="top" trigger="hover">
              <div
                slot="reference"
                style="cursor:pointer;"
                @mouseover="showInfo(scope.row.t1c8)">{{ scope.row.t1c8 }}</div>
              <cmpoverviewpopovertable :list="popoverTable.list" :company="popoverTable.company" />
            </el-popover>
          </template>
          <div v-else style="cursor:pointer;" @click="handleCellClick('蜡油')">{{ scope.row.t1c8 }}</div>
        </template>
      </el-table-column>
      <el-table-column prop='t1c9' label="柴油" width="120">
        <template #default="scope">
          <template v-if="scope.$index===6">
            <el-popover placement="top" trigger="hover">
              <div
                slot="reference"
                style="cursor:pointer;"
                @mouseover="showInfo(scope.row.t1c9)">{{ scope.row.t1c9 }}</div>
              <cmpoverviewpopovertable :list="popoverTable.list" :company="popoverTable.company" />
            </el-popover>
          </template>
          <div v-else style="cursor:pointer;" @click="handleCellClick('柴油')">{{ scope.row.t1c9 }}</div>
        </template>
      </el-table-column>
      <el-table-column prop='t1c10' label="航煤" width="120">
        <template #default="scope">
          <template v-if="scope.$index===6">
            <el-popover placement="top" trigger="hover">
              <div
                slot="reference"
                style="cursor:pointer;"
                @mouseover="showInfo(scope.row.t1c10)">{{ scope.row.t1c10 }}</div>
              <cmpoverviewpopovertable :list="popoverTable.list" :company="popoverTable.company" />
            </el-popover>
          </template>
          <div v-else style="cursor:pointer;" @click="handleCellClick('航煤')">{{ scope.row.t1c10 }}</div>
        </template>
      </el-table-column>
      <el-table-column prop='t1c11' label="催汽" width="120">
        <template #default="scope">
          <template v-if="scope.$index===6">
            <el-popover placement="top" trigger="hover">
              <div
                slot="reference"
                style="cursor:pointer;"
                @mouseover="showInfo(scope.row.t1c11)">{{ scope.row.t1c11 }}</div>
              <cmpoverviewpopovertable :list="popoverTable.list" :company="popoverTable.company" />
            </el-popover>
          </template>
          <div v-else style="cursor:pointer;" @click="handleCellClick('催汽')">{{ scope.row.t1c11 }}</div>
        </template>
      </el-table-column>
      <el-table-column prop='t1c12' label="焦汽" width="120">
        <template #default="scope">
          <template v-if="scope.$index===6">
            <el-popover placement="top" trigger="hover">
              <div
                slot="reference"
                style="cursor:pointer;"
                @mouseover="showInfo(scope.row.t1c12)">{{ scope.row.t1c12 }}</div>
              <cmpoverviewpopovertable :list="popoverTable.list" :company="popoverTable.company" />
            </el-popover>
          </template>
          <div v-else style="cursor:pointer;" @click="handleCellClick('焦汽')">{{ scope.row.t1c12 }}</div>
        </template>
      </el-table-column>
      <el-table-column prop='t1c13' label="S Zorb" width="120">
        <template #default="scope">
          <template v-if="scope.$index===6">
            <el-popover placement="top" trigger="hover">
              <div
                slot="reference"
                style="cursor:pointer;"
                @mouseover="showInfo(scope.row.t1c13)">{{ scope.row.t1c13 }}</div>
              <cmpoverviewpopovertable :list="popoverTable.list" :company="popoverTable.company" />
            </el-popover>
          </template>
          <div v-else style="cursor:pointer;" @click="handleCellClick('S Zorb')">{{ scope.row.t1c13 }}</div>
        </template>
      </el-table-column>
      <el-table-column prop='t1c14' label="硫磺" width="120">
        <template #default="scope">
          <template v-if="scope.$index===6">
            <el-popover placement="top" trigger="hover">
              <div
                slot="reference"
                style="cursor:pointer;"
                @mouseover="showInfo(scope.row.t1c14)">{{ scope.row.t1c14 }}</div>
              <cmpoverviewpopovertable :list="popoverTable.list" :company="popoverTable.company" />
            </el-popover>
          </template>
          <div v-else style="cursor:pointer;" @click="handleCellClick('硫磺')">{{ scope.row.t1c14 }}</div>
        </template>
      </el-table-column>
      <el-table-column prop='t1c15' label="制氢" width="120">
        <template #default="scope">
          <template v-if="scope.$index===6">
            <el-popover placement="top" trigger="hover">
              <div
                slot="reference"
                style="cursor:pointer;"
                @mouseover="showInfo(scope.row.t1c15)">{{ scope.row.t1c15 }}</div>
              <cmpoverviewpopovertable :list="popoverTable.list" :company="popoverTable.company" />
            </el-popover>
          </template>
          <div v-else style="cursor:pointer;" @click="handleCellClick('制氢')">{{ scope.row.t1c15 }}</div>
        </template>
      </el-table-column>
      <el-table-column prop='t1c16' label="气分" width="120">
        <template #default="scope">
          <template v-if="scope.$index===6">
            <el-popover placement="top" trigger="hover">
              <div
                slot="reference"
                style="cursor:pointer;"
                @mouseover="showInfo(scope.row.t1c16)">{{ scope.row.t1c16 }}</div>
              <cmpoverviewpopovertable :list="popoverTable.list" :company="popoverTable.company" />
            </el-popover>
          </template>
          <div v-else style="cursor:pointer;" @click="handleCellClick('气分')">{{ scope.row.t1c16 }}</div>
        </template>
      </el-table-column>
      <el-table-column prop='t1c17' label="MTBE" width="120">
        <template #default="scope">
          <template v-if="scope.$index===6">
            <el-popover placement="top" trigger="hover">
              <div
                slot="reference"
                style="cursor:pointer;"
                @mouseover="showInfo('refT1c1Total',scope.row.t1c17)">{{ scope.row.t1c17 }}</div>
              <cmpoverviewpopovertable :list="popoverTable.list" :company="popoverTable.company" />
            </el-popover>
          </template>
          <div v-else style="cursor:pointer;" @click="handleCellClick('MTBE')">{{ scope.row.t1c17 }}</div>
        </template>
      </el-table-column>
    </el-table>
  </div>

  <!-- 弹出窗口 -->
  <el-dialog
    :title="`${dialogData.company}`"
    :visible.sync="dialogVisible"
    width="90%"
    height="80%"
    :modal="false"
    @close="closeDialog">
    <div class="iframe-container">
      <iframe
        v-if="externalUrl"
        :src="externalUrl"
        class="external-iframe"
        frameborder="0">
      </iframe>
    </div>
  </el-dialog>
</div>
</template>
<style lang="less" scoped>
@import "../style.less";

.iframe-container {
  width: 100%;
  height: 70vh;
  min-height: 500px;

  .external-iframe {
    width: 100%;
    height: 100%;
    border: none;
  }

  .no-url-message {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    font-size: 16px;
    color: #666;
    background-color: #f5f5f5;
    border: 1px dashed #ddd;
  }
}

// 弹出窗口样式调整
:deep(.el-dialog) {
  .el-dialog__body {
    padding: 10px 20px;
  }
}
</style>