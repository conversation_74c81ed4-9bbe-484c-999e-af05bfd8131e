package com.pcitc.yczd.coke.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.pcitc.yczd.coke.entity.PageConfigEntity;
import com.pcitc.yczd.coke.mapper.*;
import com.pcitc.yczd.coke.service.*;
import com.pcitc.yczd.coke.utils.YczdCataUtils;
import com.pcitc.yczd.coke.vo.*;
import com.pcitc.yczd.common.utils.DateConvertUtils;
import com.pcitc.yczd.common.utils.ResponseCode;
import com.pcitc.yczd.common.utils.ResponseUtil;
import dm.jdbc.driver.DmdbBlob;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.sql.rowset.serial.SerialBlob;
import java.io.ObjectInputStream;
import java.sql.Blob;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: yczd-springboot
 * @description: 图形公共接口
 * @author: pengchong.li
 * @createTime: 2024-05-07 16:25
 **/
@Service
public class CokeChartService implements ICokeChartService {
    private static Logger logger =
            LogManager.getLogger(CokeChartService.class.getName());
    @Autowired
    private IRTDBCokeService irtdbCokeservice;
    @Autowired
    private TSavedCachedCokeMapper tsavedcachedcokemapper;
    @Autowired
    private ProductyieldModelhistoryCokeMapper productyieldmodelhistorycokemapper;
    @Autowired
    private TpointDataHisCokeMapper tpointdatahiscokemapper;
    @Autowired
    private TIndexDataCokeMapper tindexdatacokemapper;
    @Autowired
    private IExamineServiceCoke iExamineServiceCoke;
    @Autowired
    private GantWaterFallCokeMapper gantwaterfallcokemapper;
    @Autowired
    private AbnormalFilterCokeMapper abnormalfiltercokemapper;
    @Autowired
    private ICokeStableRatePicService iCokeStableRatePicService;
    @Autowired
    private ICokeStableRatePic2Service iCokeStableRatePic2Service;
    @Autowired
    private ICokeChartPopService iCokeChartpopservice;
    @Autowired
    private PageConfigCokeMapper pageConfigMapper;
    @Autowired
    private IPageConfigCokeService iPageConfigCokeService;
    @Autowired
    private ProductDistributionMapper productDistributionMapper;
    @Autowired
    private TchartConfigCokeMapper tchartConfigCokeMapper;
    @Autowired
    private GantWaterFallCokeMapper gantWaterFallCokeMapper;
    @Autowired
    private CokeSortServiceUtils cokeSortServiceUtils;


    @Override
    public Object GetCokeChart(String[] group, String[] charttype, String pageencode, String starttime,
                           String endtime, String ismacd, String ismacdset, String redlineset, Integer xsws) throws Exception {

        try {
            List<TablePageConfigVO> list =getTablePageConfig(pageencode,"0","0");
            /*List<TablePageConfigVO> list =
                    tablepageconfigservice.GetTablePageConfigList(pageencode);*/

            //String[] groupcode = new String[] { "原料UOP K因子", "轻收" };
            String[] groupcode = group;
            List<yczdParamVo> pointlist = new ArrayList<>();

            List<String> type = list.stream().map(TablePageConfigVO::getGrouptype).distinct().collect(Collectors.toList());
            String[] stringdevice = null;
            String[] stringcode = null;
            for (int m = 0; m < type.size(); m++) {
                final String typecode = type.get(m);

                List<TablePageConfigVO> listvo =
                        list.stream().filter(ivo -> ivo.getGrouptype().equals(typecode)).collect(Collectors.toList());
                String[] stringArray = new String[listvo.size()];
                String[] valueArray = new String[listvo.size()];
                String[] codeArray = new String[listvo.size()];
                for (int i = 0; i < listvo.size(); i++) {
          /* yczdParamVo vo=new yczdParamVo();
           vo.setDeviceCode(list.get(i).getPointname());*/
                    Double pointvalue = 0.00;
                    if (!listvo.get(i).getPointvalue().equals("") && listvo.get(i).getPointvalue() != null) {
                        if (listvo.get(i) != null && listvo.get(i).getPointvalue() != "") {

                            pointvalue = Double.parseDouble(irtdbCokeservice.calculateCoke(listvo.get(i).getPointvalue(),
                                    Integer.parseInt(listvo.get(i).getIsformula()),
                                    starttime,
                                    endtime));

                        } else {
                            pointvalue = 0.0;
                        }
                    } else {
                        pointvalue = 0.0;
                    }
                    final String poincode = listvo.get(i).getPointvalue();
                    List<TIndexDataVO> indexvolist = tindexdatacokemapper.GetIndexdata(poincode);

                    if (indexvolist.size() > 0) {
                        if (indexvolist.get(0).getValid_data_top_limit() != null && indexvolist.get(0).getValid_data_bottom_limit() != null) {
                            if (pointvalue <= indexvolist.get(0).getValid_data_top_limit() && pointvalue >= indexvolist.get(0).getValid_data_bottom_limit()) {
                                if (listvo.size() > 0) {
                                    if (xsws != -1) {
                                        String value = getXsValue(pointvalue, xsws);
                                        valueArray[i] = value;
                                    }
                                    String device = listvo.get(i).getPointname();
                                    stringArray[i] = device;
                                    codeArray[i]=listvo.get(i).getPointvalue();

                                }
                            }
                        } else {
                            if (listvo.size() > 0) {
                                if (xsws != -1) {
                                    String value = getXsValue(pointvalue, xsws);
                                    valueArray[i] = value;
                                }
                                String device = listvo.get(i).getPointname();
                                stringArray[i] = device;
                                codeArray[i]=listvo.get(i).getPointvalue();

                            }
                        }
                    } else {
                        if (xsws != -1) {
                            String value = getXsValue(pointvalue, xsws);
                            valueArray[i] = value;
                        }
                        String device = listvo.get(i).getPointname();
                        stringArray[i] = device;
                        codeArray[i]=listvo.get(i).getPointvalue();

                    }
                }
                yczdParamVo vo = new yczdParamVo();
                if (group.length != type.size()) {
                    vo.setParameterName(group[0]);
                    vo.setChartType(charttype[0]);
                } else {
                    vo.setParameterName(group[m]);
                    vo.setChartType(charttype[m]);
                }
           /*vo.setJKCS(JKCS);
           vo.setJKCSJC(JKCSJC);*/
                /*vo.setDeviceSum(stringArray);*/
                vo.setParameterValue(valueArray);
                //vo.setDevicecode(codeArray);
                if (ismacd.equals("1")) {
                    vo.setMacdValue(getCokemacd(valueArray));
                }
                if (ismacdset.equals("1")) {
                    vo.setMacdValue(getCokemacdset(redlineset, valueArray));
                }
                pointlist.add(vo);
                if (m == 0) {
                    stringdevice = stringArray;
                    stringcode=codeArray;
                }
            }
            yczdParamNewVo newVo = new yczdParamNewVo();
            /*if (starttime.isEmpty() && endtime.isEmpty()) {
                starttime = DateTime.now().toString("yyyy-MM-dd 0:00:00");
                endtime = DateTime.now().toString("yyyy-MM-dd HH:mm:ss");
            }*/
            newVo.setQuerytime("数据更新时间:" + starttime + " 至 " + endtime);
            newVo.setDeviceSum(stringdevice);
            newVo.setDeviceCode(stringcode);
            newVo.setYczdParamListVo(pointlist);
            return ResponseUtil.success(newVo);
        } catch (Exception e) {
            // TODO: handle exception
            //log.error(e.getMessage());
            return ResponseUtil.error(ResponseCode.ERROR_CODE, e.getMessage());
        }
        //return pointlist;
    }

    /**
     * 获取差值的图形数据
     *
     * @param group
     * @param charttype
     * @param pageencode
     * @param starttime
     * @param endtime
     * @param ismacd
     * @return
     * @throws Exception
     */
    @Override
    public Object GetCokeDifferenceChart(String[] group, String[] charttype, String pageencode, String starttime,
                                     String endtime, String ismacd, String ismacdset, String redlineset, Integer xsws) throws Exception {

        try {
            List<TablePageConfigVO> list =getTablePageConfig(pageencode,"0","0");
            String starttimes = "";
            String starttimee = "";
            String endtimes = "";
            String endtimee = "";
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM");
            if (starttime.isEmpty() && endtime.isEmpty()) {

                starttimes =
                        simpleDateFormat.format(DateConvertUtils.stringToDate(DateConvertUtils.addMonth(new Date(),
                                -1))) + "-01";
                starttimee =
                        DateConvertUtils.add(DateConvertUtils.stringToDate(DateConvertUtils.addMonth(new Date(), -1)), -1);

                endtimes = simpleDateFormat.format(DateConvertUtils.getFirstDayOfMonth(new Date())) + "-01";
                endtimee = DateConvertUtils.add(new Date(), -1);

            } else {
                starttimes = starttime.substring(0, 7) + "-01";
                starttimee =
                        DateConvertUtils.add(DateConvertUtils.stringToDate(starttime), -1);

                endtimes = endtime.substring(0, 7) + "-01";
                endtimee = DateConvertUtils.add(DateConvertUtils.stringToDate(endtime), -1);
            }
            //String[] groupcode = new String[] { "原料UOP K因子", "轻收" };
            String[] groupcode = group;
            String pointname = "";
            List<yczdParamVo> pointlist = new ArrayList<>();
            String[] stringdevice = null;

            /*List<PointConfTableVO> pointConfTableVOList =
                    ipointconftableservice.GetPointConfTableList();*/
            /*for (int i = 0; i < list.size(); i++) {
                final String pointconf = list.get(i).getPointvalue();
                *//*List<PointConfTableVO> pointvolist =
                        pointConfTableVOList.stream().filter(ivo -> ivo.getSystemponitcode().equals(pointconf)).collect(Collectors.toList());
                if (pointvolist.size() > 0) {
                    pointname += "'" + pointvolist.get(0).getEnterprisepointcode() + "',";
                }*//*
            }*/
            /*starttimes="2018-04-01";
            starttimee="2018-04-20";
            endtimes="2018-05-01";
            endtimee="2018-05-20";*/
            //pointname=iPageConfigCokeService.getPointcode(pointconf);
            List<TpointDataHisVO> listvo =
                    tpointdatahiscokemapper.GetDifferenceTpointdataHis(org.apache.commons.lang.StringUtils.removeEnd(pointname, ","),
                            starttimes, endtimes, starttimee, endtimee);
            String[] stringArray = new String[list.size()];
            String[] valueArray = new String[list.size()];
            for (int m = 0; m < list.size(); m++) {
                final String pointde = list.get(m).getPointvalue();
                Double value1 = 0.0;
                Double value2 = 0.0;
                /*List<PointConfTableVO> pointvolist =
                        pointConfTableVOList.stream().filter(ivo -> ivo.getSystemponitcode().equals(pointde)).collect(Collectors.toList());*/
                String pointcode=iPageConfigCokeService.getPointcode(pointde);
                if (!pointcode.equals("")) {
                    final String poinconf = pointcode;
                    List<TpointDataHisVO> hisvo =
                            listvo.stream().filter(ivo -> ivo.getPointname().equals(poinconf)).collect(Collectors.toList());
                    if (listvo.size() > 0) {
                        for (int n = 0; n < hisvo.size(); n++) {
                            if (hisvo.get(n).getTs().equals(DateTime.parse(starttimes).toString("yyyy-MM"))) {
                                value1 = listvo.get(0).getVa();
                            }
                            if (hisvo.get(n).getTs().equals(DateTime.parse(endtimes).toString("yyyy-MM"))) {
                                value2 = listvo.get(1).getVa();
                            }
                        }
                    }
                    List<TIndexDataVO> indexvolist = tindexdatacokemapper.GetIndexdata(pointde);

                    if (indexvolist.size() > 0) {
                        if (indexvolist.get(0).getValid_data_top_limit() != null && indexvolist.get(0).getValid_data_bottom_limit() != null) {
                            if (value2 <= indexvolist.get(0).getValid_data_top_limit() && value2 >= indexvolist.get(0).getValid_data_bottom_limit() && value1 <= indexvolist.get(0).getValid_data_top_limit() && value1 >= indexvolist.get(0).getValid_data_bottom_limit()) {
                                if (list.size() > 0) {
                                    if (xsws != -1) {
                                        String value = getXsValue((value2 - value1), xsws);
                                        valueArray[m] = value;
                                    }
                                    if (m == 0) {
                                        String device = list.get(m).getPointname();
                                        stringArray[m] = device;
                                    }
                                }
                            }
                        } else {
                            if (list.size() > 0) {
                                if (xsws != -1) {
                                    String value = getXsValue((value2 - value1), xsws);
                                    valueArray[m] = value;
                                }
                                String device = list.get(m).getPointname();
                                stringArray[m] = device;
                            }
                        }
                    } else {
                        if (list.size() > 0) {
                            if (xsws != -1) {
                                String value = getXsValue((value2 - value1), xsws);
                                valueArray[m] = value;
                            }
                            String device = list.get(m).getPointname();
                            stringArray[m] = device;

                        }
                    }
                } else {
                    if (list.size() > 0) {
                        if (xsws != -1) {
                            String value = getXsValue((value2 - value1), xsws);
                            valueArray[m] = value;
                        }
                        String device = list.get(m).getPointname();
                        stringArray[m] = device;

                    }
                }
            }
            yczdParamVo vo = new yczdParamVo();
            if (group.length != list.size()) {
                vo.setParameterName(group[0]);
                vo.setChartType(charttype[0]);
            } else {
                vo.setParameterName(group[0]);
                vo.setChartType(charttype[0]);
            }
           /*vo.setJKCS(JKCS);
           vo.setJKCSJC(JKCSJC);*/
            /*vo.setDeviceSum(stringArray);*/
            vo.setParameterValue(valueArray);
            if (ismacd.equals("1")) {
                vo.setMacdValue(getCokemacd(valueArray));
            }
            if (ismacdset.equals("1")) {
                vo.setMacdValue(getCokemacdset(redlineset, valueArray));
            }
            pointlist.add(vo);
            stringdevice = stringArray;

            yczdParamNewVo newVo = new yczdParamNewVo();
            if (starttime.isEmpty() && endtime.isEmpty()) {
                starttime = DateTime.parse(starttimes).toString("yyyy-MM");
                endtime = DateTime.parse(endtimes).toString("yyyy-MM");
            } else {
                starttime = starttime.substring(0, 7);
                endtime = endtime.substring(0, 7);
            }
            newVo.setQuerytime("对比时间:" + endtime + " 至 " + starttime);
            newVo.setDeviceSum(stringdevice);
            newVo.setYczdParamListVo(pointlist);
            return ResponseUtil.success(newVo);
        } catch (Exception e) {
            // TODO: handle exception
            //log.error(e.getMessage());
            return ResponseUtil.error(ResponseCode.ERROR_CODE, e.getMessage());
        }
        //return pointlist;
    }

    /**
     * 是否是平均数
     *
     * @param csvalue
     * @return
     */
    @Override
    public Double[] getCokemacd(String[] csvalue) {
        Double[] doumacd = new Double[csvalue.length];
        Double sumvalue = 0.0;
        Double maxvalue = 0.0;
        Double minvalue = 1000000.0;
        Integer flagcount = 0;
        for (int i = 0; i < csvalue.length; i++) {
            if (maxvalue < Double.parseDouble(csvalue[i])) {
                maxvalue = Double.parseDouble(csvalue[i]);
            }
            if (minvalue > Double.parseDouble(csvalue[i])) {
                minvalue = Double.parseDouble(csvalue[i]);
            }
            sumvalue += Double.parseDouble(csvalue[i]);
            flagcount++;
        }
        DecimalFormat df = new DecimalFormat("#.######");
        sumvalue = Double.parseDouble(df.format(sumvalue / flagcount));
        for (int j = 0; j < csvalue.length; j++) {
            doumacd[j] = sumvalue;
        }
        return doumacd;
    }

    /**
     * 是否是设置值
     *
     * @param redlineset
     * @param csvalue
     * @return
     */
    @Override
    public Double[] getCokemacdset(String redlineset, String[] csvalue) {
        Double[] doumacd = new Double[csvalue.length];
        String pageencode = "RedLineSet";
        List<TablePageConfigVO> list =getTablePageConfig(pageencode,"0","1");
        for (int j = 0; j < csvalue.length; j++) {
            for (int i = 0; i < list.size(); i++) {
                if (list.get(i).getType().equals(redlineset)) {
                    doumacd[j] = Double.parseDouble(list.get(i).getSetvalue());
                }
            }

        }
        return doumacd;
    }

    @Override
    public Object GetCokeProductYieldChart(String[] group, String[] charttype, String starttime,
                                       String endtime, String ismacd) throws Exception {

        try {
            String Catalytic_DryGas = "Catalytic_DryGas";//干气+焦碳
            String Catalytic_SlurryOil = "Catalytic_SlurryOil";//油浆
            String Catalytic_DieselOil = "Catalytic_DieselOil";//柴油
            String Catalytic_Petrol = "Catalytic_Petrol";//汽油
            String Catalytic_LiquefiedGas = "Catalytic_LiquefiedGas";//液化气
            String keyname = "FCCColum";
            TSavedCachedVO cachedvo = tsavedcachedcokemapper.GetTSavedCachedList(keyname);
            yczdParamNewVo newVo = new yczdParamNewVo();
            String[] stringdevice = null;
            List<yczdParamVo> pointlist = new ArrayList<>();
            if (starttime.isEmpty() && endtime.isEmpty()) {
                starttime = DateTime.now().toString("yyyy-MM-dd 0:00:00");
                endtime = DateTime.now().toString("yyyy-MM-dd HH:mm:ss");
            }
            List<ProductyieldModelhistoryVO> listvonew = new ArrayList<>();
            if (cachedvo.getCachecontent() != null) {
                Blob blob = (Blob) cachedvo.getCachecontent();
                /*Properties properties = blobToProperties(blob);*/
            /*blob.getBinaryStream();
            InputStream in = (InputStream) cachedvo.getCachecontent();*/
                /*BufferedReader r=new BufferedReader(new InputStreamReader(in, "UTF-8"));*/
                byte[] objectBytes = blob.getBytes(1, (int) ((DmdbBlob) blob).length);
                // 使用字节数组创建一个新的Blob
                Blob newBlob = new SerialBlob(objectBytes);

                // 反序列化得到对象
                ObjectInputStream ois = new ObjectInputStream(newBlob.getBinaryStream());
                Object result = Object.class.cast(ois.readObject());
            } else {
                /*starttime = "2018-05-01";
                endtime = "2024-05-14";*/
                List<ProductyieldModelhistoryVO> listvo = productyieldmodelhistorycokemapper.getProductyieldModelhistory(starttime, endtime);
                String[] stringArray = new String[listvo.size()];
                String[] valueArraycyyc = new String[listvo.size()];
                String[] valueArraycysj = new String[listvo.size()];
                String[] valueArraygqjtyc = new String[listvo.size()];
                String[] valueArraygqjtsj = new String[listvo.size()];
                String[] valueArrayqyyc = new String[listvo.size()];
                String[] valueArrayqysj = new String[listvo.size()];
                String[] valueArrayyhqyc = new String[listvo.size()];
                String[] valueArrayyhqsj = new String[listvo.size()];
                String[] valueArrayyjyc = new String[listvo.size()];
                String[] valueArrayyjsj = new String[listvo.size()];
                for (int i = 0; i < listvo.size(); i++) {
                    stringArray[i] = listvo.get(i).getDevicename();
                    ProductyieldModelhistoryVO vo = new ProductyieldModelhistoryVO();
                    /*vo.setCy_value(listvo.get(i).getCy_value());
                    vo.setCy_value_sj(listvo.get(i).getCy_value_sj());
                    vo.setGqjt_value(listvo.get(i).getGqjt_value());
                    vo.setGqjt_value_sj(listvo.get(i).getGqjt_value_sj());
                    vo.setQy_value(listvo.get(i).getQy_value());
                    vo.setQy_value_sj(listvo.get(i).getQy_value_sj());
                    vo.setYhq_value(listvo.get(i).getYhq_value());
                    vo.setYhq_value_sj(listvo.get(i).getYhq_value_sj());
                    vo.setYj_value(listvo.get(i).getYj_value());
                    vo.setYj_value_sj(listvo.get(i).getYj_value_sj());*/
                    valueArraycyyc[i] = listvo.get(i).getCy_value();
                    valueArraycysj[i] = listvo.get(i).getCy_value_sj();
                    valueArraygqjtyc[i] =listvo.get(i).getGqjt_value();
                    valueArraygqjtsj[i] = listvo.get(i).getGqjt_value_sj();
                    valueArrayqyyc[i] = listvo.get(i).getQy_value();
                    valueArrayqysj[i] = listvo.get(i).getQy_value_sj();
                    valueArrayyhqyc[i] = listvo.get(i).getYhq_value();
                    valueArrayyhqsj[i] = listvo.get(i).getYhq_value_sj();
                    valueArrayyjyc[i] = listvo.get(i).getYj_value();
                    valueArrayyjsj[i] = listvo.get(i).getYj_value_sj();
                    listvonew.add(vo);
                }
                stringdevice = stringArray;
                yczdParamVo prvoyjyc = new yczdParamVo();
                prvoyjyc.setParameterName("油浆预测");
                prvoyjyc.setParameterValue(valueArrayyjyc);
                pointlist.add(prvoyjyc);
                yczdParamVo prvoyjsj = new yczdParamVo();
                prvoyjsj.setParameterName("油浆实际");
                prvoyjsj.setParameterValue(valueArrayyjsj);
                pointlist.add(prvoyjsj);
                yczdParamVo prvocyyc = new yczdParamVo();
                prvocyyc.setParameterName("柴油预测");
                prvocyyc.setParameterValue(valueArraycyyc);
                pointlist.add(prvocyyc);
                yczdParamVo prvocysj = new yczdParamVo();
                prvocysj.setParameterName("柴油实际");
                prvocysj.setParameterValue(valueArraycysj);
                pointlist.add(prvocysj);
                yczdParamVo prvoqyyc = new yczdParamVo();
                prvoqyyc.setParameterName("汽油预测");
                prvoqyyc.setParameterValue(valueArrayqyyc);
                pointlist.add(prvoqyyc);
                yczdParamVo prvoqysj = new yczdParamVo();
                prvoqysj.setParameterName("汽油实际");
                prvoqysj.setParameterValue(valueArrayqysj);
                pointlist.add(prvoqysj);
                yczdParamVo prvoyhqyc = new yczdParamVo();
                prvoyhqyc.setParameterName("液化气预测");
                prvoyhqyc.setParameterValue(valueArrayyhqyc);
                pointlist.add(prvoyhqyc);
                yczdParamVo prvoyhqsj = new yczdParamVo();
                prvoyhqsj.setParameterName("液化气实际");
                prvoyhqsj.setParameterValue(valueArrayyhqsj);
                pointlist.add(prvoyhqsj);
                yczdParamVo prvogqjtyc = new yczdParamVo();
                prvogqjtyc.setParameterName("干气+焦炭预测");
                prvogqjtyc.setParameterValue(valueArraygqjtyc);
                pointlist.add(prvogqjtyc);
                yczdParamVo prvogqjtsj = new yczdParamVo();
                prvogqjtsj.setParameterName("干气+焦炭实际");
                prvogqjtsj.setParameterValue(valueArraygqjtsj);

                pointlist.add(prvogqjtsj);
/*listvo.stream().forEach(o->{
    stringdevice[o]=o.getDevicename();
    ProductyieldModelhistoryVO vo=new ProductyieldModelhistoryVO();
    vo.setCy_value(o.getCy_value());
    vo.setCy_value_sj(o.getCy_value_sj());
    vo.setGqjt_value(o.getGqjt_value());
    vo.setGqjt_value_sj(o.getGqjt_value_sj());
    vo.setQy_value(o.getQy_value());
    vo.setQy_value_sj(o.getQy_value_sj());
    vo.setYhq_value(o.getYhq_value());
    vo.setYhq_value_sj(o.getYhq_value_sj());
    vo.setYj_value(o.getYj_value());
    vo.setYj_value_sj(o.getYj_value_sj());
    listvonew.add(vo);
});*/
            }
            /*Gson gson = new Gson();
            String aa = new String(blob.getBytes(1, (int) ((DmdbBlob) blob).length), "UTF-8");
            gson.fromJson(aa, Object.class);
*/


            //String[] groupcode = new String[] { "原料UOP K因子", "轻收" };
            /*String[] groupcode = group;


            List<String> type = list.stream().map(TablePageConfigVO::getGrouptype).distinct().collect(Collectors.toList());

            for (int m = 0; m < type.size(); m++) {
                final String typecode = type.get(m);

                List<TablePageConfigVO> listvo =
                        list.stream().filter(ivo -> ivo.getGrouptype().equals(typecode)).collect(Collectors.toList());
                String[] stringArray = new String[listvo.size()];
                Double[] valueArray = new Double[listvo.size()];
                for (int i = 0; i < listvo.size(); i++) {
          *//* yczdParamVo vo=new yczdParamVo();
           vo.setDeviceCode(list.get(i).getPointname());*//*
                    Double pointvalue = 0.00;
                    if (!listvo.get(i).getPointvalue().equals("") && listvo.get(i).getPointvalue() != null) {
                        if (listvo.get(i)!=null&&listvo.get(i).getPointvalue()!="") {

                            pointvalue = Double.parseDouble(rtdbbaseservice.Calculate(listvo.get(i).getPointvalue(),
                                    Integer.parseInt(listvo.get(i).getIsformula()),
                                    starttime,
                                    endtime));

                        }else {
                            pointvalue=0.0;
                        }
                    } else {
                        pointvalue = 0.0;
                    }
                    Double value = pointvalue;
                    valueArray[i] = value;

                    String device = listvo.get(i).getPointname();
                    stringArray[i] = device;
                }
                yczdParamVo vo = new yczdParamVo();
                vo.setParameterName(group[m]);
                vo.setChartType(charttype[m]);
                vo.setParameterValue(valueArray);
                if (ismacd.equals("1")) {
                    vo.setMacdValue(getmacd(valueArray));
                }
                pointlist.add(vo);
                stringdevice=stringArray;
            }*/

            newVo.setQuerytime("数据更新时间:" + starttime + " 至 " + endtime);
            newVo.setDeviceSum(stringdevice);
            newVo.setYczdParamListVo(pointlist);
            return ResponseUtil.success(newVo);
        } catch (Exception e) {
            // TODO: handle exception
            //log.error(e.getMessage());
            return ResponseUtil.error(ResponseCode.ERROR_CODE, e.getMessage());
        }
        //return pointlist;
    }

    /*public static Properties blobToProperties(Blob blob) throws Exception {
        Properties  properties = new Properties();
        InputStream inputStream = null;
        try {
            inputStream = blob.getBinaryStream();
            properties.load(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
        } finally {
            if (inputStream != null) {
                inputStream.close();
            }
        }
        return properties;
    }*/

    /**
     * 值保留的小数位数
     *
     * @param value
     * @param number
     * @return
     */
    public String getXsValue(Double value, Integer number) {
        double f=value;
        if(number==0){
            DecimalFormat df=new DecimalFormat("0");
        /*BigDecimal bg = new BigDecimal(value);
        Double f1 = bg.setScale(number, BigDecimal.ROUND_HALF_UP).doubleValue();*/
            return df.format(f);
        }
        if(number==1){
            DecimalFormat df=new DecimalFormat("0.0");
        /*BigDecimal bg = new BigDecimal(value);
        Double f1 = bg.setScale(number, BigDecimal.ROUND_HALF_UP).doubleValue();*/
            return df.format(f);
        }
        if(number==2){
            DecimalFormat df=new DecimalFormat("0.00");
        /*BigDecimal bg = new BigDecimal(value);
        Double f1 = bg.setScale(number, BigDecimal.ROUND_HALF_UP).doubleValue();*/
            return df.format(f);
        }
        if(number==3){
            DecimalFormat df=new DecimalFormat("0.000");
        /*BigDecimal bg = new BigDecimal(value);
        Double f1 = bg.setScale(number, BigDecimal.ROUND_HALF_UP).doubleValue();*/
            return df.format(f);
        }
        if(number==4){
            DecimalFormat df=new DecimalFormat("0.0000");
        /*BigDecimal bg = new BigDecimal(value);
        Double f1 = bg.setScale(number, BigDecimal.ROUND_HALF_UP).doubleValue();*/
            return df.format(f);
        }
        if(number==5){
            DecimalFormat df=new DecimalFormat("0.00000");
        /*BigDecimal bg = new BigDecimal(value);
        Double f1 = bg.setScale(number, BigDecimal.ROUND_HALF_UP).doubleValue();*/
            return df.format(f);
        }
        if(number==6){
            DecimalFormat df=new DecimalFormat("0.000000");
        /*BigDecimal bg = new BigDecimal(value);
        Double f1 = bg.setScale(number, BigDecimal.ROUND_HALF_UP).doubleValue();*/
            return df.format(f);
        }
        return null;
    }

    /**
     * 装置竞赛排名的图表数据
     *
     * @param group
     * @param charttype
     * @param pageencode
     * @param starttime
     * @param endtime
     * @param ismacd
     * @param ismacdset
     * @param redlineset
     * @param xsws
     * @return
     * @throws Exception
     */
    @Override
    public Object GetCokeExamineConfigChart(String[] group, String[] charttype, String pageencode, String starttime,
                                        String endtime, String ismacd, String ismacdset, String redlineset, Integer xsws) throws Exception {

        try {
            if (starttime == "" && endtime == "") {
                /*starttime = DateTime.now().getYear()+"-01-01";
                endtime = DateTime.now().toString("yyyy-MM-dd");*/
                /*starttime = "2013-01-01";
                endtime = "2013-07-15";*/
            }

            List<TablePageConfigVO> list =
                    getTablePageConfig(pageencode,"2","0").stream().filter(ivo ->ivo.getMax()!=null && !ivo.getMax().equals("")).collect(Collectors.toList());

            /*Map<String, List<TablePageConfigVO>> exlist = iExamineServiceCoke.getExaminelist(group, pageencode, list,
                    starttime, endtime);*/
            Map<String, List<TablePageConfigVO>> exlist = iExamineServiceCoke.getCokeExaminelist(group, pageencode, list,
                    starttime, endtime);
            List<yczdParamVo> pointlist = new ArrayList<>();
            String[] stringdevice = null;
            for (Map.Entry<String, List<TablePageConfigVO>> item : exlist.entrySet()
            ) {
                yczdParamVo vo = new yczdParamVo();
                if (group.length != exlist.size()) {
                    vo.setParameterName(group[0]);
                    vo.setChartType(charttype[0]);
                } else {
                    vo.setParameterName(item.getKey());
                    vo.setChartType("Bar");
                }
                String[] stringArray = new String[item.getValue().size()];
                String[] valueArray = new String[item.getValue().size()];
                for (int i = 0; i < item.getValue().size(); i++) {
                    if (xsws != -1) {
                        if (item.getValue().get(i).getScore() != null) {
                            String value = getXsValue(item.getValue().get(i).getScore(), xsws);
                            valueArray[i] = value;
                        } else {
                            valueArray[i] = getXsValue(0.0, xsws);
                        }
                    }
                    String device = item.getValue().get(i).getDevicename();
                    stringArray[i] = device;
                }
                vo.setParameterValue(valueArray);
                if (ismacd.equals("1")) {
                    vo.setMacdValue(getCokemacd(valueArray));
                }
                if (ismacdset.equals("1")) {
                    vo.setMacdValue(getCokemacdset(redlineset, valueArray));
                }
                pointlist.add(vo);
                stringdevice = stringArray;
            }

            yczdParamNewVo newVo = new yczdParamNewVo();
            if (starttime.isEmpty() && endtime.isEmpty()) {
                starttime = DateTime.now().toString("yyyy-MM-dd 0:00:00");
                endtime = DateTime.now().toString("yyyy-MM-dd HH:mm:ss");
            } else {
                starttime = starttime + " 0:00:00";
                if (endtime.equals(DateTime.now().toString("yyyy-MM-dd"))) {
                    endtime = DateTime.now().toString("yyyy-MM-dd HH:mm:ss");
                } else {
                    endtime = endtime + " 0:00:00";
                }
            }
            newVo.setQuerytime("数据更新时间:" + starttime + " 至 " + endtime);
            newVo.setDeviceSum(stringdevice);
            newVo.setYczdParamListVo(pointlist);
            return newVo;
            //return ResponseUtil.success(newVo);

        } catch (Exception e) {
            // TODO: handle exception
            //log.error(e.getMessage());
            return ResponseUtil.error(ResponseCode.ERROR_CODE, e.getMessage());
        }
        //return ResponseUtil.success(null);
    }

    /**
     * 开停工累计瀑布图
     *
     * @param group
     * @param charttype
     * @param pageencode
     * @param starttime
     * @param endtime
     * @param ismacd
     * @param ismacdset
     * @param redlineset
     * @param xsws
     * @param dtype
     * @return
     * @throws Exception
     */
    @Override
    public Object GetCokeGantWaterFallChart(String[] group, String[] charttype, String pageencode, String starttime,
                                        String endtime, String ismacd, String ismacdset, String redlineset, Integer xsws
            , String dtype) throws Exception {

        try {
            List<TablePageConfigVO> list =getTablePageConfig(pageencode,"0","0");
            List<GantWaterVO> pointlist = new ArrayList<>();
            List<String> monthLcn = new ArrayList();
            List<String> yearL = new ArrayList();
            List<String> monthL = new ArrayList();//月份轴
            List<String> mountL = new ArrayList();
            /*HttpServletRequest req= (HttpServletRequest)request;
            HttpSession session=req.getSession();
            List chartconfig=(List) session.getAttribute("ChartConfig");*/
            /*if (YczdCataUtils.GetConfig(16, chartconfig) =="0")*/
            TchartConfigVO configvo=  tchartConfigCokeMapper.GetTchartConfig("16")==null?null:tchartConfigCokeMapper.GetTchartConfig("16").get(0);
            if (configvo.getContent().equals("0")) {
                //向前12个月
                endtime = LocalDate.now().toString();
                starttime = LocalDate.parse(endtime).plusMonths(-12).toString();

            }
            else if (configvo.getContent().equals("1"))
            {
                //当年,
                starttime = DateTime.now().toString("yyyy-01-01");
                endtime = DateTime.now().toString("yyyy-21-31");
            }
            for (int i = 0; i < 12; i++) {
                yearL.add(Integer.toString(LocalDate.parse(starttime).plusMonths(1).getYear()));
                monthL.add(Integer.toString(LocalDate.parse(starttime).plusMonths(1).getMonthValue()));
                monthLcn.add(LocalDate.parse(starttime).plusMonths(1).getYear() + "-" + Integer.toString(LocalDate.parse(starttime).plusMonths(1).getMonthValue()));
                mountL.add("0");
            }

            /*starttime = "2021-05-29";
            endtime = "2022-05-29";*/
            List<GantWaterFallVO> gantlist = gantwaterfallcokemapper.getGantWaterFall(dtype, starttime, endtime);
            for (int j = 0; j < gantlist.size(); j++) {
                //将时间跨度分配到各个月份中


                if (DateConvertUtils.getLocalDateTime(gantlist.get(j).getStarttime()).getMonthValue() == DateConvertUtils.getLocalDateTime(gantlist.get(j).getEndtime()).getMonthValue() && DateConvertUtils.getLocalDateTime(gantlist.get(j).getStarttime()).getYear() == DateConvertUtils.getLocalDateTime(gantlist.get(j).getEndtime()).getYear()) {

                    //年份和月份都相同，计入一个月（在里面约束进入本周期的12个月）
                    long ts =
                            Duration.between(DateConvertUtils.getLocalDateTime(gantlist.get(j).getEndtime()),
                                    DateConvertUtils.getLocalDateTime(gantlist.get(j).getStarttime())).toHours();
                    for (int i = 0; i < monthL.size(); i++) {
                        if (monthL.get(i) == Integer.toString(DateConvertUtils.getLocalDateTime(gantlist.get(j).getStarttime()).getMonthValue()) && yearL.get(i) == Integer.toString(DateConvertUtils.getLocalDateTime(gantlist.get(j).getStarttime()).getYear())) {
                            //年月同时相同才计入
                            mountL.add(i, Double.toString(Double.parseDouble(mountL.get(i)) + ts));
                        }
                    }
                } else if (gantlist.get(j) == null) {
                    //开始了，没结束
                    //TimeSpan ts = DateTime.Now - DateTime.Parse(((object[])shutDown[j])[1].ToString());
                    for (int i = 0; i < monthL.size(); i++) {
                        if (monthL.get(i) == Integer.toString(DateConvertUtils.getLocalDateTime(gantlist.get(j).getStarttime()).getMonthValue()) && yearL.get(i) == Integer.toString(DateConvertUtils.getLocalDateTime(gantlist.get(j).getStarttime()).getYear())) {
                            //开始的月份
                            mountL.add(i,
                                    String.valueOf(Double.parseDouble(mountL.get(i)) + Math.abs(Duration.between(LocalDateTime.of(Integer.parseInt(yearL.get(i)), Integer.parseInt(monthL.get(i)), 1, 0, 0, 0).plusMonths(1), DateConvertUtils.getLocalDateTime(gantlist.get(j).getStarttime())).toHours())));
                            //取开始时间缩在月份的下个月的第一天减去开始时间再减24小时    开始时间的下月第一天-开始时间
                        } else if (LocalDateTime.of(Integer.parseInt(yearL.get(i)), Integer.parseInt(monthL.get(i)), 1,
                                0, 0, 0).isAfter(DateConvertUtils.getLocalDateTime(gantlist.get(j).getStarttime())) && LocalDateTime.of(Integer.parseInt(yearL.get(i)), Integer.parseInt(monthL.get(i)), 1, 10, 30).plusMonths(1).isBefore(LocalDateTime.now())) {
                            //中间的整月数
                            mountL.add(i,
                                    String.valueOf(Double.parseDouble(mountL.get(i)) + Math.abs((Duration.between(LocalDateTime.of(Integer.parseInt(yearL.get(i)), Integer.parseInt(monthL.get(i)), 1, 0, 0, 0).plusMonths(1), LocalDateTime.of(Integer.parseInt(yearL.get(i)), Integer.parseInt(monthL.get(i)), 1, 0, 0, 0)).toHours()))));
                        } else if (LocalDateTime.of(Integer.parseInt(yearL.get(i)), Integer.parseInt(monthL.get(i)), 1,
                                0, 0, 0
                        ).isAfter(DateConvertUtils.getLocalDateTime(gantlist.get(j).getStarttime())) && LocalDateTime.of(Integer.parseInt(yearL.get(i)), Integer.parseInt(monthL.get(i)), 1, 0, 0, 0).plusMonths(1).isAfter(LocalDateTime.now()) && monthL.get(i) == Integer.toString(DateTime.now().getMonthOfYear()) && yearL.get(i) == Integer.toString(DateTime.now().getYear())) {
                            //结束月份，从月初到结束时间
                            mountL.add(i,
                                    String.valueOf(Double.parseDouble(mountL.get(i)) + Math.abs(Duration.between(LocalDateTime.now()
                                            , LocalDateTime.of(Integer.parseInt(yearL.get(i)),
                                                    Integer.parseInt(monthL.get(i)), 1, 0, 0, 0)).toHours())));
                        }
                    }
                } else {
                    //开始结束垮了几个月，计算

                    for (int i = 0; i < monthL.size(); i++) {
                        if (monthL.get(i) == Integer.toString(DateConvertUtils.getLocalDateTime(gantlist.get(j).getStarttime()).getMonthValue()) && yearL.get(i) == Integer.toString(DateConvertUtils.getLocalDateTime(gantlist.get(j).getStarttime()).getYear())) {
                            //开始的月份
                            mountL.add(i,
                                    String.valueOf(Double.parseDouble(mountL.get(i)) + Math.abs((Duration.between(LocalDateTime.of(Integer.parseInt(yearL.get(i)), Integer.parseInt(monthL.get(i)), 1, 0, 0, 0).plusMonths(1), DateConvertUtils.getLocalDateTime(gantlist.get(j).getStarttime())).toHours()))));
                            //取开始时间缩在月份的下个月的第一天减去开始时间再减24小时    开始时间的下月第一天-开始时间
                        } else if (LocalDateTime.of(Integer.parseInt(yearL.get(i)), Integer.parseInt(monthL.get(i)), 1,
                                0, 0, 0).isAfter(DateConvertUtils.getLocalDateTime(gantlist.get(j).getStarttime()))
                                && LocalDateTime.of(Integer.parseInt(yearL.get(i)), Integer.parseInt(monthL.get(i)), 1, 0, 0, 0
                        ).plusMonths(1).isBefore(DateConvertUtils.getLocalDateTime(gantlist.get(j).getEndtime()))) {
                            //中间的整月数
                            mountL.add(i,
                                    String.valueOf(Double.parseDouble(mountL.get(i)) + Math.abs((Duration.between(LocalDateTime.of(Integer.parseInt(yearL.get(i)), Integer.parseInt(monthL.get(i)), 1, 0, 0, 0).plusMonths(1), LocalDateTime.of(Integer.parseInt(yearL.get(i)), Integer.parseInt(monthL.get(i)), 1, 0, 0, 0)).toHours()))));
                        } else if (LocalDateTime.of(Integer.parseInt(yearL.get(i)), Integer.parseInt(monthL.get(i)), 1,
                                0, 0, 0
                        ).isAfter(DateConvertUtils.getLocalDateTime(gantlist.get(j).getStarttime())) && LocalDateTime.of(Integer.parseInt(yearL.get(i)), Integer.parseInt(monthL.get(i)), 1, 0, 0, 0).plusMonths(1).isAfter(DateConvertUtils.getLocalDateTime(gantlist.get(j).getEndtime())) && monthL.get(i) == Integer.toString(DateConvertUtils.getLocalDateTime(gantlist.get(j).getEndtime()).getMonthValue()) && yearL.get(i) == Integer.toString(DateConvertUtils.getLocalDateTime(gantlist.get(j).getEndtime()).getYear())) {
                            //结束月份，从月初到结束时间
                            mountL.add(i,
                                    String.valueOf(Double.parseDouble(mountL.get(i)) + Math.abs((Duration.between(DateConvertUtils.getLocalDateTime(gantlist.get(j).getEndtime()), LocalDateTime.of(Integer.parseInt(yearL.get(i)), Integer.parseInt(monthL.get(i)), 1, 0, 0, 0)).toHours()))));
                        }
                    }

                }
            }
            //mountL作为结束状态
            List<String> endmountl = new ArrayList();
            for (int i = 0; i < mountL.size(); i++) {
                if (i == 0) {
                    endmountl.add("0");
                } else if (i == mountL.size() - 1) {
                    endmountl.add("0");
                } else {
                    endmountl.add(mountL.get(i - 1));
                }
            }
            String[] stringdevice = null;
            String[] timevalue=new String[mountL.size()];
            for (int i = 0; i < mountL.size(); i++) {
                GantWaterVO vo = new GantWaterVO();
                if (Double.parseDouble(mountL.get(i)) < Double.parseDouble(endmountl.get(i)) && i != mountL.size() - 1) {
                    vo.setLinecolor("Green"+",绿色减少");
                    vo.setFontcolor("Green"+",绿色减少");
                    vo.setLabel(String.valueOf(Math.abs(Double.parseDouble(mountL.get(i)) - Double.parseDouble(endmountl.get(i)))));
                } else if (Double.parseDouble(mountL.get(i)) > Double.parseDouble(endmountl.get(i)) && i != mountL.size() - 1) {
                    vo.setLinecolor("Red"+",红色增加");
                    vo.setFontcolor("Red"+",红色增加");
                    vo.setLabel(String.valueOf(Math.abs(Double.parseDouble(mountL.get(i)) - Double.parseDouble(endmountl.get(i)))));
                } else if (i == mountL.size() - 1 || i == 0) {
                    vo.setLinecolor("Blue"+",蓝色");
                    vo.setFontcolor("Blue"+",蓝色");
                    vo.setLabel(String.valueOf(Math.abs(Double.parseDouble(mountL.get(i)) - Double.parseDouble(endmountl.get(i)))));
                } else {
                    vo.setLinecolor("Black"+",黑色");
                    vo.setFontcolor("Black"+",黑色");
                    vo.setLabel(String.valueOf(Math.abs(Double.parseDouble(mountL.get(i)) - Double.parseDouble(endmountl.get(i)))));
                }
                if (i == 0 || i == mountL.size() - 1) {
                    vo.setLinecolor("Blue"+",蓝色");
                }
                timevalue[i]=DateConvertUtils.getLocalDateMonth(LocalDate.parse(starttime).plusMonths(i));
                vo.setToolTip("当月停工时长为：" + Double.parseDouble(mountL.get(i)) + "小时");
                pointlist.add(vo);
            }
            stringdevice=timevalue;
            yczdParamGantWaterVo newVo = new yczdParamGantWaterVo();
            if (starttime.isEmpty() && endtime.isEmpty()) {
                starttime = DateTime.parse(starttime).toString("yyyy-MM");
                endtime = DateTime.parse(endtime).toString("yyyy-MM");
            } else {
                starttime = starttime.substring(0, 7);
                endtime = endtime.substring(0, 7);
            }
            newVo.setQuerytime("对比时间:" + endtime + " 至 " + starttime);
            newVo.setDeviceSum(stringdevice);
            newVo.setYczdParamListVo(pointlist);
            return newVo;
            //return ResponseUtil.success(newVo);
        } catch (Exception e) {
            // TODO: handle exception
            //log.error(e.getMessage());
            return ResponseUtil.error(ResponseCode.ERROR_CODE, e.getMessage());
        }
        //return pointlist;
    }

    /**
     * 三维四象限图-总液收
     *
     * @param group
     * @param charttype
     * @param pageencode
     * @param starttime
     * @param endtime
     * @param ismacd
     * @param ismacdset
     * @param redlineset
     * @param xsws
     * @return
     * @throws Exception
     */
    @Override
    public Object GetCokeBubblrConfigChart(String[] group, String[] charttype, String pageencode, String starttime,
                                       String endtime, String ismacd, String ismacdset, String redlineset, Integer xsws
    ) throws Exception {

        try {
            List<TablePageConfigVO> list =getTablePageConfig(pageencode,"1","0");
            double max = 0.0;
            double min = 100000.0;
            double maxy = 0.0;
            double miny = 100000.0;
            List<AnalysisListVO> RetList = new ArrayList<>();
            String xmlFile_YS = list.get(0).getXxml();
            String xmlFile_NH = list.get(0).getYxml();
            String xmlFile_QQ = list.get(0).getY2xml();
            //获取三组数据集
            List<yczdParamVo> xl = GetBubbleValue(starttime, endtime, xmlFile_YS.substring(0,xmlFile_YS.length()-4),
                    xsws).stream().filter(item -> item.getParameterName()!=null).collect(Collectors.toList());
            List<yczdParamVo> yl = GetBubbleValue(starttime, endtime, xmlFile_NH.substring(0,xmlFile_NH.length()-4), xsws).stream().filter(item -> item.getParameterName()!=null).collect(Collectors.toList());
            List<yczdParamVo> y2l = GetBubbleValue(starttime, endtime, xmlFile_QQ.substring(0,xmlFile_QQ.length()-4), xsws).stream().filter(item -> item.getParameterName()!=null).collect(Collectors.toList());
            for (int i = 0; i < xl.size(); i++) {
                AnalysisListVO al = new AnalysisListVO();
                al.setName(xl.get(i).getParameterName());
                al.setX(Double.parseDouble(xl.get(i).getDeviceValue()==null?"0":xl.get(i).getDeviceValue()));
                //遍历y和y2
                if(i<yl.size()) {
                    al.setY(Double.parseDouble(yl.get(i).getDeviceValue() == null ? "0" : yl.get(i).getDeviceValue()));
                }else {
                    al.setY(0.0);
                }
                if(i<y2l.size()) {
                    al.setY2(Double.parseDouble(y2l.get(i).getDeviceValue() == null ? "0" : y2l.get(i).getDeviceValue()));
                }else {
                    al.setY2(0.0);
                }
                RetList.add(al);
            }
            String buffername = "Coking_BubbleAnalysis";
            List<AbnormalFilterVO> abnormallist = abnormalfiltercokemapper.getAbnormalFilterList(buffername);
            double XMaxValue = 0;//X最大值
            double XMinValue = 0;//X最小值
            double YMaxValue = 0;//Y最大值
            double YMinValue = 0;//Y最小值
            double Y2MaxValue = 0;//Y2最大值
            double Y2MinValue = 0;//Y2最小值

            long groups = 0;//0：X   1：Y

            for (int i = 0; i < abnormallist.size(); i++) {
                if (Integer.parseInt(abnormallist.get(i).getGroups()) == 0)//0：X
                {
                    if (abnormallist.get(i).getMaximum() != null || abnormallist.get(i).getMinimum() != null) {
                        XMaxValue = Double.parseDouble(abnormallist.get(i).getMaximum());//X最大值
                        XMinValue = Double.parseDouble(abnormallist.get(i).getMinimum());//X最大值
                    }
                } else if (Integer.parseInt(abnormallist.get(i).getGroups()) == 1)//1：Y
                {
                    if (abnormallist.get(i).getMaximum() != null || abnormallist.get(i).getMinimum() != null) {
                        YMaxValue = Double.parseDouble(abnormallist.get(i).getMaximum());//Y值
                        YMinValue = Double.parseDouble(abnormallist.get(i).getMinimum());//Y值

                    }
                } else if (Integer.parseInt(abnormallist.get(i).getGroups()) == 2) {
                    if (abnormallist.get(i).getMaximum() != null || abnormallist.get(i).getMinimum() != null) {
                        Y2MaxValue = Double.parseDouble(abnormallist.get(i).getMaximum());//Y2值
                        Y2MinValue = Double.parseDouble(abnormallist.get(i).getMinimum());//Y2值

                    }
                }
            }
            List<String> listName = new ArrayList<>();
            List<String> listValue = new ArrayList<>();
            for (int i = RetList.size() - 1; i >= 0; i--) {
                if (RetList.get(i).getX() <= XMinValue || RetList.get(i).getX() >= XMaxValue || RetList.get(i).getY() <= YMinValue || (RetList.get(i).getY() >= YMaxValue || RetList.get(i).getY2() <= Y2MinValue || RetList.get(i).getY2() >= Y2MaxValue)) {
                    listName.add(RetList.get(i).getName());

                    listValue.add((RetList.get(i).getName() + "X轴:" + RetList.get(i).getX() + "Y轴:" + RetList.get(i).getY() + "Y2轴:" + RetList.get(i).getY2()));
                    RetList.remove(i); //移除异常值
                }
            }
//region 组合股份公司
            double XValue = 0;
            double YValue = 0;
            double Y2Value = 0;
            for (int i = 0; i < RetList.size(); i++) {
                XValue += RetList.get(i).getX();
                YValue += RetList.get(i).getY();
                Y2Value += RetList.get(i).getY2();
            }
            //chart.Series[chart.Series.Count].Points.AddXY(XValue / double.Parse(chart.Series.Count.ToString()), new object[] { YValue / double.Parse(chart.Series.Count.ToString()), Y2Value / double.Parse(chart.Series.Count.ToString()) });
            //.Add(get_gf(XValue / chart.Series.Count, YValue / chart.Series.Count, Y2Value / chart.Series.Count, starttime, endtime));
            if (RetList.size()>0){
                RetList.add(get_gf(XValue / RetList.size(), YValue / RetList.size(), Y2Value / RetList.size(),
                        starttime, endtime));//添加股份球
            }

            //endregion
            for (int i = 0; i < RetList.size(); i++) {
                //if (Util.GetStopBadData(Util.DeviceType.Catalytic) != null && !((IList)(Util.GetStopBadData(Util.DeviceType.Catalytic)[0])).Contains(((AnalysisList)bubblelist[i]).Name.Replace("\n", "")) && !(((IList)(Util.GetStopBadData(Util.DeviceType.Catalytic)[1])).Contains(((AnalysisList)bubblelist[i]).Name.Replace("\n", ""))))
                // {
                if (max < YczdCataUtils.DandusChartBugChange(RetList.get(i).getX())) {
                    max = YczdCataUtils.DandusChartBugChange(RetList.get(i).getX());
                }
                if (min > YczdCataUtils.DandusChartBugChange(RetList.get(i).getX())) {
                    min = YczdCataUtils.DandusChartBugChange(RetList.get(i).getX());
                }
                if (maxy < RetList.get(i).getY()) {
                    maxy = RetList.get(i).getY();
                }
                if (miny > RetList.get(i).getY()) {
                    miny = RetList.get(i).getY();
                }
                // }
            }
            if (maxy == miny) {
                //当数据相等时要对最大值和最小值进行修正
                maxy = maxy * 1.1;
                miny = miny * 0.9;
            }
            if (max == min) {
                //当数据相等时要对最大值和最小值进行修正
                max = max * 1.1;
                min = min * 0.9;
            }
            int xF = YczdCataUtils.BubbleNumCheck((max - min) * 0.05);
            int yF = YczdCataUtils.BubbleNumCheck((maxy - miny) * 0.1);
            List<yczdAbnormalVO> pointlist = new ArrayList<>();
            yczdAbnormalVO vo = new yczdAbnormalVO();
            if (starttime.isEmpty() && endtime.isEmpty()) {
                starttime = DateTime.now().toString("yyyy-MM-dd 0:00:00");
                endtime = DateTime.now().toString("yyyy-MM-dd HH:mm:ss");
            }

            String[] stringArray = new String[RetList.size()];
            Double[] valueArrayx = new Double[RetList.size()];
            Double[] valueArrayy = new Double[RetList.size()];
            Double[] valueArrayy2 = new Double[RetList.size()];
            String[] stringdevice = null;
            Double[] XStripLines = new Double[RetList.size()];
            Double[] YStripLines = new Double[RetList.size()];
            for (int i = 0; i < RetList.size(); i++) {

                Double valuex = RetList.get(i).getX();
                valueArrayx[i] = valuex;
                Double valuey = RetList.get(i).getY();
                valueArrayy[i] = valuey;
                Double valuey2 = RetList.get(i).getY2();
                valueArrayy2[i] = valuey2;
                XStripLines[i] = RetList.get(RetList.size() - 1).getX();
                YStripLines[i] = RetList.get(RetList.size() - 1).getY();
                String device = RetList.get(i).getName();
                stringArray[i] = device;
            }
            vo.setParametervaluex(valueArrayx);
            vo.setParametervaluey(valueArrayy);
            vo.setParametervaluey2(valueArrayy2);
            vo.setXstriplines(XStripLines);
            vo.setYstriplines(YStripLines);
            pointlist.add(vo);
            stringdevice = stringArray;
            AbnormalParamVO newVo = new AbnormalParamVO();
            newVo.setQuerytime("数据更新时间:" + starttime + " 至 " + endtime);
            newVo.setXMinimum(Double.parseDouble(getXsValue(min - (max - min) * 0.05, xF)) > 0 ?
                    getXsValue(min - (max - min) * 0.05, xF) : getXsValue(0.0, 0));
            newVo.setXMaximum(getXsValue((max - min) * 0.05 + max, xF));
            newVo.setYMinimum(Double.parseDouble(getXsValue(miny - (maxy - miny) * 0.1, yF)) > 0 ?
                    getXsValue(miny - (maxy - miny) * 0.1, yF) : "0");
            newVo.setYMaximum(getXsValue((maxy - miny) * 0.1 + maxy, yF));
            newVo.setXInterval(getXsValue((max - min) / 7, xF));
            newVo.setYInterval(getXsValue((maxy - miny) / 4, 1));

            newVo.setYczdParamListVo(pointlist);
            newVo.setDeviceSum(stringArray);
            return newVo;
            //return ResponseUtil.success(newVo);
        } catch (Exception e) {
            // TODO: handle exception
            //log.error(e.getMessage());
            return ResponseUtil.error(ResponseCode.ERROR_CODE, e.getMessage());
        }
        //return pointlist;
    }

    /**
     * 三维四象限图-主风机出口到烟机入口压降
     *
     * @param group
     * @param charttype
     * @param pageencode
     * @param starttime
     * @param endtime
     * @param ismacd
     * @param ismacdset
     * @param redlineset
     * @param xsws
     * @return
     * @throws Exception
     */
    @Override
    public Object GetCokeDesulfurizeBubCfgChart(String[] group, String[] charttype, String pageencode, String starttime,
                                            String endtime, String ismacd, String ismacdset, String redlineset, Integer xsws
    ) throws Exception {

        try {
            List<TablePageConfigVO> list =getTablePageConfig(pageencode,"0","0");
            double max = 0.0;
            double min = 100000.0;
            double maxy = 0.0;
            double miny = 100000.0;
            List<AnalysisListVO> RetList = new ArrayList<>();
            String xmlFile_YS = list.get(0).getXxml();
            String xmlFile_NH = list.get(0).getYxml();
            String xmlFile_QQ = list.get(0).getY2xml();
            //获取三组数据集
            List<yczdParamVo> xl = GetBubbleValue(starttime, endtime, xmlFile_YS, xsws);
            List<yczdParamVo> yl = GetBubbleValue(starttime, endtime, xmlFile_NH, xsws);
            List<yczdParamVo> y2l = GetBubbleValue(starttime, endtime, xmlFile_QQ, xsws);
            for (int i = 0; i < xl.size(); i++) {
                AnalysisListVO al = new AnalysisListVO();
                al.setName(xl.get(i).getParameterName());
                al.setX(Double.parseDouble(xl.get(i).getDeviceValue()));
                //遍历y和y2
                al.setY(Double.parseDouble(yl.get(i).getDeviceValue()));
                al.setY2(Double.parseDouble(y2l.get(i).getDeviceValue()));
                RetList.add(al);
            }
            String buffername = "Catalytic_BubbleAnalysis_2";
            List<AbnormalFilterVO> abnormallist = abnormalfiltercokemapper.getAbnormalFilterList(buffername);
            double XMaxValue = 0;//X最大值
            double XMinValue = 0;//X最小值
            double YMaxValue = 0;//Y最大值
            double YMinValue = 0;//Y最小值
            double Y2MaxValue = 0;//Y2最大值
            double Y2MinValue = 0;//Y2最小值

            long groups = 0;//0：X   1：Y

            for (int i = 0; i < abnormallist.size(); i++) {
                if (Integer.parseInt(abnormallist.get(i).getGroups()) == 0)//0：X
                {
                    if (abnormallist.get(i).getMaximum() != null || abnormallist.get(i).getMinimum() != null) {
                        XMaxValue = Double.parseDouble(abnormallist.get(i).getMaximum());//X最大值
                        XMinValue = Double.parseDouble(abnormallist.get(i).getMinimum());//X最大值
                    }
                } else if (Integer.parseInt(abnormallist.get(i).getGroups()) == 1)//1：Y
                {
                    if (abnormallist.get(i).getMaximum() != null || abnormallist.get(i).getMinimum() != null) {
                        YMaxValue = Double.parseDouble(abnormallist.get(i).getMaximum());//Y值
                        YMinValue = Double.parseDouble(abnormallist.get(i).getMinimum());//Y值

                    }
                } else if (Integer.parseInt(abnormallist.get(i).getGroups()) == 2) {
                    if (abnormallist.get(i).getMaximum() != null || abnormallist.get(i).getMinimum() != null) {
                        Y2MaxValue = Double.parseDouble(abnormallist.get(i).getMaximum());//Y2值
                        Y2MinValue = Double.parseDouble(abnormallist.get(i).getMinimum());//Y2值

                    }
                }
            }
            List<String> listName = new ArrayList();
            List<String> listValue = new ArrayList();
            for (int i = RetList.size() - 1; i >= 0; i--) {
                if (RetList.get(i).getX() <= XMinValue || RetList.get(i).getX() >= XMaxValue || RetList.get(i).getY() <= YMinValue || (RetList.get(i).getY() >= YMaxValue || RetList.get(i).getY2() <= Y2MinValue || RetList.get(i).getY2() >= Y2MaxValue)) {
                    listName.add(RetList.get(i).getName());

                    listValue.add((RetList.get(i).getName() + "X轴:" + RetList.get(i).getX() + "Y轴:" + RetList.get(i).getY() + "Y2轴:" + RetList.get(i).getY2()));
                    RetList.remove(i); //移除异常值
                }
            }
//region 组合股份公司
            double XValue = 0;
            double YValue = 0;
            double Y2Value = 0;
            for (int i = 0; i < RetList.size(); i++) {
                XValue += RetList.get(i).getX();
                YValue += RetList.get(i).getY();
                Y2Value += RetList.get(i).getY2();
            }
            //chart.Series[chart.Series.Count].Points.AddXY(XValue / double.Parse(chart.Series.Count.ToString()), new object[] { YValue / double.Parse(chart.Series.Count.ToString()), Y2Value / double.Parse(chart.Series.Count.ToString()) });
            //.Add(get_gf(XValue / chart.Series.Count, YValue / chart.Series.Count, Y2Value / chart.Series.Count, starttime, endtime));
            RetList.add(get_gf(XValue / RetList.size(), YValue / RetList.size(), Y2Value / RetList.size(),
                    starttime, endtime));//添加股份球
            //endregion
            for (int i = 0; i < RetList.size(); i++) {
                //if (Util.GetStopBadData(Util.DeviceType.Catalytic) != null && !((IList)(Util.GetStopBadData(Util.DeviceType.Catalytic)[0])).Contains(((AnalysisList)bubblelist[i]).Name.Replace("\n", "")) && !(((IList)(Util.GetStopBadData(Util.DeviceType.Catalytic)[1])).Contains(((AnalysisList)bubblelist[i]).Name.Replace("\n", ""))))
                // {
                if (max < YczdCataUtils.DandusChartBugChange(RetList.get(i).getX())) {
                    max = YczdCataUtils.DandusChartBugChange(RetList.get(i).getX());
                }
                if (min > YczdCataUtils.DandusChartBugChange(RetList.get(i).getX())) {
                    min = YczdCataUtils.DandusChartBugChange(RetList.get(i).getX());
                }
                if (maxy < RetList.get(i).getY()) {
                    maxy = RetList.get(i).getY();
                }
                if (miny > RetList.get(i).getY()) {
                    miny = RetList.get(i).getY();
                }
                // }
            }
            if (maxy == miny) {
                //当数据相等时要对最大值和最小值进行修正
                maxy = maxy * 1.1;
                miny = miny * 0.9;
            }
            if (max == min) {
                //当数据相等时要对最大值和最小值进行修正
                max = max * 1.1;
                min = min * 0.9;
            }
            int xF = YczdCataUtils.BubbleNumCheck((max - min) * 0.05);
            int yF = YczdCataUtils.BubbleNumCheck((maxy - miny) * 0.1);
            List<yczdAbnormalVO> pointlist = new ArrayList<>();
            yczdAbnormalVO vo = new yczdAbnormalVO();
            if (starttime.isEmpty() && endtime.isEmpty()) {
                starttime = DateTime.now().toString("yyyy-MM-dd 0:00:00");
                endtime = DateTime.now().toString("yyyy-MM-dd HH:mm:ss");
            }

            String[] stringArray = new String[RetList.size()];
            Double[] valueArrayx = new Double[RetList.size()];
            Double[] valueArrayy = new Double[RetList.size()];
            Double[] valueArrayy2 = new Double[RetList.size()];
            String[] stringdevice = null;
            Double[] XStripLines = new Double[RetList.size()];
            Double[] YStripLines = new Double[RetList.size()];
            for (int i = 0; i < RetList.size(); i++) {

                Double valuex = RetList.get(i).getX();
                valueArrayx[i] = valuex;
                Double valuey = RetList.get(i).getY();
                valueArrayy[i] = valuey;
                Double valuey2 = RetList.get(i).getY2();
                valueArrayy2[i] = valuey2;
                XStripLines[i] = RetList.get(RetList.size() - 1).getX();
                YStripLines[i] = RetList.get(RetList.size() - 1).getY();
                String device = RetList.get(i).getName();
                stringArray[i] = device;
            }
            vo.setParametervaluex(valueArrayx);
            vo.setParametervaluey(valueArrayy);
            vo.setParametervaluey2(valueArrayy2);
            vo.setXstriplines(XStripLines);
            vo.setYstriplines(YStripLines);

            pointlist.add(vo);
            stringdevice = stringArray;
            AbnormalParamVO newVo = new AbnormalParamVO();
            newVo.setQuerytime("数据更新时间:" + starttime + " 至 " + endtime);
            newVo.setXMinimum(Double.parseDouble(getXsValue(min - (max - min) * 0.05, xF)) > 0 ?
                    getXsValue(min - (max - min) * 0.05, xF) : getXsValue(0.0, 0));
            newVo.setXMaximum(getXsValue((max - min) * 0.05 + max, xF));
            newVo.setYMinimum(Double.parseDouble(getXsValue(miny - (maxy - miny) * 0.1, yF)) > 0 ?
                    getXsValue(miny - (maxy - miny) * 0.1, yF) : "0");
            newVo.setYMaximum(getXsValue((maxy - miny) * 0.1 + maxy, yF));
            newVo.setXInterval(getXsValue((max - min) / 7, xF));
            newVo.setYInterval(getXsValue((maxy - miny) / 4, 1));
            newVo.setYczdParamListVo(pointlist);
            newVo.setDeviceSum(stringArray);
            return ResponseUtil.success(newVo);
        } catch (Exception e) {
            // TODO: handle exception
            //log.error(e.getMessage());
            return ResponseUtil.error(ResponseCode.ERROR_CODE, e.getMessage());
        }
        //return pointlist;
    }

    private List<yczdParamVo> GetBubbleValue(String starttime, String endtime, String xmlFile, Integer xsws) {
        List<TablePageConfigVO> list =getTablePageConfig(xmlFile,"0","0");
        //String[] groupcode = new String[] { "原料UOP K因子", "轻收" };
        List<yczdParamVo> pointlist = new ArrayList<>();


        List<String> type = list.stream().map(TablePageConfigVO::getGrouptype).distinct().collect(Collectors.toList());
        String[] stringdevice = null;
        for (int m = 0; m < type.size(); m++) {
            final String typecode = type.get(m);

            List<TablePageConfigVO> listvo =
                    list.stream().filter(ivo -> ivo.getGrouptype().equals(typecode)).collect(Collectors.toList());
            String[] stringArray = new String[listvo.size()];
            Double[] valueArray = new Double[listvo.size()];
            for (int i = 0; i < listvo.size(); i++) {
                yczdParamVo vo = new yczdParamVo();
                /*vo.setDeviceCode(list.get(i).getPointname());*/
                Double pointvalue = 0.00;
                if (listvo.get(i).getPointvalue() != null && !listvo.get(i).getPointvalue().equals("")) {
                    if (listvo.get(i) != null && listvo.get(i).getPointvalue() != "") {

                        pointvalue = Double.parseDouble(irtdbCokeservice.calculateCoke(listvo.get(i).getPointvalue(),
                                Integer.parseInt(listvo.get(i).getIsformula()),
                                starttime,
                                endtime));

                    } else {
                        pointvalue = 0.0;
                    }
                } else {
                    pointvalue = 0.0;
                }
                final String poincode = listvo.get(i).getPointvalue();
                List<TIndexDataVO> indexvolist = tindexdatacokemapper.GetIndexdata(poincode);

                if (indexvolist.size() > 0) {
                    if (indexvolist.get(0).getValid_data_top_limit() != null && indexvolist.get(0).getValid_data_bottom_limit() != null) {
                        if (pointvalue <= indexvolist.get(0).getValid_data_top_limit() && pointvalue >= indexvolist.get(0).getValid_data_bottom_limit()) {
                            if (listvo.size() > 0) {
                                if (xsws != -1) {
                                    vo.setParameterName(listvo.get(i).getPointname());
                                    vo.setDeviceValue(getXsValue(pointvalue, xsws));

                                }

                            }
                        }
                    } else {
                        if (listvo.size() > 0) {
                            if (xsws != -1) {
                                vo.setParameterName(listvo.get(i).getPointname());
                                vo.setDeviceValue(getXsValue(pointvalue, xsws));
                            }

                        }
                    }
                } else {
                    if (xsws != -1) {
                        vo.setParameterName(listvo.get(i).getPointname());
                        vo.setDeviceValue(getXsValue(pointvalue, xsws));
                    }

                }
                pointlist.add(vo);
            }
            /*yczdParamVo vo = new yczdParamVo();

             *//*vo.setJKCS(JKCS);
           vo.setJKCSJC(JKCSJC);*//*
             *//*vo.setDeviceSum(stringArray);*//*
            vo.setParameterValue(valueArray);
            pointlist.add(vo);
            if (m == 0) {
                stringdevice = stringArray;
            }*/
        }
        return pointlist;
    }

    private AnalysisListVO get_gf(double xpoint, double ypoint, double y2point, String starttime, String endtime) {
        AnalysisListVO al = new AnalysisListVO();
        al.setName("股份");
        al.setX(Double.parseDouble(getXsValue(xpoint, 1)));
        //遍历y和y2
        al.setY(Double.parseDouble(getXsValue(ypoint, 1)));
        al.setY2(Double.parseDouble(getXsValue(y2point, 2)));

        //al.X = Math.Round(double.Parse(((object)rtdb.getRTDBPointAVG(xpoint, starttime, endtime)).ToString()), 1);
        ////遍历y和y2
        //al.Y = Math.Round(double.Parse(((object)rtdb.getRTDBPointAVG(ypoint, starttime, endtime)).ToString()), 1);
        //al.Y2 = Math.Round(double.Parse(((object)rtdb.getRTDBPointAVG(y2point, starttime, endtime)).ToString()), 1);
        return al;
    }

    @Override
    public Object GetCokeCatalyticRateBubbleChart(String[] group, String[] charttype, String pageencode, String starttime,
                                              String endtime, String ismacd, String ismacdset, String redlineset, Integer xsws
    ) throws Exception {

        try {
            /*List<TablePageConfigVO> list =
                    tablepageconfigservice.GetTablePageConfigList(pageencode);*/
            List<TablePageConfigVO> list =getTablePageConfig(pageencode,"0","0");
            double max = 0.0;
            double min = 100000.0;
            double maxy = 0.0;
            double miny = 100000.0;
            List<AnalysisListVO> RetList = new ArrayList<>();
            for (int i = 0; i < list.size(); i++) {
                AnalysisListVO vo = new AnalysisListVO();
                vo.setName(list.get(i).getPointname());
                vo.setX(Double.parseDouble(getXsValue(Double.parseDouble(irtdbCokeservice.calculateCoke(list.get(i).getX(), 0,
                        starttime,
                        endtime)), 2)));
                vo.setY(Double.parseDouble(getXsValue(Double.parseDouble(irtdbCokeservice.calculateCoke(list.get(i).getY(), 0, starttime,
                        endtime)), 1)));
                vo.setY2(Double.parseDouble(getXsValue(Double.parseDouble(irtdbCokeservice.calculateCoke(list.get(i).getY2(), 0, starttime,
                        endtime)), 1)));
                vo.setStrColor(list.get(i).getColor());
                RetList.add(vo);
            }

            String buffername = "Catalytic_CatalyticRateBubble";
            List<AbnormalFilterVO> abnormallist = abnormalfiltercokemapper.getAbnormalFilterList(buffername);
            double XMaxValue = 0;//X最大值
            double XMinValue = 0;//X最小值
            double YMaxValue = 0;//Y最大值
            double YMinValue = 0;//Y最小值
            double Y2MaxValue = 0;//Y2最大值
            double Y2MinValue = 0;//Y2最小值

            long groups = 0;//0：X   1：Y

            for (int i = 0; i < abnormallist.size(); i++) {
                if (Integer.parseInt(abnormallist.get(i).getGroups()) == 0)//0：X
                {
                    if (abnormallist.get(i).getMaximum() != null || abnormallist.get(i).getMinimum() != null) {
                        XMaxValue = Double.parseDouble(abnormallist.get(i).getMaximum());//X最大值
                        XMinValue = Double.parseDouble(abnormallist.get(i).getMinimum());//X最大值
                    }
                } else if (Integer.parseInt(abnormallist.get(i).getGroups()) == 1)//1：Y
                {
                    if (abnormallist.get(i).getMaximum() != null || abnormallist.get(i).getMinimum() != null) {
                        YMaxValue = Double.parseDouble(abnormallist.get(i).getMaximum());//Y值
                        YMinValue = Double.parseDouble(abnormallist.get(i).getMinimum());//Y值

                    }
                } else if (Integer.parseInt(abnormallist.get(i).getGroups()) == 2) {
                    if (abnormallist.get(i).getMaximum() != null || abnormallist.get(i).getMinimum() != null) {
                        Y2MaxValue = Double.parseDouble(abnormallist.get(i).getMaximum());//Y2值
                        Y2MinValue = Double.parseDouble(abnormallist.get(i).getMinimum());//Y2值

                    }
                }
            }
            List<String> listName = new ArrayList();
            List<String> listValue = new ArrayList();
            for (int i = RetList.size() - 1; i >= 0; i--) {
                if (RetList.get(i).getX() <= XMinValue || RetList.get(i).getX() >= XMaxValue || RetList.get(i).getY() <= YMinValue || (RetList.get(i).getY() >= YMaxValue || RetList.get(i).getY2() <= Y2MinValue || RetList.get(i).getY2() >= Y2MaxValue)) {
                    listName.add(RetList.get(i).getName());

                    listValue.add((RetList.get(i).getName() + "X轴:" + RetList.get(i).getX() + "Y轴:" + RetList.get(i).getY() + "Y2轴:" + RetList.get(i).getY2()));
                    RetList.remove(i); //移除异常值
                }
            }
            //region 红线的斜率
            double diff = Double.parseDouble(getXsValue((getmax(RetList) - getmin(RetList)) * 0.1, 2));
            double x1 = Double.parseDouble(getXsValue(getmin(RetList) - diff, 2));
            double x2 = getmax(RetList) + diff;


            List<Double> xl = new ArrayList();
            List<Double> yl = new ArrayList();
            xl.add(Double.parseDouble(getXsValue(x1, 2)));
            xl.add(Double.parseDouble(getXsValue(x2, 2)));
            yl.add(Double.parseDouble(getXsValue(YczdCataUtils.GetCataY(x1), 2)));
            yl.add(Double.parseDouble(getXsValue(YczdCataUtils.GetCataY(x2), 2)));
            //endregion
            List<yczdAbnormalVO> pointlist = new ArrayList<>();
            yczdAbnormalVO vo = new yczdAbnormalVO();
            if (starttime.isEmpty() && endtime.isEmpty()) {
                starttime = DateTime.now().toString("yyyy-MM-dd 0:00:00");
                endtime = DateTime.now().toString("yyyy-MM-dd HH:mm:ss");
            }


            String[] stringArray = new String[RetList.size()];
            Double[] valueArrayx = new Double[RetList.size()];
            Double[] valueArrayy = new Double[RetList.size()];
            Double[] valueArrayy2 = new Double[RetList.size()];
            String[] valueColor = new String[RetList.size()];
            String[] stringdevice = null;

            for (int i = 0; i < RetList.size(); i++) {

                Double valuex = RetList.get(i).getX();
                valueArrayx[i] = valuex;
                Double valuey = RetList.get(i).getY();
                valueArrayy[i] = valuey;
                Double valuey2 = RetList.get(i).getY2();
                valueArrayy2[i] = valuey2;
                valueColor[i] = RetList.get(i).getStrColor();

                String device = RetList.get(i).getName();
                stringArray[i] = device;
            }
            vo.setParametervaluex(valueArrayx);
            vo.setParametervaluey(valueArrayy);
            vo.setParametervaluey2(valueArrayy2);
            vo.setStrColor(valueColor);
            pointlist.add(vo);
            stringdevice = stringArray;
            AbnormalParamVO newVo = new AbnormalParamVO();
            newVo.setQuerytime("数据更新时间:" + starttime + " 至 " + endtime);

            newVo.setYczdParamListVo(pointlist);
            newVo.setDeviceSum(stringArray);
            newVo.setXl(xl);
            newVo.setYl(yl);
            return ResponseUtil.success(newVo);
        } catch (Exception e) {
            // TODO: handle exception
            //log.error(e.getMessage());
            return ResponseUtil.error(ResponseCode.ERROR_CODE, e.getMessage());
        }
        //return pointlist;
    }

    private double getmax(List<AnalysisListVO> retlist) {
        double max = 0;
        for (int i = 0; i < retlist.size(); i++) {
            if (max < retlist.get(i).getX())
                max = retlist.get(i).getX();
        }
        return Double.parseDouble(getXsValue(max, 1));
    }

    private double getmin(List<AnalysisListVO> retlist) {
        double min = 999999;
        for (int i = 0; i < retlist.size(); i++) {
            if (min > retlist.get(i).getX())
                min = retlist.get(i).getX();
        }
        return Double.parseDouble(getXsValue(min, 1));
    }

    @Override
    public Object GetCokeStableRatePicChart(String[] group, String[] charttype, String pageencode, String starttime,
                                            String endtime, String ismacd, String ismacdset, String redlineset,
                                            Integer xsws,String timetype,String type,String zztype,String sorttype) throws Exception {

        try {
            List<TstableratePrameVO> list = iCokeStableRatePicService.getCokeStableList(starttime,endtime,timetype,
                    type,zztype);

            List<yczdParamVo> pointlist = new ArrayList<>();
            String[] stringArray = new String[list.size()];
            String[] valueArray = new String[list.size()];
            String[] stringdevice = null;
            for (int i = 0; i < list.size(); i++) {
                yczdParamVo vo = new yczdParamVo();
                String value = getXsValue(Double.parseDouble(list.get(i).getAvgvavlue()!=null?list.get(i).getAvgvavlue():"0"),
                        xsws);
                valueArray[i] = value;

                String device = list.get(i).getQy();
                stringArray[i] = device;
                vo.setGrouptype("1");
                if("".equals(type)){
                    vo.setParameterName("装置月度平稳率");
                }else {
                    vo.setParameterName(iCokeStableRatePicService.GetDesc(type));
                }
                vo.setDeviceValue(value);
                vo.setPointName(device);
                vo.setParameterValue(valueArray);
                pointlist.add(vo);
            }

            yczdParamNewVo newVo = new yczdParamNewVo();
            newVo = cokeSortServiceUtils.pointsortzzpwl(pointlist, new String[]{"1"}, group, charttype, ismacd, ismacdset,
                    redlineset,
                    starttime, endtime,
                    sorttype);
            return newVo;
            //return ResponseUtil.success(newVo);
        } catch (Exception e) {
            // TODO: handle exception
            //log.error(e.getMessage());
            return ResponseUtil.error(ResponseCode.ERROR_CODE, e.getMessage());
        }
        //return pointlist;
    }
    public Object GetCokePopChart(String[] group, String[] charttype, String pageencode, String starttime,
                           String endtime, String ismacd, String ismacdset, String redlineset, Integer xsws,
                                  String ParentValue,String sorttype,String lable) throws Exception {

        try {
            List<TablePageConfigVO> list =getTablePageConfig(pageencode,"0","0");
            List<yczdParamVo> pointlist = new ArrayList<>();
            /*List<String> type = list.stream().map(TablePageConfigVO::getNumber).distinct().collect(Collectors
            .toList());*/
            List<String> type= Arrays.asList(group);
            String[] stringdevice = null;
            String unit="";
            if (ParentValue.contains("{")){
                List<String> replacelist = com.pcitc.yczd.common.utils.YczdCataUtils.GetVariantNameNewnew(ParentValue);
                unit = tindexdatacokemapper.GetIndexdata(replacelist.get(0)).size()>0?
                        tindexdatacokemapper.GetIndexdata(replacelist.get(0)).get(0).getUnit():"";
            }else {
                unit = tindexdatacokemapper.GetIndexdata(ParentValue).size()>0?
                        tindexdatacokemapper.GetIndexdata(ParentValue).get(0).getUnit():"";
            }
            for (int m = 0; m < type.size(); m++) {
                final String typecode = type.get(m);
                List<TablePageConfigVO> listvo =
                        list.stream().filter(ivo -> ivo.getGrouptype().equals(typecode)).collect(Collectors.toList());
                List<ChartPopVO>  listchart=iCokeChartpopservice.getCokePopChartList(listvo,starttime,endtime,
                        ParentValue);
                String[] valueArray = new String[listchart.size()];
                String[] codeArray = new String[listchart.size()];
                for (int i = 0; i <listchart.size() ; i++) {
                    String value = getXsValue(Double.parseDouble(listchart.get(i).getYValue()), xsws);
                    valueArray[i] = value;
                    codeArray[i]=listchart.get(i).getXValue();
                }
                stringdevice=codeArray;
                yczdParamVo vo = new yczdParamVo();
                vo.setParameterValue(valueArray);
                pointlist.add(vo);
                if (m == 0) {
                    stringdevice = codeArray;
                }
            }
            yczdParamNewVo newVo = new yczdParamNewVo();
            newVo.setUnit(unit);
            newVo.setLabel(lable);
            newVo.setQuerytime("数据更新时间:" + starttime + " 至 " + endtime);
            newVo.setDeviceSum(stringdevice);
            newVo.setYczdParamListVo(pointlist);
            return ResponseUtil.success(newVo);
        } catch (Exception e) {
            // TODO: handle exception
            //log.error(e.getMessage());
            return ResponseUtil.error(ResponseCode.ERROR_CODE, e.getMessage());
        }
        //return pointlist;
    }

    public List<TablePageConfigVO> getTablePageConfig(String pageencode,String xml,String redlineset) {
        List<TablePageConfigVO> listnew = new ArrayList<>();
        List<PageConfigEntity> json = pageConfigMapper.queryByName(pageencode + ".xml");
        if(json.size()>0) {
            if (xml.equals("1")) {
                //JSONObject jsonObject = JSONObject.fromObject(json.get(0).getContent());
                JSONObject collection = json.get(0).getContentObj();
                for (Map.Entry<String, Object> point : collection.entrySet()
                ) {
                    List<TablePageConfigVO> list1 = new ArrayList<>();
                    String pointname = point.getKey();
                    if (pointname.equals("Data")) {
                        list1 =
                                ((JSONArray) point.getValue()).toJavaList(TablePageConfigVO.class);
                        list1.stream().forEach(item -> {
                            item.setGrouptype(pointname);
                            if (item.getPointvalue() != null && (item.getPointvalue().indexOf("{") != -1 || item.getPointvalue().indexOf("(") != -1)) {
                                item.setIsformula("2");
                            } else {
                                item.setIsformula("1");
                            }
                        });

                        listnew.addAll(list1);
                    }
                }
            }
            if (redlineset.equals("1")) {
                //JSONObject jsonObject = JSONObject.fromObject(json.get(0).getContent());
                JSONObject collection = json.get(0).getContentObj();
                for (Map.Entry<String, Object> point : collection.entrySet()
                ) {
                    List<TablePageConfigVO> list1 = new ArrayList<>();
                    String pointname = point.getKey();
                    if (!pointname.equals("Sort")) {
                        list1 =
                                ((JSONArray) point.getValue()).toJavaList(TablePageConfigVO.class);
                        list1.stream().forEach(item -> {
                            item.setGrouptype(pointname);
                            if (item.getPointvalue() != null && (item.getPointvalue().indexOf("{") != -1 || item.getPointvalue().indexOf("(") != -1)) {
                                item.setIsformula("2");
                            } else {
                                item.setIsformula("1");
                            }
                        });

                        listnew.addAll(list1);
                    }
                }
            }
            if (xml.equals("0") && redlineset.equals("0")) {
                //JSONObject jsonObject = JSONObject.fromObject(json.get(0).getContent());
                JSONObject collection = json.get(0).getContentObj();
                for (Map.Entry<String, Object> point : collection.entrySet()
                ) {
                    List<TablePageConfigVO> list1 = new ArrayList<>();
                    String pointname = point.getKey();
                    if (!pointname.equals("Sort")) {
                        list1 =
                                ((JSONArray) point.getValue()).toJavaList(TablePageConfigVO.class);
                        list1.stream().forEach(item -> {
                            item.setGrouptype(pointname);
                            if (item.getPointvalue() != null && (item.getPointvalue().indexOf("{") != -1 || item.getPointvalue().indexOf("(") != -1)) {
                                item.setIsformula("2");
                            } else {
                                item.setIsformula("1");
                            }
                        });

                        listnew.addAll(list1);
                    }
                }
            }
            if (xml.equals("1")) {
                //JSONObject jsonObject = JSONObject.fromObject(json.get(0).getContent());
                JSONObject collection = json.get(0).getContentObj();
                for (Map.Entry<String, Object> point : collection.entrySet()
                ) {
                    List<TablePageConfigVO> list1 = new ArrayList<>();
                    String pointname = point.getKey();
                    if (pointname.equals("Data")) {
                        list1 =
                                ((JSONArray) point.getValue()).toJavaList(TablePageConfigVO.class);
                        list1.stream().forEach(item -> {
                            item.setGrouptype(pointname);
                            if (item.getPointvalue() != null && (item.getPointvalue().indexOf("{") != -1 || item.getPointvalue().indexOf("(") != -1)) {
                                item.setIsformula("2");
                            } else {
                                item.setIsformula("1");
                            }
                        });

                        listnew.addAll(list1);
                    }
                }
            }
            if (xml.equals("2")) {
                //JSONObject jsonObject = JSONObject.fromObject(json.get(0).getContent());
                JSONObject collection = json.get(0).getContentObj();
                for (Map.Entry<String, Object> point : collection.entrySet()
                ) {
                    List<TablePageConfigVO> list1 = new ArrayList<>();
                    String pointname = point.getKey();

                    if (!pointname.equals("Sort")) {
                        if (pointname.equals("devices")) {
                            for (Map.Entry<String, Object> index : ((JSONObject) point.getValue()).entrySet()
                            ) {
                                String pointindex = index.getKey();
                                list1 =
                                        ((JSONArray) index.getValue()).toJavaList(TablePageConfigVO.class);
                                for (int i = 0; i < ((JSONArray) index.getValue()).size(); i++) {
                                    final int aa = i;
                                    JSONArray json1 = new JSONArray();
                                    json1.add(((JSONArray) index.getValue()).get(i));
                                    list1 = json1.toJavaList(TablePageConfigVO.class);
                                    for (Map.Entry<String, Object> indexnew :
                                            ((JSONObject) ((JSONArray) index.getValue()).get(i)).entrySet()
                                    ) {
                                        list1.stream().forEach(item -> {
                                            item.setDwcode(index.getKey());
                                            item.setPointname(index.getKey());
                                            item.setGrouptype(pointindex);
                                        /*item.setClasstype(((JSONObject) ((JSONArray) index.getValue()).get(aa)).get(
                                                "class").toString());*/
                                            if (indexnew.getKey().equals("class")) {
                                                item.setClasstype(indexnew.getValue().toString());
                                            }
                                            if (item.getPointvalue() != null && (item.getPointvalue().indexOf("{") != -1 || item.getPointvalue().indexOf("(") != -1)) {
                                                item.setIsformula("2");
                                            } else {
                                                item.setIsformula("1");
                                            }
                                        });
                                    }
                                    listnew.addAll(list1);
                                }

                            }
                            listnew.addAll(list1);
                        } else {
                            TablePageConfigVO vo = new TablePageConfigVO();
                            for (Map.Entry<String, Object> device : ((JSONObject) point.getValue()).entrySet()
                            ) {

                                if (device.getKey().equals("Item")) {
                                    vo.setItem(device.getValue().toString());
                                } else {
                                    vo.setIndex(device.getValue().toString());
                                }

                            }
                            list1.add(vo);
                            listnew.addAll(list1);
                        }
                    }
                }
            }
        }
        return listnew;
    }


    public List<ChartPopVO> OutOneColnum(String pageencode, String starttime, String endtime,Double dTime)
    {
        List<ChartPopVO>  listpop=new ArrayList<>();
        List<TablePageConfigVO> list=getTablePageConfig(pageencode,"0","0");

        List<String> XValue = new ArrayList();
        List<String> YValue = new ArrayList();
        try
        {
            if (!starttime.equals("")) {
                double dTimes = ChronoUnit.DAYS.between(LocalDate.parse(starttime), LocalDate.parse(endtime));
                if (dTimes >= dTime) {
                    listpop = OutOneColnum_Time(pageencode, starttime, endtime, 3);
                } else {
                    if (list != null) {
                        for (int i = 0; i < list.size(); i++) {
                            ChartPopVO vo = new ChartPopVO();
                            Double pointvalue=Double.parseDouble(irtdbCokeservice.calculateCoke(list.get(i).getPointvalue(),
                                    Integer.parseInt(list.get(i).getIsformula()),
                                    starttime,
                                    endtime));
                            final String poincode = list.get(i).getPointvalue();
                            //final String poincode = "ZHH_FCC01_CAL_CZB";
                            List<TIndexDataVO> indexvolist =tindexdatacokemapper .GetIndexdata(poincode);
                            if (indexvolist.size() > 0) {
                                if (indexvolist.get(0).getValid_data_top_limit() != null && indexvolist.get(0).getValid_data_bottom_limit() != null) {
                                    if (pointvalue <= indexvolist.get(0).getValid_data_top_limit() && pointvalue >= indexvolist.get(0).getValid_data_bottom_limit()) {
                                        vo.setXValue(list.get(i).getPointname());
                                        vo.setYValue(Double.toString(pointvalue));
                                        vo.setGrouptype(list.get(i).getGrouptype());
                                        vo.setPointValue(list.get(i).getPointvalue());
                        /*vo.setYValue(irtdbbaseservice.calculate(list.get(i).getPointvalue(),
                                Integer.parseInt(list.get(i).getIsformula()), starttime,
                                endtime));*/
                                        listpop.add(vo);
                                    }
                                }else {
                                    vo.setXValue(list.get(i).getPointname());
                                    vo.setYValue(Double.toString(pointvalue));
                                    vo.setGrouptype(list.get(i).getGrouptype());
                                    vo.setPointValue(list.get(i).getPointvalue());
                        /*vo.setYValue(irtdbbaseservice.calculate(list.get(i).getPointvalue(),
                                Integer.parseInt(list.get(i).getIsformula()), starttime,
                                endtime));*/
                                    listpop.add(vo);
                                }
                            }else {
                                vo.setXValue(list.get(i).getPointname());
                                vo.setYValue(Double.toString(pointvalue));
                                vo.setGrouptype(list.get(i).getGrouptype());
                                vo.setPointValue(list.get(i).getPointvalue());
                        /*vo.setYValue(irtdbbaseservice.calculate(list.get(i).getPointvalue(),
                                Integer.parseInt(list.get(i).getIsformula()), starttime,
                                endtime));*/
                                listpop.add(vo);
                            }
                        }
                    }
                }
            }else{
                for (int i = 0; i < list.size(); i++) {
                    ChartPopVO vo = new ChartPopVO();
                    Double pointvalue=Double.parseDouble(irtdbCokeservice.calculateCoke(list.get(i).getPointvalue(),
                            Integer.parseInt(list.get(i).getIsformula()),
                            starttime,
                            endtime));
                    final String poincode = list.get(i).getPointvalue();
                    //final String poincode = "ZHH_FCC01_CAL_CZB";
                    List<TIndexDataVO> indexvolist =tindexdatacokemapper .GetIndexdata(poincode);

                    if (indexvolist.size() > 0) {
                        if (indexvolist.get(0).getValid_data_top_limit() != null && indexvolist.get(0).getValid_data_bottom_limit() != null) {
                            if (pointvalue <= indexvolist.get(0).getValid_data_top_limit() && pointvalue >= indexvolist.get(0).getValid_data_bottom_limit()) {
                                vo.setXValue(list.get(i).getPointname());
                                vo.setYValue(Double.toString(pointvalue));
                                vo.setGrouptype(list.get(i).getGrouptype());
                                vo.setPointValue(list.get(i).getPointvalue());
                        /*vo.setYValue(irtdbbaseservice.calculate(list.get(i).getPointvalue(),
                                Integer.parseInt(list.get(i).getIsformula()), starttime,
                                endtime));*/
                                listpop.add(vo);
                            }
                        }else {
                            vo.setXValue(list.get(i).getPointname());
                            vo.setYValue(Double.toString(pointvalue));
                            vo.setGrouptype(list.get(i).getGrouptype());
                            vo.setPointValue(list.get(i).getPointvalue());
                        /*vo.setYValue(irtdbbaseservice.calculate(list.get(i).getPointvalue(),
                                Integer.parseInt(list.get(i).getIsformula()), starttime,
                                endtime));*/
                            listpop.add(vo);
                        }
                    }else {
                        vo.setXValue(list.get(i).getPointname());
                        vo.setYValue(Double.toString(pointvalue));
                        vo.setGrouptype(list.get(i).getGrouptype());
                        vo.setPointValue(list.get(i).getPointvalue());
                        /*vo.setYValue(irtdbbaseservice.calculate(list.get(i).getPointvalue(),
                                Integer.parseInt(list.get(i).getIsformula()), starttime,
                                endtime));*/
                        listpop.add(vo);
                    }
                }
            }
        }
        catch (Exception ee)
        {
            //throw new Exception("RtbChartDAO行588数据读取出错：" + ee.getMessage());
        }
        return listpop;
    }


    public List<ChartPopVO> OutOneColnum_Time(String pageencode,String starttime, String endtime,
                                              int j)
    {
        //合格率计算方法
        //查看考核中的ExamineExhaustGasPassingRate_Sru
        //配置点位号是具体的值，按默认步长取数据，找出合格的数量（即低于等于标定值）除以总数量就是合格率
        List<ChartPopVO>  listpop=new ArrayList<>();
        List<TablePageConfigVO> list=getTablePageConfig(pageencode,"0","0");

        String points="";
        try {
            for (int i = 0; i < list.size(); i++) {
                ChartPopVO vo=new ChartPopVO();
                Double pointvalue=Double.parseDouble(irtdbCokeservice.calculateCoke(list.get(i).getPointvalue(),
                        Integer.parseInt(list.get(i).getIsformula()),
                        starttime,endtime));
                final String poincode = list.get(i).getPointvalue();
                //final String poincode = "ZHH_FCC01_CAL_CZB";
                List<TIndexDataVO> indexvolist = tindexdatacokemapper.GetIndexdata(poincode);

                if (indexvolist.size() > 0) {
                    if (indexvolist.get(0).getValid_data_top_limit() != null && indexvolist.get(0).getValid_data_bottom_limit() != null) {
                        if (pointvalue <= indexvolist.get(0).getValid_data_top_limit() && pointvalue >= indexvolist.get(0).getValid_data_bottom_limit()) {
                            vo.setXValue(list.get(i).getPointname());
                            if (list.get(i).getPointvalue()!=null && !list.get(i).getPointvalue().equals("")){
                                vo.setYValue(Double.toString(pointvalue));
                            }
                            vo.setGrouptype(list.get(i).getGrouptype());
                            vo.setPointValue(list.get(i).getPointvalue());
                            listpop.add(vo);
                        }
                    }else {
                        vo.setXValue(list.get(i).getPointname());
                        if (list.get(i).getPointvalue()!=null && !list.get(i).getPointvalue().equals("")){
                            vo.setYValue(Double.toString(pointvalue));
                        }
                        vo.setGrouptype(list.get(i).getGrouptype());
                        vo.setPointValue(list.get(i).getPointvalue());
                        listpop.add(vo);
                    }
                }else {
                    vo.setXValue(list.get(i).getPointname());
                    if (list.get(i).getPointvalue()!=null && !list.get(i).getPointvalue().equals("")){
                        vo.setYValue(Double.toString(pointvalue));
                    }
                    vo.setGrouptype(list.get(i).getGrouptype());
                    vo.setPointValue(list.get(i).getPointvalue());
                    listpop.add(vo);
                }

            }

        }
        catch (Exception ee)
        {
            //throw new Exception("RtbChartDAO行588数据读取出错：" + ee.getMessage());
        }
        return listpop;
    }

    /**
     * 计算合格率的方法
     * @param starttime
     * @param endtime
     * @param pageencode
     * @return
     *//*

    public List<ChartPopVO> OutOneColnum(String pageencode, String starttime, String endtime,Double dTime)
    {
        List<ChartPopVO>  listpop=new ArrayList<>();
        List<TablePageConfigVO> list=getTablePageConfig(pageencode,"0","0");
        List<String> XValue = new ArrayList();
        List<String> YValue = new ArrayList();
        try
        {
            if (!starttime.equals("")) {
                double dTimes = ChronoUnit.DAYS.between(LocalDate.parse(starttime), LocalDate.parse(endtime));
                if (dTimes >= dTime) {
                    listpop = OutOneColnum_Time(pageencode, starttime, endtime, 3);
                } else {
                    if (list != null) {
                        for (int i = 0; i < list.size(); i++) {
                            ChartPopVO vo = new ChartPopVO();
                            vo.setXValue(list.get(i).getPointname());
                            vo.setYValue(irtdbCokeservice.calculateCoke(list.get(i).getPointvalue(),
                                    Integer.parseInt(list.get(i).getIsformula()),
                                    starttime,
                                    endtime));
                            vo.setGrouptype(list.get(i).getGrouptype());
                            vo.setPointValue(list.get(i).getPointvalue());
                        *//*vo.setYValue(irtdbbaseservice.calculate(list.get(i).getPointvalue(),
                                Integer.parseInt(list.get(i).getIsformula()), starttime,
                                endtime));*//*
                            listpop.add(vo);
                        }
                    }
                }
            }else{
                for (int i = 0; i < list.size(); i++) {
                    ChartPopVO vo = new ChartPopVO();
                    vo.setXValue(list.get(i).getPointname());
                    vo.setYValue(irtdbCokeservice.calculateCoke(list.get(i).getPointvalue(),
                            Integer.parseInt(list.get(i).getIsformula()),
                            starttime,
                            endtime));
                    vo.setGrouptype(list.get(i).getGrouptype());
                    vo.setPointValue(list.get(i).getPointvalue());
                        *//*vo.setYValue(irtdbbaseservice.calculate(list.get(i).getPointvalue(),
                                Integer.parseInt(list.get(i).getIsformula()), starttime,
                                endtime));*//*
                    listpop.add(vo);
                }
            }
        }
        catch (Exception ee)
        {
           //throw new Exception("RtbChartDAO行588数据读取出错：" + ee.getMessage());
        }
        return listpop;
    }

    public List<ChartPopVO> OutOneColnum_Time(String pageencode,String starttime, String endtime,
                                               int j)
    {
        //合格率计算方法
        //查看考核中的ExamineExhaustGasPassingRate_Sru
        //配置点位号是具体的值，按默认步长取数据，找出合格的数量（即低于等于标定值）除以总数量就是合格率
        List<ChartPopVO>  listpop=new ArrayList<>();
        List<TablePageConfigVO> list=getTablePageConfig(pageencode,"0","0");
        String points="";
        try {
            for (int i = 0; i < list.size(); i++) {
                ChartPopVO vo=new ChartPopVO();
                vo.setPointValue(list.get(i).getPointvalue());
                vo.setXValue(list.get(i).getPointname());
                if (list.get(i).getPointvalue()!=null && !list.get(i).getPointvalue().equals("")){
                    vo.setYValue(irtdbCokeservice.calculateCoke(list.get(i).getPointvalue(),
                            Integer.parseInt(list.get(i).getIsformula()),
                            starttime,endtime));
                }
                vo.setGrouptype(list.get(i).getGrouptype());
                listpop.add(vo);
            }

        }
        catch (Exception ee)
        {
            //throw new Exception("RtbChartDAO行588数据读取出错：" + ee.getMessage());
        }
        return listpop;
    }*/

    public List<ChartPopVO> OutOneColnumbyRate(String pageencode,String starttime, String endtime,
                                               int j,
                                               double ratio)
    {
        //合格率计算方法
        //查看考核中的ExamineExhaustGasPassingRate_Sru
        //配置点位号是具体的值，按默认步长取数据，找出合格的数量（即低于等于标定值）除以总数量就是合格率
        List<ChartPopVO>  listpop=new ArrayList<>();
        List<TablePageConfigVO> list=getTablePageConfig(pageencode,"0","0");
        List<String> XValue = new ArrayList();
        List<String> YValue = new ArrayList();
        try
        {
            if (list != null)
            {
                for (int i = 0; i < list.size(); i++)
                {
                    ChartPopVO vo=new ChartPopVO();
                    vo.setXValue(list.get(i).getPointname());
                    if (list.get(i).getPointvalue()!=null && !list.get(i).getPointvalue().equals("")){

                        List<ChartPopVO> poplist =
                                iCokeChartpopservice.getCokeRTDBPointList(list.get(i).getPointvalue(),starttime,
                                        endtime);
                        int passingCount = 0;
                        for (int k = 0; k < poplist.size(); k++)
                        {
                            if (Double.parseDouble(poplist.get(k).getYValue()) <= ratio)
                                passingCount++;
                        }
                        double normal = 0;
                        if (poplist.size() > 0)
                            normal =
                                    Double.valueOf(passingCount) / Double.valueOf(poplist.size()) * 100;

                        vo.setYValue(YczdCataUtils.getXsValue(normal, j));
                        listpop.add(vo);
                    }
                    else
                    {

                    }
                }
            }

        }
        catch (Exception ee)
        {
            //throw new Exception("RtbChartDAO行588数据读取出错：" + ee.getMessage());
        }
        return listpop;
    }

    public Object getCokePointPramsCharttime(String[] group,String[] grouptype, String[] charttype, String pageencode,
                                              String starttime,
                                          String endtime, String ismacd, String ismacdset, String redlineset,
                                              Integer xsws,Double dTime,String sorttype) {
        String sql="";

        List<yczdParamVo> pointlists = new ArrayList<>();
        List<yczdParamVo> pointlist = new ArrayList<>();
        String[] stringdevice=null;
        List<ChartPopVO> listnew = OutOneColnum(pageencode, starttime, endtime,dTime);
        for (int i = 0; i < grouptype.length; i++) {
            final String typegroup = grouptype[i];
            List<ChartPopVO> list =
                        listnew.stream().filter(ivo -> ivo.getGrouptype().equals(typegroup)).collect(Collectors.toList());

            yczdParamVo vo = new yczdParamVo();
            //vo.setParameterName(pointlist[i]);
            String[] valueArray = new String[list.size()];
            String[] codeArray = new String[list.size()];
            for (int j = 0; j < list.size(); j++) {
                yczdParamVo ycvo=new yczdParamVo();
                codeArray[j] = list.get(j).getXValue();
                valueArray[j] = YczdCataUtils.getXsValue(Double.parseDouble(list.get(j).getYValue()),xsws);
                ycvo.setParameterName(list.get(j).getXValue());
                ycvo.setDeviceValue( YczdCataUtils.getXsValue(Double.parseDouble(list.get(j).getYValue()),xsws));
                ycvo.setGrouptype(grouptype[i]);
                ycvo.setPointName(list.get(j).getPointValue());
                pointlist.add(ycvo);
            }
            /*vo.setParameterValue(valueArray);

            vo.setParameterName(group[i]);
            vo.setChartType(charttype[i]);
            if (ismacd.equals("1")) {
                vo.setMacdValue(getCokemacd(valueArray));
            }
            if (ismacdset.equals("1")) {
                vo.setMacdValue(getCokemacdset(redlineset, valueArray));
            }
            pointlists.add(vo);
            if (i==0) {
                stringdevice = codeArray;
            }*/
        }
        yczdParamNewVo newVo = new yczdParamNewVo();
        newVo= cokeSortServiceUtils.pointsort(pointlist,group,grouptype,charttype,ismacd,ismacdset,redlineset,starttime,
                endtime,
                sorttype);
            /*if (starttime.isEmpty() && endtime.isEmpty()) {
                starttime = DateTime.now().toString("yyyy-MM-dd 0:00:00");
                endtime = DateTime.now().toString("yyyy-MM-dd HH:mm:ss");
            }*/
       /* newVo.setQuerytime("数据更新时间:" + starttime + " 至 " + endtime);
        newVo.setDeviceSum(stringdevice);
        //newVo.setDeviceCode(stringcode);
        newVo.setYczdParamListVo(pointlists);*/
        return newVo;
        //return ResponseUtil.success(newVo);
    }

    public Object getCokePointPramsChart(String[] group, String[] charttype, String pageencode, String starttime,
                                          String endtime, String ismacd, String ismacdset, String redlineset, Integer xsws) {
        String sql="";

        List<yczdParamVo> pointlists = new ArrayList<>();
        String[] stringdevice=null;
        List<ChartPopVO> listnew=new ArrayList<>();
        for (int i = 0; i < group.length; i++) {
            /*List<ChartPopVO> list= cokeChartPopService.getCokeRTDBPointList(pointlist[i],starttime,endtime);*/
            List<ChartPopVO> list= OutOneColnumbyRate(pageencode,starttime,endtime,2,3);
            yczdParamVo vo=new yczdParamVo();
            //vo.setParameterName(pointlist[i]);
            String[] valueArray = new String[list.size()];
            String[] codeArray = new String[list.size()];
            for (int j = 0; j < list.size(); j++) {
                codeArray[j] = list.get(j).getXValue();
                valueArray[j] = YczdCataUtils.getXsValue(Double.parseDouble(list.get(j).getYValue()),xsws);
            }
            vo.setParameterValue(valueArray);
            if (ismacdset.equals("1")) {
                vo.setMacdValue(getCokemacdset(redlineset, valueArray));
            }
            pointlists.add(vo);
            stringdevice=codeArray;
        }
        yczdParamNewVo newVo = new yczdParamNewVo();
            /*if (starttime.isEmpty() && endtime.isEmpty()) {
                starttime = DateTime.now().toString("yyyy-MM-dd 0:00:00");
                endtime = DateTime.now().toString("yyyy-MM-dd HH:mm:ss");
            }*/
        newVo.setQuerytime("数据更新时间:" + starttime + " 至 " + endtime);
        newVo.setDeviceSum(stringdevice);
        //newVo.setDeviceCode(stringcode);
        newVo.setYczdParamListVo(pointlists);
        return ResponseUtil.success(newVo);
    }

    public Object getCokeHisChart(String[] group, String[] charttype, String pageencode, String starttime,
                                          String endtime, String ismacd, String ismacdset, String redlineset, Integer xsws,Double dTime) {
        String sql="";

        List<yczdParamVo> pointlists = new ArrayList<>();
        String[] stringdevice=null;
        List<ChartPopVO> listnew=new ArrayList<>();
        for (int i = 0; i < group.length; i++) {

            List<ChartPopVO> list= OutOneColnumPetrolYieldCompare(pageencode,starttime,endtime);
            yczdParamVo vo=new yczdParamVo();
            //vo.setParameterName(pointlist[i]);
            String[] valueArray = new String[list.size()];
            String[] codeArray = new String[list.size()];
            for (int j = 0; j < list.size(); j++) {
                codeArray[j] = list.get(j).getXValue();
                valueArray[j] = YczdCataUtils.getXsValue(Double.parseDouble(list.get(j).getYValue()),xsws);
            }
            vo.setParameterValue(valueArray);
            if (ismacdset.equals("1")) {
                vo.setMacdValue(getCokemacdset(redlineset, valueArray));
            }
            pointlists.add(vo);
            stringdevice=codeArray;
        }
        yczdParamNewVo newVo = new yczdParamNewVo();
            /*if (starttime.isEmpty() && endtime.isEmpty()) {
                starttime = DateTime.now().toString("yyyy-MM-dd 0:00:00");
                endtime = DateTime.now().toString("yyyy-MM-dd HH:mm:ss");
            }*/
        newVo.setQuerytime("数据更新时间:" + starttime + " 至 " + endtime);
        newVo.setDeviceSum(stringdevice);
        //newVo.setDeviceCode(stringcode);
        newVo.setYczdParamListVo(pointlists);
        return ResponseUtil.success(newVo);
    }
    public List<ChartPopVO> OutOneColnumPetrolYieldCompare(String pageencode,String starttime, String endtime)
    {
        String starttimes = "";
        String starttimee = "";
        String endtimes = "";
        String endtimee = "";
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM");
        if (starttime.isEmpty() && endtime.isEmpty()) {

            starttimes =
                    simpleDateFormat.format(DateConvertUtils.stringToDate(DateConvertUtils.addMonth(new Date(),
                            0))) + "-01";
            starttimee =
                    DateConvertUtils.add(DateConvertUtils.stringToDate(DateConvertUtils.addMonth(new Date(), 1)), -1);

            endtimes = simpleDateFormat.format(DateConvertUtils.getFirstDayOfMonth(new Date())) + "-01";
            endtimee = DateConvertUtils.add(DateConvertUtils.stringToDate(DateConvertUtils.addMonth(new Date(), 1)), -1);

        } else {
            starttimes = starttime.substring(0, 7) + "-01";
            starttimee =
                    DateConvertUtils.add(DateConvertUtils.stringToDate(DateConvertUtils.addMonth(DateConvertUtils.stringToDate(starttime), 1)), -1);

            endtimes = endtime.substring(0, 7) + "-01";
            endtimee =
                    DateConvertUtils.add(DateConvertUtils.stringToDate(DateConvertUtils.addMonth(DateConvertUtils.stringToDate(endtime), 1)), -1);
        }
        String pointname = "";
        List<ChartPopVO>  listpop=new ArrayList<>();
        List<TablePageConfigVO> list=getTablePageConfig(pageencode,"0","0");
        for (int i = 0; i < list.size(); i++) {
            pointname+="'"+list.get(i).getPointvalue()+"',";
        }
        List<TpointDataHisVO> listhis =
                tpointdatahiscokemapper.GetDifferenceTpointdataHis(pointname.substring(0,pointname.length()-1),
                        starttimes, endtimes, starttimee, endtimee);
        for (int i = 0; i < list.size(); i++) {
            for (int j = 0; j < listhis.size(); j++) {

                if(list.get(i).getPointvalue().equals(listhis.get(j).getPointname()) && listhis.get(j).getTs().equals(DateConvertUtils.getLocalDateMonth(LocalDate.parse(starttime)))){
                    double value1 = listhis.get(j).getVa();
                    for (int k = 0; k <  listhis.size(); k++) {
                        if(list.get(i).getPointvalue().equals(listhis.get(k).getPointname()) && listhis.get(k).getTs().equals(DateConvertUtils.getLocalDateMonth(LocalDate.parse(endtime)))){
                            ChartPopVO vo=new ChartPopVO();
                            vo.setXValue(list.get(i).getPointname());
                            vo.setYValue(YczdCataUtils.getXsValue(value1 - listhis.get(k).getVa(), 1));
                            listpop.add(vo);
                        }
                    }
                }

            }
        }

        return listpop;
    }
    @Override
    public Object GetCokeDesaltingExamineChart(String[] group, String[] charttype, String pageencode, String starttime,
                                             String endtime, String ismacd, String ismacdset, String redlineset, Integer xsws) throws Exception {

        try {
            if (starttime == "" && endtime == "") {
                /*starttime = DateTime.now().getYear()+"-01-01";
                endtime = DateTime.now().toString("yyyy-MM-dd");*/
                /*starttime = "2013-01-01";
                endtime = "2013-07-15";*/
            }

            List<TablePageConfigVO> list =
                    getTablePageConfig(pageencode,"2","0").stream().filter(ivo ->ivo.getMax()!=null && !ivo.getMax().equals("")).collect(Collectors.toList());

            Map<String, List<TablePageConfigVO>> exlist = iExamineServiceCoke.getDesaltingExaminelist(group,
                    pageencode,
                    list,
                    starttime, endtime);
            List<yczdParamVo> pointlist = new ArrayList<>();
            String[] stringdevice = null;
            for (Map.Entry<String, List<TablePageConfigVO>> item : exlist.entrySet()
            ) {
                yczdParamVo vo = new yczdParamVo();
                if (group.length != exlist.size()) {
                    vo.setParameterName(group[0]);
                    vo.setChartType(charttype[0]);
                } else {
                    vo.setParameterName(item.getKey());
                    vo.setChartType("Bar");
                }
                String[] stringArray = new String[item.getValue().size()];
                String[] valueArray = new String[item.getValue().size()];
                for (int i = 0; i < item.getValue().size(); i++) {
                    if (xsws != -1) {
                        if (item.getValue().get(i).getScore() != null) {
                            String value = getXsValue(item.getValue().get(i).getScore(), xsws);
                            valueArray[i] = value;
                        } else {
                            valueArray[i] = getXsValue(0.0, xsws);
                        }
                    }
                    String device =
                            item.getValue().get(i).getDevicename()!=null && !item.getValue().get(i).getDevicename().equals("")?
                            item.getValue().get(i).getDevicename():item.getValue().get(i).getDwname();
                    stringArray[i] = device;
                }
                vo.setParameterValue(valueArray);
                if (ismacd.equals("1")) {
                    vo.setMacdValue(getCokemacd(valueArray));
                }
                if (ismacdset.equals("1")) {
                    vo.setMacdValue(getCokemacdset(redlineset, valueArray));
                }
                pointlist.add(vo);
                stringdevice = stringArray;
            }

            yczdParamNewVo newVo = new yczdParamNewVo();
            if (starttime.isEmpty() && endtime.isEmpty()) {
                starttime = DateTime.now().toString("yyyy-MM-dd 0:00:00");
                endtime = DateTime.now().toString("yyyy-MM-dd HH:mm:ss");
            }
            newVo.setQuerytime("数据更新时间:" + starttime + " 至 " + endtime);
            newVo.setDeviceSum(stringdevice);
            newVo.setYczdParamListVo(pointlist);
            return ResponseUtil.success(newVo);

        } catch (Exception e) {
            // TODO: handle exception
            //log.error(e.getMessage());
            return ResponseUtil.error(ResponseCode.ERROR_CODE, e.getMessage());
        }
        //return ResponseUtil.success(null);
    }
    @Override
    public Object GetCokeStableRatePic2Chart(String[] group, String[] charttype, String pageencode, String starttime,
                                                 String endtime, String ismacd, String ismacdset, String redlineset,
                                                 Integer xsws,String timetype,String type,String zztype,String sorttype
    ) throws Exception {

        try {
            List<TstableratePrameVO> list = iCokeStableRatePic2Service.getCataStableList(starttime,endtime,timetype,
                    type,zztype);

            List<yczdParamVo> pointlist = new ArrayList<>();
            String[] stringArray = new String[list.size()];
            String[] valueArray = new String[list.size()];
            String[] stringdevice = null;
            for (int i = 0; i < list.size(); i++) {
                yczdParamVo vo = new yczdParamVo();
                String value = getXsValue(Double.parseDouble(list.get(i).getTvalue()!=null?list.get(i).getTvalue():"0"), xsws);
                valueArray[i] = YczdCataUtils.getXsValue(Double.parseDouble(value),xsws);;

                String device = list.get(i).getQy();
                stringArray[i] = device;
                vo.setGrouptype("1");
                if("".equals(type)){
                    vo.setParameterName("装置月度平稳率");

                }else {
                    vo.setParameterName(iCokeStableRatePic2Service.GetDesc(type));
                }
                vo.setDeviceValue(value);
                vo.setPointName(device);
                vo.setParameterValue(valueArray);
                pointlist.add(vo);

            }


            yczdParamNewVo newVo = new yczdParamNewVo();newVo = cokeSortServiceUtils.pointsortzzpwl(pointlist, new String[]{"1"}, group,
             charttype, ismacd, ismacdset,
                    redlineset,
                    starttime, endtime,
                    sorttype);
            return newVo;
            //return ResponseUtil.success(newVo);
        } catch (Exception e) {
            // TODO: handle exception
            //log.error(e.getMessage());
            return ResponseUtil.error(ResponseCode.ERROR_CODE, e.getMessage());
        }
        //return pointlist;
    }
    public Object getCokeRateChart(String[] group, String[] charttype, String pageencode, String starttime,
                                  String endtime, String ismacd, String ismacdset, String redlineset, Integer xsws,Double dTime) {
        String sql="";

        List<yczdParamVo> pointlists = new ArrayList<>();
        String[] stringdevice=null;
        List<ChartPopVO> listnew=new ArrayList<>();
        for (int i = 0; i < group.length; i++) {

           /* List<ChartPopVO> list= OutOneColnumPetrolYieldCompare(pageencode,starttime,endtime);*/
            List<ProductDistributionAddHistoryVO> list=productDistributionMapper.GetProductDistributionAddHistory(starttime,endtime);
            yczdParamVo vo=new yczdParamVo();
            //vo.setParameterName(pointlist[i]);
            String[] nameArray = new String[list.size()];
            String[] valueArray = new String[list.size()];
            for (int j = 0; j < list.size(); j++) {
                nameArray[j] = list.get(j).getDeviceName();
                if (i==1){
                    valueArray[j] = YczdCataUtils.getXsValue(Double.parseDouble(list.get(j).getJtValue()),xsws);
                }else {
                    valueArray[j] = YczdCataUtils.getXsValue(Double.parseDouble(list.get(j).getJtValueSj()),xsws);
                }

            }
            vo.setParameterName(group[i]);
            vo.setParameterValue(valueArray);
            if (ismacdset.equals("1")) {
                vo.setMacdValue(getCokemacdset(redlineset, valueArray));
            }
            pointlists.add(vo);
            stringdevice=nameArray;
        }
        yczdParamNewVo newVo = new yczdParamNewVo();
            /*if (starttime.isEmpty() && endtime.isEmpty()) {
                starttime = DateTime.now().toString("yyyy-MM-dd 0:00:00");
                endtime = DateTime.now().toString("yyyy-MM-dd HH:mm:ss");
            }*/
        newVo.setQuerytime("数据更新时间:" + starttime + " 至 " + endtime);
        newVo.setDeviceSum(stringdevice);
        //newVo.setDeviceCode(stringcode);
        newVo.setYczdParamListVo(pointlists);
        return newVo;
        //return ResponseUtil.success(newVo);
    }
    /**
     * 装置竞赛排名的图表数据
     *
     * @param group
     * @param charttype
     * @param pageencode
     * @param starttime
     * @param endtime
     * @param ismacd
     * @param ismacdset
     * @param redlineset
     * @param xsws
     * @return
     * @throws Exception
     */
    @Override
    public Object GetCokeGanttChartList(String[] group, String[] charttype, String pageencode, String starttime,
                                         String endtime, String ismacd, String ismacdset, String redlineset,
                                         Integer xsws,String dtype) throws Exception {

        try {

            List<GantWaterVO> list=new ArrayList<>();
            starttime = LocalDate.of(LocalDate.now().getYear(),01,01).toString() ;
            endtime = DateTime.now().toString("yyyy-MM-dd");
            list=CreateChart(starttime,endtime,dtype);

            return list;
            //return ResponseUtil.success(list);

        } catch (Exception e) {
            // TODO: handle exception
            //log.error(e.getMessage());
            return ResponseUtil.error(ResponseCode.ERROR_CODE, e.getMessage());
        }
        //return ResponseUtil.success(null);
    }

    private List<GantWaterVO> CreateChart(String starttime,String endtime,String dtype){
        List<TablePageConfigVO> list=getTablePageConfig(dtype,"0","0");
        double imgWidth = 0.7;
        if (list.size() > 2 && list.size() <= 5)
        {
            imgWidth = 0.5;
        }
        if (list.size() <= 2)
        {
            imgWidth = 0.3;
        }
        String content=tchartConfigCokeMapper.GetTchartConfig("16").get(0).getContent();
        if (content.equals("0"))
        {
            //向前12个月
            endtime = DateTime.now().toString();
            starttime =
                    DateConvertUtils.add(DateConvertUtils.stringToDate(DateConvertUtils.addMonth(DateConvertUtils.stringToDate(endtime), -12)), 0);
        }
        else if(content.equals("1"))
        {
            //当年,
            starttime =LocalDate.of(LocalDate.now().getYear(),01,01).toString() ;
            endtime = LocalDate.of(LocalDate.now().getYear(),12,31).toString();
        }
        List<GantWaterFallVO> gantlist=gantWaterFallCokeMapper.getGanttChart(dtype);
        List<GantWaterVO> listgant=new ArrayList<>();

        for (int i = 0; i < list.size(); i++) {
            double stopDays=0;
            GantWaterVO vo=new GantWaterVO();
            vo.setDeviceName(list.get(i).getCompany());
            vo.setOnDay(list.get(i).getIndex());
            for (int j = 0; j < gantlist.size(); j++) {            /*vo.setStarttime(gantlist.get(j).getStarttime());
                vo.setEndtime(gantlist.get(j).getEndtime());*/
                if(list.get(i).getDevicename()!=null && list.get(i).getDevicename().equals(gantlist.get(j).getDevice())){
                    stopDays +=Duration.between(DateConvertUtils.getLocalDateTime(gantlist.get(j).getEndtime()),
                            DateConvertUtils.getLocalDateTime(gantlist.get(j).getStarttime())).toDays();
                }
            }
            vo.setOffDay(Double.toString(stopDays));
            vo.setToolTip(starttime+"至"+endtime+",停工"+stopDays+"天");
            listgant.add(vo);
        }

        return listgant;
    }
    public Object getProductDistributionAllChart(String[] group,String[] grouptype, String[] charttype, String pageencode,
                                             String starttime,
                                             String endtime, String ismacd, String ismacdset, String redlineset,
                                             Integer xsws,Double dTime) {
        String sql="";

        List<yczdParamVo> pointlists = new ArrayList<>();
        String[] stringdevice=null;
        List<ProductDistributionAddHistoryVO> list = productDistributionMapper.GetProductDistributionAll(starttime, endtime);
        for (int i = 0; i < group.length; i++) {

            if(grouptype.length!=0) {
                yczdParamVo vo = new yczdParamVo();
                //vo.setParameterName(pointlist[i]);
                String[] valueArray = new String[list.size()];
                String[] valuesjArray = new String[list.size()];
                String[] codeArray = new String[list.size()];

                for (int j = 0; j < list.size(); j++) {
                    if (grouptype[i].equals("jt")) {
                        codeArray[j] = list.get(j).getDeviceName();
                        valueArray[j] = YczdCataUtils.getXsValue(Double.parseDouble(list.get(j).getJtValue()),xsws);
                        valuesjArray[j] = YczdCataUtils.getXsValue(Double.parseDouble(list.get(j).getJtValueSj()),xsws);
                    }
                    if (grouptype[i].equals("ly")) {
                        codeArray[j] = list.get(j).getDeviceName();
                        valueArray[j] = YczdCataUtils.getXsValue(Double.parseDouble(list.get(j).getLyValue()),xsws);
                        valuesjArray[j] = YczdCataUtils.getXsValue(Double.parseDouble(list.get(j).getLyValueSj()),xsws);
                    }
                    if (grouptype[i].equals("cy")) {
                        codeArray[j] = list.get(j).getDeviceName();
                        valueArray[j] = YczdCataUtils.getXsValue(Double.parseDouble(list.get(j).getCyValue()),xsws);
                        valuesjArray[j] = YczdCataUtils.getXsValue(Double.parseDouble(list.get(j).getCyValueSj()),xsws);
                    }
                    if (grouptype[i].equals("qy")) {
                        codeArray[j] = list.get(j).getDeviceName();
                        valueArray[j] = YczdCataUtils.getXsValue(Double.parseDouble(list.get(j).getQyValue()),xsws);
                        valuesjArray[j] = YczdCataUtils.getXsValue(Double.parseDouble(list.get(j).getQyValueSj()),xsws);
                    }
                    if (grouptype[i].equals("yhq")) {
                        codeArray[j] = list.get(j).getDeviceName();
                        valueArray[j] = YczdCataUtils.getXsValue(Double.parseDouble(list.get(j).getYhqValue()),xsws);
                        valuesjArray[j] = YczdCataUtils.getXsValue(Double.parseDouble(list.get(j).getYhqValueSj()),xsws);
                    }
                    if (grouptype[i].equals("gq")) {
                        codeArray[j] = list.get(j).getDeviceName();
                        valueArray[j] = YczdCataUtils.getXsValue(Double.parseDouble(list.get(j).getGqValue()),xsws);
                        valuesjArray[j] = YczdCataUtils.getXsValue(Double.parseDouble(list.get(j).getGqValueSj()),xsws);
                    }
                }

                vo.setParameterValue(valueArray);
                vo.setParameterValuesj(valuesjArray);
                vo.setParameterName(group[i]);
                vo.setChartType(charttype[i]);
                if (ismacd.equals("1")) {
                    vo.setMacdValue(getCokemacd(valueArray));
                }
                if (ismacdset.equals("1")) {
                    vo.setMacdValue(getCokemacdset(redlineset, valueArray));
                }
                pointlists.add(vo);
                if (i == 0) {
                    stringdevice = codeArray;
                }
            }
        }
        yczdParamNewVo newVo = new yczdParamNewVo();
            /*if (starttime.isEmpty() && endtime.isEmpty()) {
                starttime = DateTime.now().toString("yyyy-MM-dd 0:00:00");
                endtime = DateTime.now().toString("yyyy-MM-dd HH:mm:ss");
            }*/
        newVo.setQuerytime("数据更新时间:" + starttime + " 至 " + endtime);
        newVo.setDeviceSum(stringdevice);
        //newVo.setDeviceCode(stringcode);
        newVo.setYczdParamListVo(pointlists);
        return newVo;
        //return ResponseUtil.success(newVo);
    }
    public Object getCokeCoefficientRadarChart(String[] group,String[] grouptype, String[] charttype, String pageencode,
                                             String starttime,
                                             String endtime, String ismacd, String ismacdset, String redlineset,
                                             Integer xsws,Double dTime) {
        String sql="";
        List<String> iResultName = new ArrayList();
        List<String> iResultY1 = new ArrayList();
        List<String> iResultY2 = new ArrayList();
        List<String> iResultY2_1 = new ArrayList();
        List<String> iResultY2_2 = new ArrayList();
        List<String> iReama = new ArrayList();
        List<yczdParamVo> pointlists = new ArrayList<>();
        String[] stringdevice=null;
        List<ChartPopVO> CokeCoefficientRadarlist = OutOneColnum(pageencode, starttime, endtime,dTime);
        List<ChartPopVO> CokeRatelist = OutOneColnum(pageencode, starttime, endtime,dTime);
        List<ChartPopVO> CarbonResiduelist = OutOneColnum(pageencode, starttime, endtime,dTime);
        for (int i = 0; i < CokeCoefficientRadarlist.size(); i++) {
            for (int j = 0; j < CokeRatelist.size(); j++) {
                if(CokeCoefficientRadarlist.get(i).getXValue().equals(CokeRatelist.get(j).getXValue())){
                    iResultName.add(CokeCoefficientRadarlist.get(i).getXValue());
                }

            }
        }
        for (int i = 0; i < iResultName.size(); i++) {
            for (int j = 0; j < CokeCoefficientRadarlist.size(); j++) {
                if (iResultName.get(i).equals(CokeCoefficientRadarlist.get(j).getXValue())){
                    iResultY1.add(CokeCoefficientRadarlist.get(j).getYValue());
                }
            }

        }
        for (int i = 0; i < iResultName.size(); i++) {
            for (int j = 0; j < CokeRatelist.size(); j++) {
                if (iResultName.get(i).equals(CokeRatelist.get(j).getXValue())){
                    iResultY2_1.add(CokeRatelist.get(j).getYValue());
                }
            }

        }
        for (int i = 0; i < iResultName.size(); i++) {
            for (int j = 0; j < CarbonResiduelist.size(); j++) {
                if (iResultName.get(i).equals(CarbonResiduelist.get(j).getXValue())){
                    iResultY2_2.add(CarbonResiduelist.get(j).getYValue());
                }
            }
        }
        for (int i = 0; i < iResultName.size(); i++)
        {
            double num = Double.parseDouble(iResultY1.get(i));
            double num1 = Double.parseDouble(iResultY2_1.get(i));
            double num2 = Double.parseDouble(iResultY2_2.get(i));
            double number = 0;

            if (num <= 0 || num1 <= 0 || num2 <= 0)
            {
                if (!iReama.contains(i))
                {
                    iReama.add(Integer.toString(i));
                }
            }
            else if (num > 0 && num1 > 0 && num2 > 0)
            {
                number = num1 / num2;
            }
            iResultY2.add(YczdCataUtils.getXsValue(number,2));

        }
        for (int i = iReama.size() - 1; i >= 0; i--)
        {
            iResultName.remove(iReama.get(i));
            iResultY1.remove(iReama.get(i));
            iResultY2.remove(iReama.get(i));
        }

        for (int i = 0; i < group.length; i++) {

            yczdParamVo vo = new yczdParamVo();
            if(group[i].equals("生焦指数")){
                String[] valueArray = new String[iResultY1.size()];
                String[] codeArray = new String[iResultY1.size()];
                for (int j = 0; j < iResultY1.size(); j++) {
                    codeArray[j] = iResultName.get(j);
                    valueArray[j] = YczdCataUtils.getXsValue(Double.parseDouble(iResultY1.get(j)),xsws);
                }
                vo.setParameterValue(valueArray);

                vo.setParameterName(group[i]);
                vo.setChartType(charttype[i]);
                if (ismacd.equals("1")) {
                    vo.setMacdValue(getCokemacd(valueArray));
                }
                if (ismacdset.equals("1")) {
                    vo.setMacdValue(getCokemacdset(redlineset, valueArray));
                }
                pointlists.add(vo);
                if (i==0) {
                    stringdevice = codeArray;
                }
            }
            if(group[i].equals("生焦系数")){
                String[] valueArray = new String[iResultY1.size()];
                String[] codeArray = new String[iResultY1.size()];
                for (int j = 0; j < iResultY2.size(); j++) {
                    codeArray[j] = iResultName.get(j);
                    valueArray[j] = YczdCataUtils.getXsValue(Double.parseDouble(iResultY2.get(j)),xsws);
                }
                vo.setParameterValue(valueArray);

                vo.setParameterName(group[i]);
                vo.setChartType(charttype[i]);
                if (ismacd.equals("1")) {
                    vo.setMacdValue(getCokemacd(valueArray));
                }
                if (ismacdset.equals("1")) {
                    vo.setMacdValue(getCokemacdset(redlineset, valueArray));
                }
                pointlists.add(vo);
                if (i==0) {
                    stringdevice = codeArray;
                }
            }
        }
        yczdParamNewVo newVo = new yczdParamNewVo();
            /*if (starttime.isEmpty() && endtime.isEmpty()) {
                starttime = DateTime.now().toString("yyyy-MM-dd 0:00:00");
                endtime = DateTime.now().toString("yyyy-MM-dd HH:mm:ss");
            }*/
        newVo.setQuerytime("数据更新时间:" + starttime + " 至 " + endtime);
        newVo.setDeviceSum(stringdevice);
        //newVo.setDeviceCode(stringcode);
        newVo.setYczdParamListVo(pointlists);
        return newVo;
        //return ResponseUtil.success(newVo);
    }

    public Object getCokeFactorChart(String[] group,String[] grouptype, String[] charttype, String pageencode,
                                               String starttime,
                                               String endtime, String ismacd, String ismacdset, String redlineset,
                                               Integer xsws,Double dTime) {
        String sql="";
        List<String> iResultName = new ArrayList();
        List<String> iResultY1 = new ArrayList();
        List<String> iResultY2 = new ArrayList();
        List<String> iResultY2_1 = new ArrayList();
        List<String> iResultY2_2 = new ArrayList();
        List<String> iReama = new ArrayList();
        List<yczdParamVo> pointlists = new ArrayList<>();
        String[] stringdevice=null;
        List<ChartPopVO> CarbonResiduelist = OutOneColnum("CarbonResidue", starttime, endtime,dTime);
        List<ChartPopVO> CokeRatelist = OutOneColnum("CokeCoefficientRadar", starttime, endtime,dTime);

        List<Double> results = GetMaxMinAvg( CarbonResiduelist,  CokeRatelist);

        //计算图的起点值和终点值
        double startX = (double )results.get(0)-1;
        double startY = (double)results.get(1)-1;
        double endX = (double)results.get(2)+1;
        double endY = (double)results.get(3)+1;

        //TODO:根据起点值和股份的值计算曲线y=ax+b的系数a,b
        double[] prammer = new double[2]; //prammer[0]为a  prammer[1]为b
        prammer = Getprammer(startX, startY, (double)results.get(4), (double)results.get(5));
        double endXNew = 0 ;
        if (prammer[0] != 0) {
            endXNew = (endY - prammer[1]) / prammer[0];
        }
        //(startX,startY),(endXNew,endY) 斜线的起止点
        double endYNew = prammer[0] * 20 + prammer[1];
        //(20,startY),(20,endYNew)为斜线的起止点


        //斜线数据源
        List<Double> X1 = new ArrayList ();
        List<Double> Y1 = new ArrayList();
        X1.add(startX);
        X1.add(endXNew);
        Y1.add(startY);
        Y1.add(endY);
        //直线数据源
        List<Double> X2 = new ArrayList();
        List<Double> Y2 = new ArrayList();
        X2.add(20.0);
        X2.add(20.0);
        Y2.add(startY);
        Y2.add(endYNew);

        for (int i = 0; i < group.length; i++) {

            yczdParamVo vo = new yczdParamVo();
            if(group[i].equals("生焦率%")){
                String[] valueArray = new String[X1.size()];
                String[] codeArray = new String[X1.size()];
                for (int j = 0; j < X1.size(); j++) {
                    codeArray[j] = X1.get(j).toString();
                    valueArray[j] = YczdCataUtils.getXsValue(Double.parseDouble(Y1.get(j).toString()),xsws);
                }
                vo.setParameterXValue(codeArray);
                vo.setParameterValue(valueArray);
                vo.setParameterName(group[i]);
                vo.setChartType(charttype[i]);
                if (ismacd.equals("1")) {
                    vo.setMacdValue(getCokemacd(valueArray));
                }
                if (ismacdset.equals("1")) {
                    vo.setMacdValue(getCokemacdset(redlineset, valueArray));
                }
                pointlists.add(vo);
                if (i==0) {
                    stringdevice = codeArray;
                }
            }
            if(group[i].equals("原料残炭%")){
                String[] valueArray = new String[X2.size()];
                String[] codeArray = new String[X2.size()];
                for (int j = 0; j < X2.size(); j++) {
                    codeArray[j] = X2.get(j).toString();
                    valueArray[j] = YczdCataUtils.getXsValue(Double.parseDouble(Y2.get(j).toString()),xsws);
                }
                vo.setParameterXValue(codeArray);
                vo.setParameterValue(valueArray);
                vo.setParameterName(group[i]);
                vo.setChartType(charttype[i]);
                if (ismacd.equals("1")) {
                    vo.setMacdValue(getCokemacd(valueArray));
                }
                if (ismacdset.equals("1")) {
                    vo.setMacdValue(getCokemacdset(redlineset, valueArray));
                }
                pointlists.add(vo);
                if (i==0) {
                    stringdevice = codeArray;
                }
            }
        }
        yczdParamNewVo newVo = new yczdParamNewVo();
            /*if (starttime.isEmpty() && endtime.isEmpty()) {
                starttime = DateTime.now().toString("yyyy-MM-dd 0:00:00");
                endtime = DateTime.now().toString("yyyy-MM-dd HH:mm:ss");
            }*/
        newVo.setQuerytime("数据更新时间:" + starttime + " 至 " + endtime);
        //newVo.setDeviceSum(stringdevice);
        //newVo.setDeviceCode(stringcode);
        newVo.setYczdParamListVo(pointlists);
        return newVo;
        //return ResponseUtil.success(newVo);
    }

    @Override
    public Object GetLowHitChart(String[] groupcode, String[] charttype, String pageencode, String starttime, String endtime, String ismacd, String ismacdset, String redlineset, Integer xsws, String sorttype, String issum, String low) {
        try {


            List<yczdParamVo> pointlist = new ArrayList<>();
            String[] type=new String[]{"1","2"};
            List<String> devicename = new ArrayList();
            List<String> value1 = new ArrayList();
            List<String> value2 = new ArrayList();
            if("low".equals(low)) {
                devicename.add("燕山");
                devicename.add("天津2");
                devicename.add("沧州");
                devicename.add("洛阳");
                devicename.add("齐鲁2");
                devicename.add("齐鲁3");
                devicename.add("济南1");
                devicename.add("安庆");
                devicename.add("九江");
                devicename.add("武汉2");
                devicename.add("长岭");
                devicename.add("荆门");
                devicename.add("金陵2");
                devicename.add("扬子2");
                devicename.add("高桥2");
                devicename.add("上海2");
                devicename.add("镇海1");
                devicename.add("镇海2");
                devicename.add("广州3");
                devicename.add("茂名1");
                devicename.add("茂名2");
                devicename.add("北海");
                devicename.add("塔河2");
                devicename.add("青炼");

                value1.add("0.125");
                value1.add("0.081");
                value1.add("0.153");
                value1.add("0.015");
                value1.add("0.12");
                value1.add("0.153");
                value1.add("0.075");
                value1.add("0.012");
                value1.add("0.128");
                value1.add("0.058");
                value1.add("0.181");
                value1.add("0.173");
                value1.add("0.045");
                value1.add("0.052");
                value1.add("0.022");
                value1.add("0.172");
                value1.add("0.017");
                value1.add("0.195");
                value1.add("0.167");
                value1.add("0.161");
                value1.add("0.114");
                value1.add("0.109");
                value1.add("0.156");
                value1.add("0.032");

                value2.add("0.145");
                value2.add("0.09882");
                value2.add("0.17289");
                value2.add("0.02023");
                value2.add("0.15");
                value2.add("0.18972");
                value2.add("0.093");
                value2.add("0.02396");
                value2.add("0.15744");
                value2.add("0.07308");
                value2.add("0.2353");
                value2.add("0.19376");
                value2.add("0.0558");
                value2.add("0.06448");
                value2.add("0.02772");
                value2.add("0.1978");
                value2.add("0.01624");
                value2.add("0.25155");
                value2.add("0.20875");
                value2.add("0.2093");
                value2.add("0.14478");
                value2.add("0.13625");
                value2.add("0.19968");
                value2.add("0.03776");
            }else {
                devicename.add("燕山");
                devicename.add("天津2");
                devicename.add("沧州");
                devicename.add("洛阳");
                devicename.add("齐鲁2");
                devicename.add("齐鲁3");
                devicename.add("济南1");
                devicename.add("安庆");
                devicename.add("九江");
                devicename.add("武汉2");
                devicename.add("长岭");
                devicename.add("荆门");
                devicename.add("金陵2");
                devicename.add("扬子2");
                devicename.add("高桥2");
                devicename.add("上海2");
                devicename.add("镇海1");
                devicename.add("镇海2");
                devicename.add("广州3");
                devicename.add("茂名1");
                devicename.add("茂名2");
                devicename.add("北海");
                devicename.add("塔河2");
                devicename.add("青炼");

                value1.add("0.031");
                value1.add("0.603");
                value1.add("0.118");
                value1.add("0.607");
                value1.add("0.043");
                value1.add("0.459");
                value1.add("0.278");
                value1.add("0.621");
                value1.add("0.301");
                value1.add("0.216");
                value1.add("0.345");
                value1.add("0.056");
                value1.add("0.121");
                value1.add("0.099");
                value1.add("0.621");
                value1.add("0.064");
                value1.add("0.626");
                value1.add("0.211");
                value1.add("0.533");
                value1.add("0.04");
                value1.add("0.422");
                value1.add("0.426");
                value1.add("0.094");
                value1.add("0.457");

                value2.add("0.02511");
                value2.add("0.47637");
                value2.add("0.14514");
                value2.add("0.69198");
                value2.add("0.03956");
                value2.add("0.48654");
                value2.add("0.34472");
                value2.add("0.58374");
                value2.add("0.21973");
                value2.add("0.23328");
                value2.add("0.276");
                value2.add("0.04368");
                value2.add("0.1573");
                value2.add("0.09999");
                value2.add("0.72036");
                value2.add("0.0448");
                value2.add("0.60722");
                value2.add("0.19834");
                value2.add("0.4797");
                value2.add("0.0316");
                value2.add("0.37558");
                value2.add("0.45582");
                value2.add("0.0987");
                value2.add("0.42958");
            }
            for (int m = 0; m < groupcode.length; m++) {
                for (int i = 0; i < devicename.size(); i++) {
                    yczdParamVo vo = new yczdParamVo();
                    vo.setGrouptype(groupcode[m]);
                    vo.setParameterName(devicename.get(i));
                    if(m==0) {
                        vo.setDeviceValue(value1.get(i));
                    }else {
                        vo.setDeviceValue(value2.get(i));
                    }
                    pointlist.add(vo);
                }
            }
            yczdParamNewVo newVo = new yczdParamNewVo();
            newVo = cokeSortServiceUtils.pointsort(pointlist, groupcode, groupcode, charttype, ismacd, ismacdset, redlineset,
                    starttime, endtime,
                    sorttype);

            return newVo;
            //return ResponseUtil.success(newVo);
        } catch (Exception e) {
            // TODO: handle exception
            //log.error(e.getMessage());
            return ResponseUtil.error(ResponseCode.ERROR_CODE, e.getMessage());
        }
        //return pointlist;
    }

    //TODO：求X Y 的最大最小值  X Y 的平局值 返回含6个double值列表
    private List<Double> GetMaxMinAvg(List<ChartPopVO> rtcol1, List<ChartPopVO> rtcol2) {
        List<Double> results = new ArrayList();
        double[] tempX = new double[3];
        double[] tempY = new double[3];
        tempX = GetMaxMinAvg(rtcol1);
        tempY = GetMaxMinAvg(rtcol2);
        results.add(tempX[0]);//X最小值
        results.add(tempY[0]);//Y最小值
        results.add(tempX[1]);//X最大值
        results.add(tempY[1]);//Y最大值
        results.add(tempX[2]);//X平均值
        results.add(tempY[2]);//Y平均值
        return results;
    }
    //TODO:查找最值，平局值，数组中依次存入最小值，最大值，平均值
    private double[] GetMaxMinAvg(List<ChartPopVO> rtcol)
    {
        double[] temp = new double[3];
        double low = Double.MAX_VALUE;
        double high = Double.MIN_VALUE;
        double sum = 0;
        for (int i = 0; i <rtcol.size(); i++) {
            double temp1=Double.parseDouble(rtcol.get(i).getYValue());
            if (temp1 <= low) { low = temp1;}
            if (temp1 >= high) { high = temp1;}
            sum += temp1;
        }
        temp[0] = low;
        temp[1] = high;
        temp[2] = sum / rtcol.size();
        return temp;
    }
    //求直线方程
    private double[] Getprammer(double startX, double startY, double midX, double midY) {
        double[] prammer = new double[2];
        double a = (midY - startY) / (midX - startX);
        double b = startY - a * startX;
        prammer[0] = a;
        prammer[1] = b;
        return prammer;
    }
}

