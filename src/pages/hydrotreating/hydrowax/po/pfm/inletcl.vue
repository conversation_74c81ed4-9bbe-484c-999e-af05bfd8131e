<script lang="ts" setup>
import { ref,reactive, nextTick, onMounted,onUnmounted } from "vue";
import { message } from "ant-design-vue";
import * as echarts from "echarts";
import dayjs from "dayjs";
import type {Dayjs} from "dayjs";
import {isEmpty,round} from "lodash";
import CMPTableCollapse from "@/components/hydro/wax/TableCollapse.vue";

import useHydroStore from "@/store/hydro.ts";
const storeHydro = useHydroStore();
import useInteractStore from "@/store/interact";
const storeInteract = useInteractStore();
let yczdParamListVo=[];
document.title="原料氯离子-工艺防腐-生产运行概览-蜡油加氢装置";
type EChartsOption = echarts.EChartsOption;
const option1: EChartsOption = reactive({
  title:{
    subtext:"",
    subtextStyle:{
      align:"right",
      lineHeight:16
    },
    right: 160,
    top: -10
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
      crossStyle: {
        color: '#999'
      }
    }
  },
  legend:{
    right:120,
    top: 30,
    itemGap:20
  },
  xAxis: [
    {
      type: 'category',
      axisPointer: {
        type: 'shadow'
      },
      axisTick:{
        alignWithLabel:true
      },
      axisLabel:{
        // rotate:270,
        formatter: function (value) {
          return value.split('').join('\n')
        }
      }
    }
  ],
  yAxis: [
    {
      type: 'value',
      min:0,
      //nameLocation:"center",
      //nameRotate:-270,
      //nameGap:40,
      name:"mg/kg",
      axiosLine:{
        show:true
      },
      axisLine: {
        show: true
      },
      axisTick: {
        show: true
      }
    }
  ],
  series: [
    {
      type: 'bar',
      data: [],
      barMaxWidth: 25,
      label:{
        show:true,
        position:"top",
        formatter:function(v){
          return round(v.value,2)
        }
      },
      markLine:{
        symbol: 'none',
        silent:true,
        label:{
          color:"red",
          //position:"end",
          show:true,
          //formatter:"报警值：{c}"
          position:"insideEndBottom",
          formatter:"{c}"
        },
        lineStyle:{
          type:"solid",
          color:'red',
        },
        data:[
          {
            symbol:false,
            yAxis:"0",
          }
        ]
      }
    }
  ]
})
let myChart: any = null;
function initChart(){
  const doc: any = document,
    chartDom1: any = doc.getElementById("chartId");

  if (!myChart) {
    myChart = echarts.init(chartDom1);
    myChart.setOption(option1);
  }
  myChart.on("click",(params)=>{
    // console.log("====click===",params,"====",deviceCalculationList);
    const dataIndex = params.dataIndex,
      seriesIndex = params.seriesIndex,
      data = yczdParamListVo[seriesIndex]||{deviceCode:[]},
      listDeviceCode = data&&data.deviceCode||[],
      deviceCode = listDeviceCode[dataIndex]||"",
      deviceName = params.name,
      date = rankDate.value,
      beginDate = date&&date[0].format("YYYY-MM-DD")||"",
      endDate = date&&date[1].format("YYYY-MM-DD")||"";
      // console.log("====click===",dataIndex,"====",seriesIndex,"====",deviceCode,"====",data);
      storeInteract.showChartDv({
        deviceType:"hydro",
	deviceType2:"HydroWax",
        deviceCode,
        deviceName,
        beginDate,
        endDate,
        pageCode:"INLETCLPop",
      });
  })
  nextTick(() => {
    window.addEventListener('resize', function () {
      myChart.resize();
    })
  })
}
function fetchChartData(){
  myChart.showLoading();
  const date = rankDate.value,
    beginDate = date&&date[0].format("YYYY-MM-DD")||"",
    endDate = date&&date[1].format("YYYY-MM-DD")||"";
  storeHydro.fetchPOChartData({
    pageCode:"INLETCL",
    ismacdset:1,
    beginDate:beginDate,
    endDate:endDate,
    deviceType:"HydroWax"
  }).then((resp)=>{
    // console.log("===ph_value=resp==",resp);
    updateTableData(resp);
  }).catch(err=>{
    // console.log("==err==",err);
    message.error((err&&(err.statusText||err.message||err.msg||err))||"数据错误!");
  });
}

function updateTableData(data){
  if(!myChart||isEmpty(data)) return;
  yczdParamListVo = data.yczdParamListVo||[];
  const opts = myChart.getOption(),
    data1 = yczdParamListVo[0]||{parameterName:"",parameterValue:[]};

  opts.title[0].subtext = data.querytime;
  opts.xAxis[0].data = data.deviceSum;
  opts.series[0].name=data1.parameterName;
  opts.series[0].data= data1.parameterValue;
  opts.series[0].markLine.data[0].yAxis = toString(floor(data1.macdValue[0],3));
  // console.log("===",ret.retResult.yczdParamListVo[0].parameterValue);
  myChart.setOption(opts);
  myChart.hideLoading();
}

onMounted(()=>{
  initChart();
  fetchChartData();
})
onUnmounted(() => {
  myChart?.clear();
  myChart?.dispose();
  myChart = null;
});


// const rankDate = ref<[Dayjs,Dayjs]>([dayjs().subtract(1,"week"),dayjs()]);
const rankDate = ref<any>(null);
function doSearch(){
  fetchChartData();
}
</script>
<template>
<div class="page ph_value">
  <div class="chart" id="chartId"></div>
  <div class="filter">
    <a-range-picker class="field"  v-model:value="rankDate"></a-range-picker>
    <a-button class="field" @click.stop="doSearch">确定</a-button>
  </div>
  <div class="tables">
    <CMPTable-Collapse />
  </div>
</div>
</template>
<style lang="less" scoped>
.page.ph_value{
  >.chart{
    width:100%;height:600px;
  }
  >.filter{
    display:flex;
    justify-content: center;
    >.field{
      margin-left:10px;
      &:first-child{margin-left:0;}
    }
  }

  >.tables{
    margin-top:10px;
  }
}
</style>