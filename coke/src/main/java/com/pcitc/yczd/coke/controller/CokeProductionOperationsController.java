package com.pcitc.yczd.coke.controller;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.pcitc.yczd.coke.entity.PageencodeValueEntity;
import com.pcitc.yczd.coke.service.*;
import com.pcitc.yczd.coke.vo.*;
import com.pcitc.yczd.common.utils.ResponseUtil;
import com.pcitc.yczd.common.utils.TimeUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * @program: yczd-server
 * @description: productionOperations
 * @author: pengchong.li
 * @createTime: 2024-08-15 10:44
 **/
@CrossOrigin
@RestController
@RequestMapping("/coke")
@Api(
        value = "焦化装置",
        tags = "焦化装置",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE)
public class CokeProductionOperationsController {

    @Autowired
    private ICokeChartService iCokeChartService;
    @Autowired
    private ICokeDeviceService iCokeDeviceService;
    @Autowired
    private IExamineServiceCoke iExamineServiceCoke;
    @Autowired
    private ITstablerateCokeService iTstablerateCokeService;
    @Autowired
    private ICokeStableRatePicService iCokeStableRatePicService;
    @Autowired
    private ICokeStableRatePic2Service iCokeStableRatePic2Service;
    @Autowired
    private IPageencodeValueCokeService iPageencodeValueCokeService;
    private ObjectMapper objectMapper = new ObjectMapper();

    @ApiImplicitParams({
            @ApiImplicitParam(name = "starttime", value = "开始时间", paramType = "query", required = false, dataType =
                    "String"),
            @ApiImplicitParam(name = "endtime", value = "结束时间", paramType = "query", required = false, dataType =
                    "String"),
            @ApiImplicitParam(name = "pageencode", value = "页面编码", paramType = "query", required = true, dataType =
                    "String"),
            @ApiImplicitParam(name = "ismacd", value = "是否带红色平均线", paramType = "query", required = false, dataType =
                    "String"),
            @ApiImplicitParam(name = "ismacdset", value = "是否带红色设置线", paramType = "query", required = false,
                    dataType =
                            "String"),
            @ApiImplicitParam(name = "dtype", value = "瀑布图的类型", paramType = "query", required = false,
                    dataType =
                            "String"),
            @ApiImplicitParam(name = "timetype", value = "月、周、日的类型（month、week、day）", paramType = "query", required =
                    false,
                    dataType =
                            "String"),
            @ApiImplicitParam(name = "type", value = "考核项平稳率类型", paramType = "query", required = false,
                    dataType =
                            "String"),
            @ApiImplicitParam(name = "ParentValue", value = "父级装置代码", paramType = "query", required = false,
                    dataType =
                            "String"),
            @ApiImplicitParam(name = "isqs", value = "是否取数调用", paramType = "query", required = false,
                    dataType =
                            "String"),
            @ApiImplicitParam(name = "label", value = "跳转页面的展示", paramType = "query", required = false,
                    dataType =
                            "String")})
    @ApiOperation(value = "查询焦化图形相关数据")
    @GetMapping(value = "/GetCokeChartList")
    public Object GetcokeChartList(@RequestParam(required = false) String starttime,
                               @RequestParam(required = false) String endtime,
                               @RequestParam(required = false) String ismacd,
                               @RequestParam(required = false) String ismacdset,
                               @RequestParam(required = false) String dtype,
                               @RequestParam(required = false) String timetype,
                               @RequestParam(required = false) String type,
                               @RequestParam(required = false) String ParentValue,
                                   @RequestParam(required = false) String isqs,
                               @RequestParam(required = true) String pageencode,
                                   @RequestParam(required = false) String label
    ) throws Exception {

        if (StringUtils.isBlank(starttime)) {
            starttime = "";
        }
        if (StringUtils.isBlank(endtime)) {
            endtime = "";
        }
        if (StringUtils.isBlank(ismacd)) {
            ismacd = "";
        }
        if (StringUtils.isBlank(ismacdset)) {
            ismacdset = "";
        }
        if (StringUtils.isBlank(dtype)) {
            dtype = "";
        }
        if (StringUtils.isBlank(timetype)) {
            timetype = "";
        }
        if (StringUtils.isBlank(type)) {
            type = "";
        }
        if (StringUtils.isBlank(ParentValue)) {
            ParentValue = "";
        }
        if (StringUtils.isBlank(isqs)) {
            isqs = "";
        }

        Double dTime=1.5;
        Object chartlist = null;
        String[] groupcode = new String[]{};
        String[] charttype = new String[]{};
        String[] grouptype = new String[]{};
        String redlineset = "";
        String sorttype="asc";
        String issum = "0";
        Integer xsws = 0; //保留的小数位数
        if (pageencode.indexOf("Pop") != -1) {
            if (!("").equals(starttime) && !("").equals(endtime)){
                starttime= starttime+" 00:00:00";
                endtime=endtime+" 23:59:59";
            }else {
                starttime= TimeUtils.getDateAddDay0(10).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                endtime=TimeUtils.getDateAddDay0(0).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            }
            if (pageencode.equals("DensitySulfurPop")) {
                groupcode = new String[]{"Group1"};
                xsws = 0;
                dTime = 1.5;
            }
            if (pageencode.equals("OilCarbonResiduePop")) {
                groupcode = new String[]{"Group1"};
                xsws = 1;
                dTime = 1.5;
            }

            if (pageencode.equals("PHValue_1Pop")) {
                groupcode = new String[]{"Group1"};
                xsws = 1;
                dTime = 1.0;
            }
            if (pageencode.equals("PHValue_2Pop")) {
                groupcode = new String[]{"Group1"};
                xsws = 2;
                dTime = 1.0;
            }
            if (pageencode.equals("PHValue_3Pop")) {
                groupcode = new String[]{"Group1"};
                xsws = 1;
                dTime = 1.0;
            }
            if (pageencode.equals("SO2CO2Pop")) {
                groupcode = new String[]{"Group1"};
                xsws = 2;
                dTime = 1.0;
            }
            if (pageencode.equals("CirculationRatioPop")) {
                groupcode = new String[]{"Group1"};
                xsws = 2;
                dTime = 1.0;
            }
            if (pageencode.equals("FurnaceOutletTemperaturePop")) {
                groupcode = new String[]{"Group1"};
                xsws = 0;
                dTime = 1.0;
            }
            if (pageencode.equals("SteamInjectionPop")) {
                groupcode = new String[]{"Group1"};
                xsws = 2;
                dTime = 1.0;
            }
            if (pageencode.equals("CokeDrumPressurePop")) {
                groupcode = new String[]{"Group1"};
                xsws = 2;
                dTime = 1.0;
            }
            if (pageencode.equals("CokeTowerGasVelocityPop")) {
                groupcode = new String[]{"Group1"};
                xsws = 2;
                dTime = 1.0;
            }
            if (pageencode.equals("CokeFurnacePressurePop")) {
                groupcode = new String[]{"Group1"};
                xsws = 2;
                dTime = 1.0;
            }
            if (pageencode.equals("TowerBottomTowertopTemperaturePop")) {
                groupcode = new String[]{"Group1"};
                xsws = 0;
                dTime = 1.5;
            }
            if (pageencode.equals("WaxsulfurcokeSulfurPop")) {
                groupcode = new String[]{"Group1"};
                xsws = 2;
                dTime = 1.5;
            }
            if (pageencode.equals("waxnitrogencontentPop")) {
                groupcode = new String[]{"Group1"};
                xsws = 2;
                dTime = 1.5;
            }
            if (pageencode.equals("CokeRateCarbonResiduePop")) {
                groupcode = new String[]{"Group1"};
                xsws = 2;
                dTime = 1.5;
            }
            if (pageencode.equals("C3DryGasRatePop")) {
                groupcode = new String[]{"Group1"};
                xsws = 2;
                dTime = 1.5;
            }
            if (pageencode.equals("CokeWaterContentPop")) {
                groupcode = new String[]{"Group1"};
                xsws = 2;
                dTime = 1.5;
            }
            if (pageencode.equals("LoadFactorPop")) {
                groupcode = new String[]{"Group1"};
                xsws = 1;
                dTime = 1.0;
            }
            if (pageencode.equals("FurnaceThermalEfficiencyPop")) {
                groupcode = new String[]{"Group1"};
                xsws = 1;
                dTime = 1.0;
            }
            if (pageencode.equals("ExhaustGasDewPointTemperaturePop")) {
                groupcode = new String[]{"Group1"};
                xsws = 1;
                dTime = 1.0;
            }
            if (pageencode.equals("TotalLowtemperatureHeatPop")) {
                groupcode = new String[]{"Group1"};
                xsws = 0;
                dTime = 1.0;
            }
            if (pageencode.equals("HeatExtractionRatioFractionationPop")) {
                groupcode = new String[]{"Group1"};
                xsws = 2;
                dTime = 1.0;
            }
            if (pageencode.equals("FractionatingTowerFlashZoneTemperaturePop")) {
                groupcode = new String[]{"Group1"};
                xsws = 0;
                dTime = 1.0;
            }
            if (pageencode.equals("ConsumptionEfficiencyPop")) {
                groupcode = new String[]{"Group1"};
                xsws = 1;
                dTime = 1.5;
            }
            return iCokeChartService.GetCokePopChart(groupcode, charttype, pageencode, starttime, endtime, ismacd, ismacdset,
                    redlineset, xsws, ParentValue, sorttype,label);
        }else {
            if (pageencode.equals("DensitySulfur")) {
                groupcode = new String[]{"硫含量", "原料密度"};
                grouptype = new String[]{"Group2", "Group1"};
                charttype = new String[]{"Bar", "Line"};
                xsws = 0;
                dTime = 1.5;
            }
            if (pageencode.equals("OilCarbonResidue")) {
                groupcode = new String[]{"原料残炭", "100-生焦率"};
                grouptype = new String[]{"Group1", "Group2"};
                charttype = new String[]{"Bar", "Line"};
                //redlineset = "切水含油";
                xsws = 1;
                dTime = 1.5;
            }
            if (pageencode.equals("ProductDistributionAll")) {
                groupcode = new String[]{"焦炭", "蜡油", "柴油", "汽油", "液化气", "干气"};
                grouptype = new String[]{"jt", "ly", "cy", "qy", "yhq", "gq"};
                charttype = new String[]{"Bar", "Bar", "Bar", "Bar", "Bar", "Bar"};
                xsws = 1;
                dTime = 1.0;
                Object yczdpara = getPagecodeValue(pageencode);
                if (yczdpara != null && ("".equals(starttime) || "".equals(endtime))) {
                    if (isqs.equals("1")) {
                        return yczdpara;
                    } else {
                        return ResponseUtil.success(yczdpara);
                    }
                } else {
                    chartlist = iCokeChartService.getProductDistributionAllChart(groupcode, grouptype, charttype, pageencode,
                            starttime, endtime,
                            ismacd, ismacdset, redlineset, xsws, dTime);
                    if (isqs.equals("1")) {
                        return chartlist;
                    } else {
                        return ResponseUtil.success(chartlist);
                    }
                }
            }
            if (pageencode.equals("CokeRate")) {
                groupcode = new String[]{"优化值", "实际值"};
                grouptype = new String[]{"Group1", "Group2"};
                charttype = new String[]{"Line", "Line"};
                xsws = 0;
                dTime = 1.0;
                Object yczdpara = getPagecodeValue(pageencode);
                if (yczdpara != null && ("".equals(starttime) || "".equals(endtime))) {
                    if (isqs.equals("1")) {
                        return yczdpara;
                    } else {
                        return ResponseUtil.success(yczdpara);
                    }
                } else {
                    chartlist = iCokeChartService.getCokeRateChart(groupcode, charttype, pageencode, starttime, endtime,
                            ismacd, ismacdset, redlineset, xsws, dTime);
                    if (isqs.equals("1")) {
                        return chartlist;
                    } else {
                        return ResponseUtil.success(chartlist);
                    }
                }
            }
            if (pageencode.equals("PHValue_1")) {
                groupcode = new String[]{"氯离子含量"};
                grouptype = new String[]{"Group1"};
                charttype = new String[]{"Bar"};
                redlineset = "氯离子含量";
                xsws = 1;
                dTime = 1.0;
            }
            if (pageencode.equals("PHValue_2")) {
                groupcode = new String[]{"铁离子含量"};
                grouptype = new String[]{"Group1"};
                charttype = new String[]{"Bar"};
                redlineset = "铁离子含量";
                xsws = 2;
                dTime = 1.0;
            }
            if (pageencode.equals("PHValue_3")) {
                groupcode = new String[]{"pH值"};
                grouptype = new String[]{"Group1"};
                charttype = new String[]{"Bar"};
                redlineset = "pH值";
                xsws = 1;
                dTime = 1.0;
            }
            if (pageencode.equals("SO2CO2")) {
                groupcode = new String[]{"二氧化碳", "二氧化硫"};
                grouptype = new String[]{"Group1", "Group2"};
                charttype = new String[]{"Bar", "Bar"};
                xsws = 2;
                dTime = 1.0;
            }
            if (pageencode.equals("LowTemperatureCorrosion")) {
                groupcode = new String[]{"内件腐蚀速率", "塔壁腐蚀速率"};
                charttype = new String[]{"Bar","Bar"};
                xsws = 0;
                redlineset="腐蚀速率";
                return ResponseUtil.success(iCokeChartService.GetLowHitChart(groupcode, charttype, pageencode, starttime, endtime,
                        ismacd,
                        ismacdset,
                        redlineset, xsws, sorttype, issum,"low"));

            }
            if (pageencode.equals("HighTemperatureCorrosion")) {
                groupcode = new String[]{"内件腐蚀速率", "塔壁腐蚀速率"};
                charttype = new String[]{"Bar","Bar"};
                xsws = 0;
                return ResponseUtil.success(iCokeChartService.GetLowHitChart(groupcode, charttype, pageencode, starttime, endtime,
                        ismacd, ismacdset,
                        redlineset, xsws, sorttype, issum,"hig"));

            }
         /*if (pageencode.equals("LowTemperatureCorrosion")) {
            groupcode = new String[]{"内件腐蚀速率", "塔壁腐蚀速率"};
            charttype = new String[]{"Bar","Bar"};
            xsws = 1;
            dTime=1.0;
        }
        if (pageencode.equals("HighTemperatureCorrosion")) {
            groupcode = new String[]{"内件腐蚀速率", "塔壁腐蚀速率"};
            charttype = new String[]{"Bar","Bar"};
            xsws = 1;
            dTime=1.0;
        }
        if (pageencode.equals("WaterCorrosionRate_1")) {
            groupcode = new String[]{"水蒸气露点腐蚀速率"};
            charttype = new String[]{"Bar","Bar"};
            xsws = 1;
            dTime=1.0;
        }
        if (pageencode.equals("Cdu_Corrosion")) {
            groupcode = new String[]{"内件腐蚀速率", "塔壁腐蚀速率"};
            charttype = new String[]{"Bar","Bar"};
            xsws = 1;
            dTime=1.0;
        }*/
            if (pageencode.equals("WaterCorrosionRate_3")) {
                groupcode = new String[]{"顶循回流温度"};
                grouptype = new String[]{"Group1"};
                charttype = new String[]{"Bar"};
                redlineset = "顶循回流温度";
                xsws = 1;
                dTime = 1.0;
            }
            if (pageencode.equals("CokeFactor")) {
                groupcode = new String[]{"生焦率%", "原料残炭%"};
                grouptype = new String[]{"Group1", "Group2"};
                charttype = new String[]{"Line", "Line"};
                redlineset = "";
                xsws = 1;
                dTime = 1.0;
                Object yczdpara = getPagecodeValue(pageencode);
                if (yczdpara != null && ("".equals(starttime) || "".equals(endtime))) {
                    if (isqs.equals("1")) {
                        return yczdpara;
                    } else {
                        return ResponseUtil.success(yczdpara);
                    }
                } else {
                    chartlist = iCokeChartService.getCokeFactorChart(groupcode, grouptype, charttype, pageencode,
                            starttime, endtime,
                            ismacd, ismacdset, redlineset, xsws, dTime);
                    if (isqs.equals("1")) {
                        return chartlist;
                    } else {
                        return ResponseUtil.success(chartlist);
                    }
                }
            }
            if (pageencode.equals("Furnace")) {
                groupcode = new String[]{"加热炉"};
                grouptype = new String[]{"Group1"};
                charttype = new String[]{"Bar"};
                redlineset = "";
                xsws = 1;
                dTime = 1.0;
            }
            if (pageencode.equals("MediumResidenceTime")) {
                groupcode = new String[]{"加热炉"};
                grouptype = new String[]{"Group1"};
                charttype = new String[]{"Bar"};
                redlineset = "";
                xsws = 1;
                dTime = 1.0;
                return null;
            }
            if (pageencode.equals("CirculationRatio")) {
                groupcode = new String[]{"实际值"};
                grouptype = new String[]{"Group2"};
                charttype = new String[]{"Line"};
                redlineset = "";
                xsws = 2;
                dTime = 1.0;
            }
            if (pageencode.equals("FurnaceOutletTemperature")) {
                groupcode = new String[]{"实际值"};
                grouptype = new String[]{"Group2"};
                charttype = new String[]{"Bar"};
                redlineset = "";
                xsws = 0;
                dTime = 1.0;
            }
            if (pageencode.equals("CokeCycle")) {
                groupcode = new String[]{"生焦周期"};
                grouptype = new String[]{"Group1"};
                charttype = new String[]{"Bar"};
                redlineset = "";
                xsws = 0;
                dTime = 1.0;
            }
            if (pageencode.equals("SteamInjection")) {
                groupcode = new String[]{"注汽量"};
                grouptype = new String[]{"Group1"};
                charttype = new String[]{"Bar"};
                redlineset = "";
                xsws = 2;
                dTime = 1.0;
            }
            if (pageencode.equals("CokeDrumPressure")) {
                groupcode = new String[]{"焦炭生产塔压力"};
                grouptype = new String[]{"Group1"};
                charttype = new String[]{"Bar"};
                redlineset = "";
                xsws = 2;
                dTime = 1.0;
            }
            if (pageencode.equals("CokeTowerGasVelocity")) {
                groupcode = new String[]{"焦炭塔气速"};
                grouptype = new String[]{"Group1"};
                charttype = new String[]{"Bar"};
                redlineset = "";
                xsws = 2;
                dTime = 1.0;
            }
            if (pageencode.equals("CokeFurnacePressure")) {
                groupcode = new String[]{"实际值"};
                grouptype = new String[]{"Group1"};
                charttype = new String[]{"Line"};
                redlineset = "";
                xsws = 2;
                dTime = 1.0;
            }
            if (pageencode.equals("TowerBottomTowertopTemperature")) {
                groupcode = new String[]{"加热炉出口温度", "分馏塔底温度", "焦炭塔顶温度"};
                grouptype = new String[]{"Group1", "Group2", "Group3"};
                charttype = new String[]{"Bar", "Line", "Line"};
                redlineset = "";
                xsws = 0;
                dTime = 1.5;
            }
            if (pageencode.equals("WaxsulfurcokeSulfur")) {
                groupcode = new String[]{"原料硫含量", "蜡油硫含量", "石油焦硫含量"};
                grouptype = new String[]{"Group1", "Group2", "Group3"};
                charttype = new String[]{"Bar", "Line", "Line"};
                redlineset = "";
                xsws = 2;
                dTime = 1.5;
            }
            if (pageencode.equals("waxnitrogencontent")) {
                groupcode = new String[]{"蜡油氮含量"};
                grouptype = new String[]{"Group1"};
                charttype = new String[]{"Bar"};
                redlineset = "";
                xsws = 2;
                dTime = 1.5;
            }
            if (pageencode.equals("CokeCoefficientRadar")) {
                groupcode = new String[]{"生焦指数", "生焦系数"};
                grouptype = new String[]{"Group1", "Group2"};
                charttype = new String[]{"Line", "Line"};
                redlineset = "";
                xsws = 2;
                dTime = 1.5;
                Object yczdpara = getPagecodeValue(pageencode);
                if (yczdpara != null && ("".equals(starttime) || "".equals(endtime))) {
                    if (isqs.equals("1")) {
                        return yczdpara;
                    } else {
                        return ResponseUtil.success(yczdpara);
                    }
                } else {
                    chartlist = iCokeChartService.getCokeCoefficientRadarChart(groupcode, grouptype, charttype, pageencode,
                            starttime, endtime,
                            ismacd, ismacdset, redlineset, xsws, dTime);
                    if (isqs.equals("1")) {
                        return chartlist;
                    } else {
                        return ResponseUtil.success(chartlist);
                    }
                }
            }
            if (pageencode.equals("CokeRateCarbonResidue")) {
                groupcode = new String[]{"原料残炭", "石油焦产率"};
                grouptype = new String[]{"Group1", "Group2"};
                charttype = new String[]{"Bar", "Line"};
                redlineset = "";
                xsws = 2;
                dTime = 1.5;
            }
            if (pageencode.equals("C3DryGasRate")) {
                groupcode = new String[]{"干气中C3以上含量", "干气产率"};
                grouptype = new String[]{"Group1", "Group2"};
                charttype = new String[]{"Bar", "Line"};
                redlineset = "";
                xsws = 2;
                dTime = 1.5;
            }
            if (pageencode.equals("CokeWaterContent")) {
                groupcode = new String[]{"石油焦扣水比例"};
                grouptype = new String[]{"Group1"};
                charttype = new String[]{"Bar"};
                redlineset = "";
                xsws = 2;
                dTime = 1.5;
            }
            if (pageencode.equals("LoadFactor")) {
                groupcode = new String[]{"能耗", "负荷率"};
                grouptype = new String[]{"Group1", "Group2"};
                charttype = new String[]{"Bar", "Line"};
                redlineset = "";
                xsws = 1;
                dTime = 1.0;
            }
            if (pageencode.equals("FurnaceThermalEfficiency")) {
                groupcode = new String[]{"加热炉热效率"};
                grouptype = new String[]{"Group1"};
                charttype = new String[]{"Bar"};
                redlineset = "";
                xsws = 1;
                dTime = 1.0;
            }
            if (pageencode.equals("ExhaustGasDewPointTemperature")) {
                groupcode = new String[]{"排烟温度", "露点温度"};
                grouptype = new String[]{"Group1", "Group2"};
                charttype = new String[]{"Line", "Line"};
                redlineset = "";
                xsws = 1;
                dTime = 1.0;
            }
            if (pageencode.equals("TotalLowtemperatureHeat")) {
                groupcode = new String[]{"低温热利用"};
                grouptype = new String[]{"Group1"};
                charttype = new String[]{"Bar"};
                redlineset = "";
                xsws = 0;
                dTime = 1.0;
            }
            if (pageencode.equals("HeatExtractionRatioFractionation")) {
                groupcode = new String[]{"蜡油", "中段", "柴油", "顶循"};
                grouptype = new String[]{"Group4", "Group3", "Group2", "Group1"};
                charttype = new String[]{"Bar", "Bar", "Bar", "Bar"};
                redlineset = "";
                xsws = 2;
                dTime = 1.0;
            }
            if (pageencode.equals("FractionatingTowerFlashZoneTemperature")) {
                groupcode = new String[]{"分馏塔闪蒸段温度"};
                grouptype = new String[]{"Group1"};
                charttype = new String[]{"Bar"};
                redlineset = "";
                xsws = 0;
                dTime = 1.0;
            }
            if (pageencode.equals("ConsumptionEfficiency")) {
                groupcode = new String[]{"能耗", "加热炉热效率"};
                grouptype = new String[]{"Group1", "Group2"};
                charttype = new String[]{"Bar", "Line"};
                redlineset = "";
                xsws = 1;
                dTime = 1.5;
            }
            if (pageencode.equals("DcuManufactureLoadfactor")) {
                groupcode = new String[]{"加工量", "加工能力", "负荷率"};
                grouptype = new String[]{"Group1", "Group2", "Group3"};
                charttype = new String[]{"Bar", "Bar", "Line"};
                redlineset = "";
                xsws = 2;
                dTime = 1.0;
            }
            if (pageencode.equals("ExamineConfig")) {

                groupcode = new String[]{"非计划停工次数", "能耗/10+损失", "原料密度×总液收", "石油焦/残炭", "加热炉热效率", "干气C3+含量"};
                charttype = new String[]{"Bar", "Bar", "Bar", "Bar", "Bar", "Bar", "Bar"};
                xsws = 0;
                Object yczdpara = getPagecodeValue(pageencode);
                if (yczdpara != null && ("".equals(starttime) || "".equals(endtime))) {
                    if (isqs.equals("1")) {
                        return yczdpara;
                    } else {
                        return ResponseUtil.success(yczdpara);
                    }
                } else {
                    if ("".equals(starttime) && "".equals(endtime)) {
                        starttime = DateTime.now().toString("yyyy-01-01 0:00:00");
                        endtime = DateTime.now().toString("yyyy-MM-dd HH:mm:ss");
                    }
                    chartlist = iCokeChartService.GetCokeExamineConfigChart(groupcode, charttype, pageencode, starttime,
                            endtime, ismacd, ismacdset,
                            redlineset, xsws);
                    if (isqs.equals("1")) {
                        return chartlist;
                    } else {
                        return ResponseUtil.success(chartlist);
                    }
                }
            }
            if (pageencode.equals("GanttChart")) {
                groupcode = new String[]{"开工", "停工及检修"};
                charttype = new String[]{"Bar", "Bar"};
                xsws = 0;
                Object yczdpara = getPagecodeValue(pageencode);
                if (yczdpara != null && ("".equals(starttime) || "".equals(endtime))) {
                    if (isqs.equals("1")) {
                        return yczdpara;
                    } else {
                        return ResponseUtil.success(yczdpara);
                    }
                } else {
                    chartlist = iCokeChartService.GetCokeGanttChartList(groupcode, charttype, pageencode, starttime,
                            endtime, ismacd, ismacdset,
                            redlineset, xsws, dtype);
                    if (isqs.equals("1")) {
                        return chartlist;
                    } else {
                        return ResponseUtil.success(chartlist);
                    }
                }
            }
            if (pageencode.equals("GantWaterFall")) {
                pageencode = dtype;
                groupcode = new String[]{"减少", "增加"};
                charttype = new String[]{"Bar", "Bar"};
                xsws = 0;
                Object yczdpara = getPagecodeValue(pageencode);
                if (yczdpara != null && ("".equals(starttime) || "".equals(endtime))) {
                    if (isqs.equals("1")) {
                        return yczdpara;
                    } else {
                        return ResponseUtil.success(yczdpara);
                    }
                } else {
                    chartlist = iCokeChartService.GetCokeGantWaterFallChart(groupcode, charttype, pageencode, starttime,
                            endtime, ismacd, ismacdset,
                            redlineset, xsws, dtype);
                    if (isqs.equals("1")) {
                        return chartlist;
                    } else {
                        return ResponseUtil.success(chartlist);
                    }
                }
            }
            if (pageencode.equals("BubblrConfig")) {
                groupcode = new String[]{"液收"};
                charttype = new String[]{"Scatter"};
                xsws = 1;
                Object yczdpara = getPagecodeValue(pageencode);
                if (yczdpara != null && ("".equals(starttime) || "".equals(endtime))) {
                    if (isqs.equals("1")) {
                        return yczdpara;
                    } else {
                        return ResponseUtil.success(yczdpara);
                    }
                } else {
                    chartlist = iCokeChartService.GetCokeBubblrConfigChart(groupcode, charttype, pageencode, starttime,
                            endtime,
                            ismacd, ismacdset,
                            redlineset, xsws);
                    if (isqs.equals("1")) {
                        return chartlist;
                    } else {
                        return ResponseUtil.success(chartlist);
                    }
                }
            }
            if (pageencode.equals("CokeStableRatecduPic")) {
                groupcode = new String[]{"装置平稳率"};
                charttype = new String[]{"Bar"};
                xsws = 2;
                String zztype = "coke";
                sorttype="desc";
                Object yczdpara = getPagecodeValue(pageencode);
                if (yczdpara != null && ("".equals(starttime) || "".equals(endtime))) {
                    if (isqs.equals("1")) {
                        return yczdpara;
                    } else {
                        return ResponseUtil.success(yczdpara);
                    }
                } else {
                    chartlist = iCokeChartService.GetCokeStableRatePicChart(groupcode, charttype, pageencode, starttime,
                            endtime, ismacd, ismacdset,
                            redlineset, xsws, timetype, type, zztype,sorttype);
                    if (isqs.equals("1")) {
                        return chartlist;
                    } else {
                        return ResponseUtil.success(chartlist);
                    }
                }
            }
            if (pageencode.equals("CokeStableRatecduPic2")) {
                /*groupcode = new String[]{"装置平稳率-'" + iCokeStableRatePic2Service.GetDesc(type) + "'"};*/
                groupcode = new String[]{iCokeStableRatePic2Service.GetDesc(type)};
                charttype = new String[]{"Bar"};
                xsws = 2;
                String zztype = "coke";
                sorttype="desc";
                Object yczdpara = getPagecodeValue(pageencode);
                if (yczdpara != null && ("".equals(starttime) || "".equals(endtime))) {
                    if (isqs.equals("1")) {
                        return yczdpara;
                    } else {
                        return ResponseUtil.success(yczdpara);
                    }
                } else {
                    chartlist = iCokeChartService.GetCokeStableRatePic2Chart(groupcode, charttype, pageencode, starttime,
                            endtime, ismacd, ismacdset,
                            redlineset, xsws, timetype, type, zztype,sorttype);
                    if (isqs.equals("1")) {
                        return chartlist;
                    } else {
                        return ResponseUtil.success(chartlist);
                    }
                }
            }
            Object yczdpara = getPagecodeValue(pageencode);
            if (yczdpara != null && ("".equals(starttime) || "".equals(endtime))) {
                if (isqs.equals("1")) {
                    return yczdpara;
                } else {
                    return ResponseUtil.success(yczdpara);
                }
            } else {
                chartlist = iCokeChartService.getCokePointPramsCharttime(groupcode, grouptype, charttype, pageencode,
                        starttime, endtime,
                        ismacd, ismacdset, redlineset, xsws, dTime, sorttype);
                //不走redis 直接获取图形数据
        /*//测试保存功能
        device_attributeservice.SaveDevice_AttributeNew();*/
                if (isqs.equals("1")) {
                    return chartlist;
                } else {
                    return ResponseUtil.success(chartlist);
                }
            }
        }
    }

    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageencode", value = "页面编码", paramType = "query", required = true, dataType =
                    "String")})
    @ApiOperation(value = "查询焦化运行参数一览表、操作条件一览表、质量数据一览表的数据")
    @GetMapping(value = "/getCokeDeviceList")
    public Object getCokeDeviceList(@RequestParam(required = true) String pageencode) {

        if (StringUtils.isBlank(pageencode)) {
            pageencode = "";
        }
        if (pageencode.equals("装置运行参数一览表")) {
            return ResponseUtil.success(iCokeDeviceService.getCokeDevice1List());
        }
        if (pageencode.equals("操作条件一览表")) {
            return ResponseUtil.success(iCokeDeviceService.getCokeDevice2List());
        }
        if (pageencode.equals("质量数据一览表")) {
            return ResponseUtil.success(iCokeDeviceService.getCokeDevice3List());
        }
        return null;
    }
    @CrossOrigin
    @ApiOperation(value = "装置竞赛排名--装置竞赛排名-得分表格")
    @GetMapping(value = "/getCokeExamineSocreChart")
    public Object getCokeExamineSocreChart(@RequestParam(required = false) String isqs) throws JsonProcessingException {
        List<ExamineSocraparamCokeVo> list=new ArrayList<>();
        Object yczdpara = getPagecodeValue("ExamineConfigTable");
        if (yczdpara != null) {
            if (isqs!=null && isqs.equals("1")) {
                return yczdpara;
            } else {
                return ResponseUtil.success(yczdpara);
            }
        } else {
            list = iExamineServiceCoke.getExamineExamineConfiglist();
            //不走redis 直接获取图形数据
            if (isqs!=null && isqs.equals("1")) {
                return list;
            } else {
                return ResponseUtil.success(list);
            }
        }
    }
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageencode", value = "页面编码", paramType = "query", required = true, dataType =
                    "String")})
    @CrossOrigin
    @ApiOperation(value = "查询装置平稳率一览表")
    @GetMapping(value = "/getCokeStableRateTableList")
    public Object getCokeStableRateTableList(@RequestParam(required = true) String pageencode) {
        List<TstablerateVO> volist=new ArrayList<>();
        String zztype="";
        if(pageencode.equals("cdu")){
            zztype="coke";
            volist = iTstablerateCokeService.getCataStableRateTableList(zztype);

        }

        //return ApiResponse.succeed(volist);
        return ResponseUtil.success(volist);
    }
    public Object getPagecodeValue(String pageencode) throws JsonProcessingException {
        PageencodeValueEntity vo1 = new PageencodeValueEntity();
        vo1.setPageencode(pageencode);
        vo1.setTs(DateTime.now().toString("yyyy-MM-dd"));

        List<PageencodeValueEntity> list = iPageencodeValueCokeService.getPageencodeValue(vo1);
        Object yczdparamnewvo = null;
        String starttime = DateTime.now().toString("yyyy-MM-dd 0:00:00");String endtime = DateTime.now().toString("yyyy-MM-dd HH:mm:ss");
        if (list.size() > 0) {
            if(list.get(0).getPageencode().equals("GanttChart")){
                yczdparamnewvo = objectMapper.readValue(list.get(0).getContent(), GantWaterVO.class);
            }else if(list.get(0).getPageencode().equals("GantWaterFall")){
                yczdparamnewvo = objectMapper.readValue(list.get(0).getContent(), yczdParamGantWaterVo.class);
                ((yczdParamGantWaterVo)yczdparamnewvo).setQuerytime("数据更新时间:" + starttime + " 至 " + endtime);
            }else if(list.get(0).getPageencode().equals("BubblrConfig")){
                yczdparamnewvo = objectMapper.readValue(list.get(0).getContent(), AbnormalParamVO.class);
                ((AbnormalParamVO)yczdparamnewvo).setQuerytime("数据更新时间:" + starttime + " 至 " + endtime);
            }else if(list.get(0).getPageencode().equals("ExamineConfigTable")){
                yczdparamnewvo = objectMapper.readValue(list.get(0).getContent(), new TypeReference<List< ExamineSocraparamCokeVo >>(){});
            }
            else {
                yczdparamnewvo = objectMapper.readValue(list.get(0).getContent(), yczdParamNewVo.class);
                ((yczdParamNewVo)yczdparamnewvo).setQuerytime("数据更新时间:" + starttime + " 至 " + endtime);
            }
            /*yczdparamnewvo = objectMapper.readValue(list.get(0).getContent(), yczdParamNewVo.class);*/
        }
        return yczdparamnewvo;
    }
}
